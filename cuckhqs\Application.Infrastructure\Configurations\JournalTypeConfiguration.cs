﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class JournalTypeConfiguration : IEntityTypeConfiguration<JournalTypeEntity>
    {
        public void Configure(EntityTypeBuilder<JournalTypeEntity> builder)
        {
            builder.ToTable("JournalType");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.JournalTypeCode).HasColumnName("JournalTypeCode");
            builder.Property(x => x.JournalTypeName).HasColumnName("JournalTypeName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
