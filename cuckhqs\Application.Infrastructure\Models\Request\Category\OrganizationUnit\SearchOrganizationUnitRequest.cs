﻿using Application.Infrastructure.Commons;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.OrganizationUnit
{
    public class SearchOrganizationUnitRequest : BaseCriteria
    {
        public int? ParentId { get; set; }
        public string? keyword { get; set; }
        public string? IsActive { get; set; }
    }
}
