﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class DecisionLevelConfiguration : IEntityTypeConfiguration<DecisionLevelEntity>
    {
        public void Configure(EntityTypeBuilder<DecisionLevelEntity> builder)
        {
            builder.ToTable("DecisionLevel");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.DecisionLevelCode).HasColumnName("DecisionLevelCode");
            builder.Property(x => x.DecisionLevelName).HasColumnName("DecisionLevelName");
            builder.Property(x => x.Class).HasColumnName("Class");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
