﻿using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Models.Response;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Application.Infrastructure.Services.Implementations.FileService;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IFileService
    {
        Task<FileUploadResult> UploadFileAsync(IFormFile file, IWebHostEnvironment env);
        Task<FileDownloadResult> DownloadFileAsync(string filename, IWebHostEnvironment env);
    }
}
