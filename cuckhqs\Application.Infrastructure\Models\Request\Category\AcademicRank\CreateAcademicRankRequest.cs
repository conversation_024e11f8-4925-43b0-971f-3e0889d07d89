﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.AcademicRank
{
    public class CreateAcademicRankRequest
    {
        public string? AcademicRankCode { get; set; }
        public string? AcademicRankName { get; set; }
        public string? AcademicRankShortName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateAcademicRankRequest, AcademicRankEntity>> Expression
        {
            get
            {
                return entity => new AcademicRankEntity
                {
                    AcademicRankCode = entity.AcademicRankCode,
                    AcademicRankName = entity.AcademicRankName,
                    AcademicRankShortName = entity.AcademicRankShortName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static AcademicRankEntity Create(CreateAcademicRankRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
