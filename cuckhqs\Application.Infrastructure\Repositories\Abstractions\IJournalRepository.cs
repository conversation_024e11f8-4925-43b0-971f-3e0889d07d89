﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions
{
    public interface IJournalRepository : IRepository<JournalEntity, int>
    {
        Task<List<JournalTypeEntity>> GetAllJournalTypeAsync();
        Task<List<JournalGroupEntity>> GetAllJournalGroupAsync();
    }
}
