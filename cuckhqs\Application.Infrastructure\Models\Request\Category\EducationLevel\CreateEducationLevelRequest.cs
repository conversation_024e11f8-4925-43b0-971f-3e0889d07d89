﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.EducationLevel
{
    public class CreateEducationLevelRequest
    {
        public string? EducationLevelCode { get; set; }
        public string? EducationLevelName { get; set; }
        public int? Class { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateEducationLevelRequest, EducationLevelEntity>> Expression
        {
            get
            {
                return entity => new EducationLevelEntity
                {
                    EducationLevelCode = entity.EducationLevelCode,
                    EducationLevelName = entity.EducationLevelName,
                    Class = entity.Class,
                    Active = entity.Active,
                    Year = DateTime.Now.Year,
                };
            }
        }

        public static EducationLevelEntity Create(CreateEducationLevelRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
