﻿using Application.Infrastructure.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Configurations
{
    public class WorkingScheduleResultConfiguration : IEntityTypeConfiguration<WorkingScheduleResultEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleResultEntity> builder)
        {
            builder.ToTable("WorkingScheduleResult");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.WorkingScheduleId).HasColumnName("WorkingScheduleId");
            builder.Property(x => x.Date).HasColumnName("Date");
            builder.Property(x => x.Result).HasColumnName("Result");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");

            builder.HasOne(x => x.WorkingSchedule)
                .WithMany(ws => ws.WorkingScheduleResult)
                .HasForeignKey(x => x.WorkingScheduleId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(x => x.WorkingScheduleResult_AttachDetail)
                   .WithOne(ws => ws.WorkingScheduleResult)
                   .HasForeignKey(ws => ws.WorkingScheduleResultId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
