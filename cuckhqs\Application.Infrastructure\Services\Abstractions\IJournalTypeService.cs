﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.JournalType;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IJournalTypeService
    {
        Task<BaseSearchResponse<JournalTypeResponse>> SearchJournalTypeAsync(SearchJournalTypeRequest request);
        Task<JournalTypeResponse> CreateJournalTypeAsync(CreateJournalTypeRequest request);
        Task<bool> UpdateJournalTypeAsync(UpdateJournalTypeRequest request);
        Task<bool> DeleteJournalTypeAsync(DeleteJournalTypeRequest request);
    }
}
