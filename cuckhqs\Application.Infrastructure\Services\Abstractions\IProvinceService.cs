﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Province;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IProvinceService
    {
        Task<BaseSearchResponse<ProvinceResponse>> SearchProvinceAsync(SearchProvinceRequest request);
        Task<ProvinceResponse> CreateProvinceAsync(CreateProvinceRequest request);
        Task<bool> UpdateProvinceAsync(UpdateProvinceRequest request);
        Task<string> DeleteProvinceAsync(DeleteProvinceRequest request);
    }
}
