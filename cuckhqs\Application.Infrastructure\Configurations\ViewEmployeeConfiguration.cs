﻿using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Configurations
{
    public class ViewEmployeeConfiguration : IEntityTypeConfiguration<EmployeeEntity>
    {
        public void Configure(EntityTypeBuilder<EmployeeEntity> builder)
        {
            builder.ToView("Employee");

            builder.Property(x => x.Id).HasColumnName("Id");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
            builder.Property(x => x.OrganizationUnitName).HasColumnName("OrganizationUnitName");
            builder.Property(x => x.EmployeeCode).HasColumnName("EmployeeCode");
            builder.Property(x => x.FullName).HasColumnName("Fullname");
            builder.Property(x => x.RankId).HasColumnName("RankId");
            builder.Property(x => x.Gender).HasColumnName("Gender");
            builder.Property(x => x.AcademicRankId).HasColumnName("AcademicRankId");
            builder.Property(x => x.YearOfAcademicRank).HasColumnName("YearOfAcademicRank");
            builder.Property(x => x.DegreeId).HasColumnName("DegreeId");
            builder.Property(x => x.YearOfDegree).HasColumnName("YearOfDegree");
            builder.Property(x => x.PositionId).HasColumnName("PositionId");
            builder.Property(x => x.PositionType).HasColumnName("PositionType");
            builder.Property(x => x.PositionName).HasColumnName("PositionName");
            builder.Property(x => x.PartyPositionId).HasColumnName("PartyPositionId");
            builder.Property(x => x.BirthDay).HasColumnName("BirthDay");
            builder.Property(x => x.Owned).HasColumnName("Owned");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.ActiveAccount).HasColumnName("ActiveAccount");
         //   builder.Property(x => x.IsAdministrator).HasColumnName("IsAdministrator");
            builder.Property(x => x.RankName).HasColumnName("RankName");
        }
    }
}
