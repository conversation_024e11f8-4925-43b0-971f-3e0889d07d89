﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Journal
{
    public class CreateJournalRequest
    {
        public string? JournalCode { get; set; }
        public string? JournalName { get; set; }
        public string? ISSN { get; set; }
        public int? JournalTypeId { get; set; }
        public string? JournalTypeCode{ get; set; }
        public string? JournalTypeId_AN { get; set; }
        public int? JournalGroupId { get; set; }
        public string? JournalGroupCode { get; set; }
        public string? JournalGroupId_AN { get; set; }
        public string? PublishingAgency { get; set; }
        public decimal? PointFrom { get; set; }
        public decimal? PointTo { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateJournalRequest, JournalEntity>> Expression
        {
            get
            {
                return entity => new JournalEntity
                {
                    JournalCode = entity.JournalCode,
                    JournalName = entity.JournalName,
                    ISSN = entity.ISSN,
                    JournalTypeId = entity.JournalTypeId,
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeId_AN = entity.JournalTypeId_AN,
                    JournalGroupId = entity.JournalGroupId,
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupId_AN = entity.JournalGroupId_AN,
                    PublishingAgency = entity.PublishingAgency,
                    PointFrom = entity.PointFrom,
                    PointTo = entity.PointTo,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalEntity Create(CreateJournalRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
