﻿using Application.Infrastructure.Models.Request.Category.JournalType;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class JournalTypeController : ControllerBase
    {
        private readonly IJournalTypeService _journalTypeService;

        public JournalTypeController(IJournalTypeService journalTypeService)
        {
            _journalTypeService = journalTypeService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchJournalTypeAsync([FromBody] SearchJournalTypeRequest request)
        {
            try
            {
                var response = await _journalTypeService.SearchJournalTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateJournalTypeAsync([FromBody] CreateJournalTypeRequest request)
        {
            var response = await _journalTypeService.CreateJournalTypeAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateJournalTypeAsync([FromBody] UpdateJournalTypeRequest request)
        {
            try
            {
                var response = await _journalTypeService.UpdateJournalTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteJournalTypeAsync([FromBody] DeleteJournalTypeRequest request)
        {
            try
            {
                var response = await _journalTypeService.DeleteJournalTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
