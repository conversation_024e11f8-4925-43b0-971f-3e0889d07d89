﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Degree
{
    public class CreateDegreeRequest
    {
        public string? DegreeCode { get; set; }
        public string? DegreeName { get; set; }
        public string? DegreeShortName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateDegreeRequest, DegreeEntity>> Expression
        {
            get
            {
                return entity => new DegreeEntity
                {
                    DegreeCode = entity.DegreeCode,
                    DegreeName = entity.DegreeName,
                    DegreeShortName = entity.DegreeShortName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static DegreeEntity Create(CreateDegreeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}

