﻿using System.Globalization;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Request.WorkingSchedule;
using Application.Infrastructure.Services;
using Application.Infrastructure.Services.Abstractions;
using Azure.Core;
using BoldReports.Writer;
using DocumentFormat.OpenXml.Spreadsheet;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using OpenIddict.Validation.AspNetCore;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using Application.Infrastructure.Exceptions;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class WorkingScheduleController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IWorkingScheduleService _workingScheduleService;
        private readonly string _filePath;

        public WorkingScheduleController(IWorkingScheduleService WorkingScheduleService, IWebHostEnvironment webHostEnvironment)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            _workingScheduleService = WorkingScheduleService;
            _webHostEnvironment = webHostEnvironment;
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateWorkingSchedule([FromBody] CreateWorkingScheduleRequest request)
        {
            try
            {
                var response = await _workingScheduleService.CreateWorkingScheduleAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Search")]
        public async Task<IActionResult> SearchProcurementSourcesAsync([FromBody] SearchWorkingScheduleRequest request)
        {
            try
            {
                var response = await _workingScheduleService.SearchWorkingScheduleAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }


        [HttpGet("GetWorkingScheduleById")]
        public async Task<IActionResult> GetWorkingScheduleById([FromQuery] int Id)
        {
            var response = await _workingScheduleService.GetWorkingScheduleById(Id);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateAsync([FromBody] UpdateWorkingScheduleRequest request)
        {
            var response = await _workingScheduleService.UpdateWorkingScheduleAsync(request);
            return Ok(response);
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteWorkingScheduleAsync([FromBody] DeleteWorkingScheduleRequest request)
        {
            try
            {
                var response = await _workingScheduleService.DeleteWorkingSchedulesAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("get-place")]
        public async Task<IActionResult> GetPlaceAsync()
        {
            try
            {
                var response = await _workingScheduleService.GetPlaceAsync();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("download-word")]
        public async Task<IActionResult> DownloadWordSchedule([FromBody] SearchWorkingScheduleRequest request)
        {
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/Templates", Uri.UnescapeDataString("WorkingScheduleTemplate.docx"));
            using (MemoryStream memoryStream = await _workingScheduleService.CreateScheduleWordFile(templatePath, request))
            {
                return File(memoryStream.ToArray(), "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "Schedule.docx");
            }
        }

        [HttpPost("export-pdf")]
        public async Task<IActionResult> ExportPdf([FromBody] SearchWorkingScheduleRequest request)
        {
            try
            {
                string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/Templates", Uri.UnescapeDataString("WorkingScheduleTemplate.docx"));
                using (MemoryStream docxStream = await _workingScheduleService.CreateScheduleWordFile(templatePath, request))
                {
                    using (MemoryStream pdfStream = new MemoryStream())
                    {
                        Aspose.Words.Document doc = new Aspose.Words.Document(docxStream);

                        doc.Save(pdfStream, Aspose.Words.SaveFormat.Pdf);

                        return File(pdfStream.ToArray(), "application/pdf", "Schedule.pdf");
                    }
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi khi tạo báo cáo PDF: {ex.Message}");
            }
        }
        [HttpPost("search-announced")]
        public async Task<IActionResult> SearchWorkingScheduleAnnouncedAsync([FromBody] SearchWorkingScheduleAnnouncedRequest request)
        {
            try
            {
                var response = await _workingScheduleService.SearchWorkingScheduleAnnouncedAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost("create-announced")]
        public async Task<IActionResult> CreateWorkingScheduleAnnouncedAsync([FromBody] WorkingScheduleIssueAnnouncedRequest request)
        {
            try
            {
                var response = await _workingScheduleService.WorkingScheduleIssueAnnouncedAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost("check-duplicate")]
        public async Task<IActionResult> CheckDuplicateSchedule([FromBody] CreateWorkingScheduleRequest request)
        {
            try
            {
                var response = await _workingScheduleService.CheckDuplicateSchedule(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
    }
}