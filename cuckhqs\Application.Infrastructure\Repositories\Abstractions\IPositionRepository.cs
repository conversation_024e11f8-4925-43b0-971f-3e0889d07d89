﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit
{
    public interface IPositionRepository : IRepository<PositionEntity, int>
    {
        // Task<OrganizationUnitEntity> GetBureauChiefUnitAsync();
        Task<List<PositionEntity>> GetAllPositionAsync(int type);
    }
}
