﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.Rank;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Province
{
    public class UpdateProvinceRequest : CreateProvinceRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateProvinceRequest, ProvinceEntity>> Expression
        {
            get
            {
                return entity => new ProvinceEntity
                {
                    Id = entity.Id,
                    ProvinceCode = entity.ProvinceCode,
                    ProvinceName = entity.ProvinceName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static ProvinceEntity Create(UpdateProvinceRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
