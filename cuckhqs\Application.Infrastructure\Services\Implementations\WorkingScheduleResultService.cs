﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.WorkingSchedule;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Response.Category;
using Microsoft.EntityFrameworkCore;
using DocumentFormat.OpenXml.EMMA;
using Application.Infrastructure.Exceptions;
using DocumentFormat.OpenXml.Vml.Office;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System.Globalization;

namespace Application.Infrastructure.Services.Implementations
{
    public class WorkingScheduleResultService : IWorkingScheduleResultService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AppDbContext _dbContext;
        private readonly IServiceProvider _serviceProvider;
        private readonly IEmployeeCService _employeeService;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public WorkingScheduleResultService(IUnitOfWork unitOfWork, AppDbContext dbContext, IServiceProvider serviceProvider, IEmployeeCService employeeService)
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
            _serviceProvider = serviceProvider;
            _employeeService = employeeService;
        }


        public async Task<bool> CreateWorkingScheduleResultAsync(CreateWorkingScheduleResultRequest request)
        {
            try
            {
                if (request == null || request.WorkingScheduleId == null)
                    throw new ArgumentException("Request or WorkingScheduleId cannot be null.");

                if (!DateTime.TryParseExact(request.Date, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    throw new ArgumentException("Invalid date format. Expected dd/MM/yyyy.");

                // Kiểm tra tồn tại bản ghi
                var existingResult = await _unitOfWork.WorkingScheduleResult
                    .AsQueryable()
                    .Include(x => x.WorkingScheduleResult_AttachDetail)
                    .FirstOrDefaultAsync(x => x.WorkingScheduleId == request.WorkingScheduleId);

                bool isUpdate = existingResult != null;
                WorkingScheduleResultEntity entity;

                if (isUpdate)
                {
                    entity = existingResult;
                    entity.Date = parsedDate;
                    entity.Result = request.Result;
                    entity.ModifiedDate = DateTime.UtcNow;
                    entity.Active = true;

                    // Xóa toàn bộ file vật lý + bản ghi file đính kèm
                    if (entity.WorkingScheduleResult_AttachDetail != null && entity.WorkingScheduleResult_AttachDetail.Any())
                    {
                        //foreach (var file in entity.WorkingScheduleResult_AttachDetail)
                        //{
                        //    // Đường dẫn thư mục chứa file (wwwroot/uploads)
                        //    var uploadFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Uploads");

                        //    // Đường dẫn đến file cụ thể
                        //    var filePath = Path.Combine(uploadFolder, file.FileName + "_" + file.Contents + file.FileType ?? "");

                        //    if (System.IO.File.Exists(filePath))
                        //    {
                        //        try
                        //        {
                        //            System.IO.File.Delete(filePath);
                        //        }
                        //        catch (Exception fileEx)
                        //        {
                        //            log.Warn($"Không thể xóa file vật lý: {filePath}. Lỗi: {fileEx.Message}");
                        //        }
                        //    }
                        //}

                        _dbContext.WorkingScheduleResult_AttachDetail.RemoveRange(entity.WorkingScheduleResult_AttachDetail);
                        entity.WorkingScheduleResult_AttachDetail.Clear();
                    }
                }
                else
                {
                    entity = new WorkingScheduleResultEntity
                    {
                        WorkingScheduleId = request.WorkingScheduleId,
                        Date = parsedDate,
                        Result = request.Result,
                        Active = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        WorkingScheduleResult_AttachDetail = new List<WorkingScheduleResult_AttachDetailEntity>()
                    };

                    _dbContext.WorkingScheduleResult.Add(entity);
                }

                // Lưu entity để lấy Id nếu là tạo mới
                await _dbContext.SaveChangesAsync();

                // Nếu có file mới, thêm vào DB
                if (request.Files != null && request.Files.Any())
                {
                    var attachDetails = request.Files.Select((file, index) => new WorkingScheduleResult_AttachDetailEntity
                    {
                        WorkingScheduleResultId = entity.Id,
                        Contents = file.Contents,
                        FileName = file.FileName,
                        FileType = file.FileType,
                        FileSize = file.FileSize,
                        Inactive = false,
                        SortOrder = index + 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow
                    }).ToList();

                    _dbContext.WorkingScheduleResult_AttachDetail.AddRange(attachDetails);
                }

                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error($"Lỗi tại CreateWorkingScheduleResultAsync: {ex}");
                throw new Exception($"UNKNOWN_ERROR: {ex.Message}", ex);
            }
        }






        //public async Task<bool> UpdateWorkingScheduleResultAsync(CreateWorkingScheduleResultRequest request, int Id)
        //{
        //    try
        //    {
        //        var entity = await _unitOfWork.WorkingScheduleResult
        //            .AsQueryable()
        //            .Include(s => s.WorkingScheduleResult_AttachDetail)
        //            .FirstOrDefaultAsync(s => s.Id == Id);

        //        if (entity == null)
        //        {
        //            throw new Exception($"Không tìm thấy WorkingScheduleResult với Id = {Id}");
        //        }
        //        entity.WorkingScheduleId = request.WorkingScheduleId;
        //        entity.Date = Convert.ToDateTime(request.Date); 
        //        entity.Result = request.Result;
        //        if (request.FileName != null && request.FileType != null && request.FileSize != null)
        //        {

        //            var attachDetail = entity.WorkingScheduleResult_AttachDetail?.FirstOrDefault();
        //            if (attachDetail != null)
        //            {
        //                attachDetail.FileName = request.FileName;
        //                attachDetail.FileType = request.FileType;
        //                attachDetail.FileSize = request.FileSize.Value; 
        //                attachDetail.ModifiedDate = DateTime.UtcNow;
        //                attachDetail.Inactive = true;
        //            }
        //            else
        //            {
        //                var newAttachDetail = new WorkingScheduleResult_AttachDetailEntity
        //                {
        //                    WorkingScheduleResultId = entity.Id,
        //                    FileName = request.FileName,
        //                    FileType = request.FileType,
        //                    FileSize = request.FileSize.Value,
        //                    CreatedDate = DateTime.UtcNow,
        //                    ModifiedDate = DateTime.UtcNow
        //                };
        //                entity.WorkingScheduleResult_AttachDetail = new List<WorkingScheduleResult_AttachDetailEntity> { newAttachDetail };
        //            }
        //        }
        //        _unitOfWork.WorkingScheduleResult.UpdateAsync(Id, entity);
        //        await _unitOfWork.SaveChangesAsync();

        //        return true; 
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error("Lỗi tại UpdateWorkingScheduleAsync: " + ex.ToString());
        //        throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
        //    }
        //}




        public async Task<List<WorkingScheduleResultResponse_Search>> GetWorkingScheduleResultByWorkingScheduleId(int WorkingScheduleId)
        {
            try
            {
                var workingScheduleResults = await _unitOfWork.WorkingScheduleResult
                                                    .AsQueryable()
                                                    .AsNoTracking()
                                                    .Where(s => s.WorkingScheduleId == WorkingScheduleId)
                                                    .Include(s => s.WorkingScheduleResult_AttachDetail)
                                                    .ToListAsync();

                var result = workingScheduleResults.Select(s => new WorkingScheduleResultResponse_Search
                {
                    Id = s.Id,
                    WorkingScheduleId = s.WorkingScheduleId ?? 0,
                    Date = s.Date,
                    Result = s.Result,
                    Active = s.Active,
                    Files = s.WorkingScheduleResult_AttachDetail?.Select(f => new FileResponseget
                    {
                        Contents = f.Contents,
                        FileName = f.FileName,
                        FileType = f.FileType,
                        FileSize = f.FileSize
                    }).ToList()
                }).ToList();

                return result;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại GetWorkingScheduleResultByWorkingScheduleId: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }

    }
}
