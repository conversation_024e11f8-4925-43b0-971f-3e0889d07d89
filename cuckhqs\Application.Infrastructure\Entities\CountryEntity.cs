﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class CountryEntity : BaseEntity<int>
    {
        public string? CountryCode { get; set; }
        public string? CountryName { get; set; }
        public string? LanguageName { get; set; }
        public int? Class { get; set; }
        public int? Year { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public string? VietnameseName { get; set; }
    }
}
