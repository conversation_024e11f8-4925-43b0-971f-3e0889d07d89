﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response
{
    public class FileDownloadResult
    {
        public Stream? FileStream { get; set; }
        public string? FileName { get; set; }
        public string? OriginalFileName { get; set; }
        public string? FileType { get; set; }
    }

    public class FileData
    {
     
        public string? FileName { get; set; }
    }

    public class FileUploadResult
    {
        public string? FileName { get; set; }
        public string? Contents { get; set; }
        public string? FileType { get; set; }
        public double? FileSize { get; set; }
    }

    //public class FileResponse
    //{
    //    public string? Contents { get; set; }
    //    public string? FileName { get; set; }
    //    public string? FileType { get; set; }
    //    public float? FileSize { get; set; }
    //}
}
