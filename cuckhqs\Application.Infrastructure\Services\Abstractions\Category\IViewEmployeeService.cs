﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions.Category
{
    public interface IViewEmployeeService
    {
        Task<BaseSearchResponse<EmployeeResponse>> SearchEmployeeAsync(SearchEmployeeRequest request);
        //Task<WorkingResultResponse> CreateWorkingResultAsync(CreateWorkingResultRequest request);
        //Task<bool> DeleteWorkingResultAsync(DeleteWorkingResultRequest request);
        //Task<bool> UpdateWorkingResultAsync(UpdateWorkingResultRequest request);
    }
}
