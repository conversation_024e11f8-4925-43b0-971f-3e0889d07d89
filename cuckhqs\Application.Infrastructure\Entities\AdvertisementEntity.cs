﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class AdvertisementEntity : BaseEntity<int>
    {
        public string? AdvertisementCode { set; get; }
        public string? AdvertisementName { set; get; }
        public DateTime? Start { set; get; }
        public int? StartHour { set; get; }
        public int? StartMinute { set; get; }
        public DateTime? Ends { set; get; }
        public int? EndsHour { set; get; }
        public int? EndsMinute { set; get; }
        public int? Year { set; get; }
        public bool? Active { set; get; }
        public int? SortOrder { set; get; }
        public DateTime? CreatedDate { set; get; }
        public DateTime? ModifiedDate { set; get; }
        public string? IPAddress { set; get; }
        public string? ModifiedBy { set; get; }
        public string? CreatedBy { set; get; }
    }
}
