﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.Data.SqlClient;

namespace Application.Infrastructure.Entities
{
    public class ScheduleEntity : BaseEntity<int>
    {
        public string? Name { get; set; }
        public DateTime? Date { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public string? Rank { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }
    }
}
