﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations.OrganizationRepository
{
    public class PositionRepository : GenericRepository<PositionEntity, int>, IPositionRepository
    {
        public AppDbContext _context { get; set; }

        public PositionRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<PositionEntity>> GetAllPositionAsync(int type)
        {
            if (type == 1)
            {
                return await _context.Position
                .Where(p => p.IsRoot == false)
                .ToListAsync();
            }
            else if (type == 0)
            {
                return await _context.Position
                .ToListAsync();
            }
            else return null;
        }

        protected override void Update(PositionEntity requestObject, PositionEntity targetObject)
        {
            targetObject.ParentId = requestObject.ParentId;
            targetObject.PositionCode = requestObject.PositionCode;
            targetObject.PositionName = requestObject.PositionName;
            targetObject.IsRoot = requestObject.IsRoot;
            targetObject.Classify = requestObject.Classify;
            targetObject.ShortPositionName = requestObject.ShortPositionName;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}