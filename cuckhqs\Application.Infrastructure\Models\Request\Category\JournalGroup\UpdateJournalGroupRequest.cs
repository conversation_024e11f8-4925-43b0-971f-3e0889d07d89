﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.JournalType;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.JournalGroup
{
    public class UpdateJournalGroupRequest : CreateJournalGroupRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateJournalGroupRequest, JournalGroupEntity>> Expression
        {
            get
            {
                return entity => new JournalGroupEntity
                {
                    Id = entity.Id,
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupName = entity.JournalGroupName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalGroupEntity Create(UpdateJournalGroupRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
