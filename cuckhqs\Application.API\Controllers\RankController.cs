﻿using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Azure.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class RankController : ControllerBase
    {
        private readonly IRankService _rankService;

        public RankController(IRankService rankService)
        {
            _rankService = rankService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchRankAsync([FromBody] SearchRankRequest request)
        {
            try
            {
                var response = await _rankService.SearchRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateRankAsync([FromBody] CreateRankRequest request)
        {
            var response = await _rankService.CreateRankAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateRankAsync([FromBody] UpdateRankRequest request)
        {
            try
            {
                var response = await _rankService.UpdateRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteRankAsync([FromBody] DeleteRankRequest request)
        {
            try
            {
                var response = await _rankService.DeleteRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
