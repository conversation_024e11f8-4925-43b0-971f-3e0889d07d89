﻿using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using OpenIddict.Validation.AspNetCore;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class DepartmentController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IDepartmentServices _departmentServices;
        private readonly string _filePath;

        public DepartmentController(IDepartmentServices StaffPosiitonService, IWebHostEnvironment webHostEnvironment)
        {
            _departmentServices = StaffPosiitonService;
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpPost("search")]
        [Authorize(Policy = "dataEventRecordsPolicy")]
        public async Task<IActionResult> SearchDepartmentAsync([FromBody] SearchDepartmentRequest request)
        {
            var response = await _departmentServices.SearchDepartmentAsync(request);

            return Ok(response);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateStaffPositonAsync([FromBody] CreateDepartmentRequest request)
        {
            var response = await _departmentServices.CreateDepartmentAsync(request);
            return Ok(response);
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateStaffPositonAsync([FromBody] UpdateDepartmentRequest request)
        {
            var response = await _departmentServices.UpdateDepartmentAsync(request);
            return Ok(response);
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteStaffPositonAsync([FromBody] DeleteDepartmentRequest ids)
        {
            var response = await _departmentServices.DeleteDepartmentAsync(ids);
            return Ok(response);
        }


        [HttpPost("confirm-status")]
        public async Task<IActionResult> UpdateStatusByIdsAsync([FromBody] UpdateStatusByIdsIntRequest request)
        {
            var response = await _departmentServices.UpdateStatusByIds(request);

            return Ok(response);
        }
    }
}