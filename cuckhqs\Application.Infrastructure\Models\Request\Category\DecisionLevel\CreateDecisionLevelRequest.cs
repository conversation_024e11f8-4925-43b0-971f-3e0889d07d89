﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.DecisionLevel
{
    public class CreateDecisionLevelRequest
    {
        public string? DecisionLevelCode { get; set; }
        public string? DecisionLevelName { get; set; }
        public int? Class { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateDecisionLevelRequest, DecisionLevelEntity>> Expression
        {
            get
            {
                return entity => new DecisionLevelEntity
                {
                    DecisionLevelCode = entity.DecisionLevelCode,
                    DecisionLevelName = entity.DecisionLevelName,
                    Class = entity.Class,
                    Active = entity.Active,
                };
            }
        }

        public static DecisionLevelEntity Create(CreateDecisionLevelRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
