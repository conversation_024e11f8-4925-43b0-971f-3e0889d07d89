﻿using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.Ward;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class WardController : ControllerBase
    {
        private readonly IWardService _wardService;

        public WardController(IWardService WardService)
        {
            _wardService = WardService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchWardAsync([FromBody] SearchWardRequest request)
        {
            try
            {
                var response = await _wardService.SearchWardAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateWardAsync([FromBody] CreateWardRequest request)
        {
            var response = await _wardService.CreateWardAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateWardAsync([FromBody] UpdateWardRequest request)
        {
            try
            {
                var response = await _wardService.UpdateWardAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteWardAsync([FromBody] DeleteWardRequest request)
        {
            try
            {
                var response = await _wardService.DeleteWardAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
