﻿
namespace Application.Infrastructure.Exceptions
{
    public class BaseException : Exception
    {
        public string Message { get; set; }
        public string? Code { get; set; }
        public IDictionary<string, object> Payloads { get; private set; }

        protected BaseException(string message, string? code, System.Exception innerException) : base(message, innerException)
        {
            Message = message;
            Code = code;
        }

        protected BaseException(string message, string? code, IDictionary<string, object> payloads, System.Exception innerException) : base(message, innerException)
        {
            Message = message;
            Code = code;
            Payloads = payloads;
        }
    }
}
