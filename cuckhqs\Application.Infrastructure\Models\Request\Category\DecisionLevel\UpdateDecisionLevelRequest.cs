﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.Degree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.DecisionLevel
{
    public class UpdateDecisionLevelRequest : CreateDecisionLevelRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateDecisionLevelRequest, DecisionLevelEntity>> Expression
        {
            get
            {
                return entity => new DecisionLevelEntity
                {
                    Id = entity.Id,
                    DecisionLevelCode = entity.DecisionLevelCode,
                    DecisionLevelName = entity.DecisionLevelName,
                    Class = entity.Class,
                    Active = entity.Active
                };
            }
        }

        public static DecisionLevelEntity Create(UpdateDecisionLevelRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
