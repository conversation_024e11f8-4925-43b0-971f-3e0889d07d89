﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.RewardType
{
    public class UpdateRewardTypeRequest : CreateRewardTypeRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateRewardTypeRequest, RewardTypeEntity>> Expression
        {
            get
            {
                return entity => new RewardTypeEntity
                {
                    Id = entity.Id,
                    RewardTypeCode = entity.RewardTypeCode,
                    RewardTypeName = entity.RewardTypeName,
                    Class = entity.Class,
                    Active = entity.Active
                };
            }
        }

        public static RewardTypeEntity Create(UpdateRewardTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
