﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Models.Response;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Application.Infrastructure.Services.Implementations.WorkingScheduleResultService;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IWorkingScheduleResultService
    {
        Task<bool> CreateWorkingScheduleResultAsync(CreateWorkingScheduleResultRequest request);
        Task<List<WorkingScheduleResultResponse_Search>> GetWorkingScheduleResultByWorkingScheduleId(int WorkingScheduleId);
     //   Task<bool> UpdateWorkingScheduleResultAsync(CreateWorkingScheduleResultRequest request, int Id);
    }
}
