﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Application.Infrastructure.Configurations
{
    public class AdvertisementConfiguration : IEntityTypeConfiguration<AdvertisementEntity>
    {
        public void Configure(EntityTypeBuilder<AdvertisementEntity> builder)
        {
            builder.ToTable("Advertisement");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("Id");
            builder.Property(x => x.AdvertisementCode).HasColumnName("AdvertisementCode");
            builder.Property(x => x.AdvertisementName).HasColumnName("AdvertisementName");
            builder.Property(x => x.Start).HasColumnName("Start");
            builder.Property(x => x.StartHour).HasColumnName("StartHour");
            builder.Property(x => x.StartMinute).HasColumnName("StartMinute");
            builder.Property(x => x.Ends).HasColumnName("Ends");
            builder.Property(x => x.EndsHour).HasColumnName("EndsHour");
            builder.Property(x => x.EndsMinute).HasColumnName("EndsMinute");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
