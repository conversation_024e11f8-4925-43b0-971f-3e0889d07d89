﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Request.WorkingSchedule;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using Azure.Core;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using log4net;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Win32;
using ql_tb_vk_vt.Infrastructure.Constants;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using Microsoft.AspNetCore.Routing.Template;
using DocumentFormat.OpenXml.Office2016.Excel;
using Microsoft.Extensions.DependencyInjection;
using DocumentFormat.OpenXml.Bibliography;
using System.Text;
using System.Globalization;
using System.Linq;

namespace Application.Infrastructure.Services.Implementations
{
    public class WorkingScheduleService : IWorkingScheduleService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AppDbContext _dbContext;
        private readonly IServiceProvider _serviceProvider;
        private readonly IEmployeeCService _employeeService;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public WorkingScheduleService(IUnitOfWork unitOfWork, AppDbContext dbContext, IServiceProvider serviceProvider, IEmployeeCService employeeService)
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
            _serviceProvider = serviceProvider;
            _employeeService = employeeService;
        }

        public async Task<WorkingScheduleResponse> CreateWorkingScheduleAsync(CreateWorkingScheduleRequest request)
        {
            var dateStrings = request.Date.Split(',', StringSplitOptions.RemoveEmptyEntries);
            var dates = new List<DateTime>();

            foreach (var dateStr in dateStrings)
            {
                if (!DateTime.TryParse(dateStr.Trim(), out var parsedDate))
                {
                    throw new ArgumentException($"Định dạng ngày không hợp lệ: {dateStr}.", nameof(request.Date));
                }
                dates.Add(parsedDate.Date);
            }

            if (!dates.Any())
            {
                throw new ArgumentException("Danh sách ngày không hợp lệ.", nameof(request.Date));
            }

            var workingSchedules = new List<WorkingScheduleEntity>();
            var workingScheduleCEntities = new List<WorkingScheduleCEntity>();
            var workingScheduleEPEntities = new List<WorkingScheduleEPEntity>();
            var workingScheduleEPHEntities = new List<WorkingScheduleEPHEntity>();
            var workingScheduleOUEntities = new List<WorkingScheduleOUEntity>();

            WorkingScheduleEntity firstRecord = null;
            foreach (var date in dates)
            {
                var entity = new WorkingScheduleEntity
                {
                    Classify = Convert.ToInt16(request.Classify),
                    OrganizationUnitId_Chair = Convert.ToInt32(request.OrganizationUnitId_Chair),
                    OrganizationUnitId = Convert.ToInt32(request.OrganizationUnitId),
                    Year = Convert.ToInt32(request.Year),
                    Week = Convert.ToInt32(request.Week),
                    Date = date,
                    Time = Convert.ToInt16(request.Time),
                    CoChair = request.CoChair,
                    Place = request.Place,
                    Contents = request.Contents,
                    Note = request.Note,
                    Register = Convert.ToBoolean(request.Register),
                    Message = string.IsNullOrWhiteSpace(request.Message) ? false : Convert.ToBoolean(request.Message),
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    Member = request.Member,
                    WorkingScheduleC = new List<WorkingScheduleCEntity>(),
                    WorkingScheduleEP = new List<WorkingScheduleEPEntity>(),
                    WorkingScheduleEPH = new List<WorkingScheduleEPHEntity>(),
                    WorkingScheduleOU = new List<WorkingScheduleOUEntity>()
                };
                if (string.IsNullOrWhiteSpace(request.TimeFrom) || !DateTime.TryParse(request.TimeFrom, out DateTime timeFromValue))
                {
                    throw new ArgumentException($"Định dạng TimeFrom không hợp lệ: {request.TimeFrom}.", nameof(request.TimeFrom));
                }
                if (string.IsNullOrWhiteSpace(request.TimeTo) || !DateTime.TryParse(request.TimeTo, out DateTime timeToValue))
                {
                    throw new ArgumentException($"Định dạng TimeTo không hợp lệ: {request.TimeTo}.", nameof(request.TimeTo));
                }
                entity.TimeFrom = date.Add(timeFromValue.TimeOfDay);
                entity.TimeTo = date.Add(timeToValue.TimeOfDay);

                // Validate time range
                if (entity.TimeTo <= entity.TimeFrom)
                {
                    throw new ArgumentException("TimeTo phải muộn hơn TimeFrom.");
                }

                workingSchedules.Add(entity);

                if (firstRecord == null)
                {
                    firstRecord = entity;
                }

                _dbContext.WorkingSchedule.Add(entity);
                await _dbContext.SaveChangesAsync();
            }

            foreach (var entity in workingSchedules)
            {
                if (request.WorkingScheduleC != null && request.WorkingScheduleC.Any())
                {
                    var entities = request.WorkingScheduleC.Select(value =>
                    {
                        if (!Guid.TryParse(value, out Guid employeeId))
                        {
                            throw new ArgumentException($"EmployeeId không hợp lệ: {value}. EmployeeId phải là một GUID hợp lệ.", nameof(request.WorkingScheduleC));
                        }
                        return new WorkingScheduleCEntity
                        {
                            WorkingScheduleId = entity.Id,
                            EmployeeId = employeeId
                        };
                    }).ToList();
                    workingScheduleCEntities.AddRange(entities);
                    entity.WorkingScheduleC = entities;
                }

                if (request.WorkingScheduleEP != null && request.WorkingScheduleEP.Any())
                {
                    var entities = request.WorkingScheduleEP.Select(value =>
                    {
                        if (!Guid.TryParse(value, out Guid employeeId))
                        {
                            throw new ArgumentException($"EmployeeId không hợp lệ: {value}. EmployeeId phải là một GUID hợp lệ.", nameof(request.WorkingScheduleEP));
                        }
                        return new WorkingScheduleEPEntity
                        {
                            WorkingScheduleId = entity.Id,
                            EmployeeId = employeeId
                        };
                    }).ToList();
                    workingScheduleEPEntities.AddRange(entities);
                    entity.WorkingScheduleEP = entities;
                }

                if (request.WorkingScheduleEPH != null && request.WorkingScheduleEPH.Any())
                {
                    var entities = request.WorkingScheduleEPH.Select(value =>
                    {
                        if (!Guid.TryParse(value, out Guid employeeId))
                        {
                            throw new ArgumentException($"EmployeeId không hợp lệ: {value}. EmployeeId phải là một GUID hợp lệ.", nameof(request.WorkingScheduleEPH));
                        }
                        return new WorkingScheduleEPHEntity
                        {
                            WorkingScheduleId = entity.Id,
                            EmployeeId = employeeId
                        };
                    }).ToList();
                    workingScheduleEPHEntities.AddRange(entities);
                    entity.WorkingScheduleEPH = entities;
                }

                if (request.WorkingScheduleOU != null && request.WorkingScheduleOU.Any())
                {
                    var entities = request.WorkingScheduleOU.Select(value =>
                    {
                        if (!int.TryParse(value, out int organizationUnitId))
                        {
                            throw new ArgumentException($"OrganizationUnitId không hợp lệ: {value}. OrganizationUnitId phải là một số nguyên.", nameof(request.WorkingScheduleOU));
                        }
                        return new WorkingScheduleOUEntity
                        {
                            WorkingScheduleId = entity.Id,
                            OrganizationUnitId = organizationUnitId
                        };
                    }).ToList();
                    workingScheduleOUEntities.AddRange(entities);
                    entity.WorkingScheduleOU = entities;
                }
            }

            _dbContext.WorkingScheduleC.AddRange(workingScheduleCEntities);
            _dbContext.WorkingScheduleEP.AddRange(workingScheduleEPEntities);
            _dbContext.WorkingScheduleEPH.AddRange(workingScheduleEPHEntities);
            _dbContext.WorkingScheduleOU.AddRange(workingScheduleOUEntities);

            await _dbContext.SaveChangesAsync();

            var scheduleDataList = workingSchedules.Select(ws => new ScheduleData
            {
                Id = ws.Id,
                Classify = ws.Classify,
                OrganizationUnitId = ws.OrganizationUnitId,
                Register = false,
                Year = ws.Year,
                Week = ws.Week,
                Date = ws.Date,
                CoChair = ws.CoChair,
                Time = ws.Time,
                TimeFrom = ws.TimeFrom,
                TimeTo = ws.TimeTo,
                Place = ws.Place,
                Contents = ws.Contents,
                Member = ws.Member,
                Note = ws.Note,
                Message = ws.Message,
                OrganizationUnitId_Chair = ws.OrganizationUnitId_Chair,
                OrganizationUnitIdName = null,
                WorkingScheduleC = ws.WorkingScheduleC?.Select(wsc => wsc.EmployeeId.ToString()).ToList(),
                WorkingScheduleEP = ws.WorkingScheduleEP?.Select(wsep => wsep.EmployeeId.ToString()).ToList(),
                WorkingScheduleEPH = ws.WorkingScheduleEPH?.Select(wseph => wseph.EmployeeId.ToString()).ToList(),
                WorkingScheduleOU = ws.WorkingScheduleOU?.Select(wsou => wsou.OrganizationUnitId.ToString()).ToList(),
                OrganizationUnitId_ChairName = null,
                MTEntityState = 0,
                CreatedDate = ws.CreatedDate,
                ModifiedDate = ws.ModifiedDate,
                ModifiedBy = ws.ModifiedBy,
                CreatedBy = ws.CreatedBy,
                SortOrder = ws.SortOrder
            }).ToList();

            return new WorkingScheduleResponse
            {
                Success = true,
                ErrorType = 0,
                Id = workingSchedules.FirstOrDefault()?.Id ?? 0,
                Total = dates.Count,
                Code = 200,
                Data = scheduleDataList
            };
        }
        public async Task<BaseSearchResponse<WorkingScheduleResponse>> SearchWorkingScheduleAsync(SearchWorkingScheduleRequest request)
        {
            try
            {
                int week = Convert.ToInt32(request.week);
                int year = Convert.ToInt32(request.year);
                int? orgUnitId = string.IsNullOrWhiteSpace(request.organizationUnitId) ? (int?)null : Convert.ToInt32(request.organizationUnitId);
                Guid? employeeGuid = string.IsNullOrEmpty(request.employeeId.ToString()) || request.employeeId.ToString() == "00000000-0000-0000-0000-000000000000"
                    ? (Guid?)null
                    : Guid.Parse(request.employeeId.ToString().Trim());

                var employees = await _unitOfWork.Employees.GetAllEmployeesAsync();
                var employessNameShort = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ShortName)
                    ?? new Dictionary<string, string>();
                var employeeDict = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.FullName)
                    ?? new Dictionary<string, string>();
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();
                var shortOrgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.ShortOrganizationUnitName) ?? new Dictionary<string, string>();

                IQueryable<WorkingScheduleEntity> query = _unitOfWork.WorkingSchedule
                    .AsQueryable()
                    .AsNoTracking()
                    .Include(x => x.WorkingScheduleC)
                    .Include(x => x.WorkingScheduleEP)
                    .Include(x => x.WorkingScheduleEPH)
                    .Include(x => x.WorkingScheduleOU)
                    .Include(x => x.WorkingScheduleResult)
                        .ThenInclude(r => r.WorkingScheduleResult_AttachDetail)
                    .Where(x => x.Week == week && x.Year == year &&
                        (orgUnitId == null || x.OrganizationUnitId == orgUnitId) &&
                        (employeeGuid == null || x.WorkingScheduleC.Any(c => c.EmployeeId == employeeGuid)));

                var workingSchedules = await query.ToListAsync() ?? new List<WorkingScheduleEntity>();
                var totalCount = workingSchedules.Count;

                var scheduleDataList = workingSchedules.Select(s => new ScheduleData
                {
                    Id = s.Id,
                    Classify = s.Classify,
                    OrganizationUnitId = s.OrganizationUnitId,
                    WorkingScheduleResultId = s.WorkingScheduleResult?.Select(s => s.Id).FirstOrDefault(),
                    Register = s.Register,
                    Year = s.Year,
                    Week = s.Week,
                    Date = s.Date,
                    CoChair = s.CoChair,
                    Time = s.Time,
                    TimeFrom = s.TimeFrom,
                    TimeTo = s.TimeTo,
                    Place = s.Place,
                    Contents = s.Contents,
                    Member = s.Member,
                    Note = s.Note,
                    Message = s.Message,
                    OrganizationUnitId_Chair = s.OrganizationUnitId_Chair,
                    OrganizationUnitIdName = orgDict.TryGetValue(s.OrganizationUnitId.ToString(), out var name) ? name : "Không tìm thấy",
                    ShortOrganizationUnitName = shortOrgDict.TryGetValue(s.OrganizationUnitId.ToString(), out var shortName) ? shortName : "Không tìm thấy",
                    WorkingScheduleC = s.WorkingScheduleC?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleEP = s.WorkingScheduleEP?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleEPH = s.WorkingScheduleEPH?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleOU = s.WorkingScheduleOU?.Select(ws => ws.OrganizationUnitId.ToString()).ToList()
                        ?? new List<string>(),
                    OrganizationUnitId_ChairName = null,
                    MTEntityState = 0,
                    CreatedDate = s.CreatedDate,
                    ModifiedDate = s.ModifiedDate,
                    ModifiedBy = s.ModifiedBy,
                    CreatedBy = s.CreatedBy,
                    SortOrder = s.SortOrder,
                    Announced = s.Announced,
                    // Thêm danh sách file
                    File = s.WorkingScheduleResult?
                        .SelectMany(r => r.WorkingScheduleResult_AttachDetail ?? new List<WorkingScheduleResult_AttachDetailEntity>())
                        .Where(ad => !string.IsNullOrEmpty(ad.FileName))
                        .Select(ad =>
                        {
                            var name = ad.Contents + ad.FileType;
                            var guid = ad.FileName;
                            return new FileData
                            {
                                FileName = guid + "_" + name,
                            };
                        })
                        .ToList() ?? new List<FileData>()
                }).ToList();

                foreach (var item in scheduleDataList)
                {
                    item.WorkingScheduleCName = String.Join(", ", item.WorkingScheduleC?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPName = String.Join(", ", item.WorkingScheduleEP?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPHName = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleOUName = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                        orgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.OrganizationUnitId_ChairName = orgDict.TryGetValue(item.OrganizationUnitId_Chair.ToString(), out var name) ? name : "Không tìm thấy";
                    item.WorkingScheduleOUName_Short = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                        shortOrgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleCName_Short = String.Join(", ", item.WorkingScheduleC?.Select(ws =>
                        employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPName_Short = String.Join(", ", item.WorkingScheduleEP?.Select(ws =>
                        employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPHName_Short = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                        employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                }

                var response = new BaseSearchResponse<WorkingScheduleResponse>();
                typeof(BaseSearchResponse<WorkingScheduleResponse>)
                    .GetField("_data", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.SetValue(response, new List<WorkingScheduleResponse>
                    {
                new WorkingScheduleResponse
                {
                    Success = true,
                    ErrorType = 0,
                    Id = scheduleDataList.FirstOrDefault()?.Id ?? 0,
                    Total = totalCount,
                    Code = 200,
                    Data = scheduleDataList
                }
                    });
                typeof(BaseSearchResponse<WorkingScheduleResponse>)
                    .GetProperty("TotalCount")
                    ?.SetValue(response, totalCount);

                return response;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchWorkingScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }

        public async Task<List<WorkingScheduleResponse>> GetWorkingScheduleById(int Id)
        {
            try
            {
                var employees = await _unitOfWork.Employees.GetAllEmployeesAsync();
                var employeeDict = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.FullName)
                    ?? new Dictionary<string, string>();
                var employessNameShort = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ShortName)
                   ?? new Dictionary<string, string>();
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();
                var shortOrgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.ShortOrganizationUnitName) ?? new Dictionary<string, string>();

                var workingSchedules = await _unitOfWork.WorkingSchedule
                    .AsQueryable()
                    .AsNoTracking()
                    .Include(s => s.WorkingScheduleC)
                    .Include(s => s.WorkingScheduleEP)
                    .Include(s => s.WorkingScheduleEPH)
                    .Include(s => s.WorkingScheduleOU)
                    .Where(s => s.Id == Id)
                    .ToListAsync() ?? new List<WorkingScheduleEntity>();

                var scheduleDataList = workingSchedules.Select(s => new ScheduleData
                {
                    Id = s.Id,
                    Classify = s.Classify,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Register = s.Register,
                    Year = s.Year,
                    Week = s.Week,
                    Date = s.Date,
                    CoChair = s.CoChair,
                    Time = s.Time,
                    TimeFrom = s.TimeFrom,
                    TimeTo = s.TimeTo,
                    Place = s.Place,
                    Contents = s.Contents,
                    Member = s.Member,
                    Note = s.Note,
                    Message = s.Message,
                    OrganizationUnitId_Chair = s.OrganizationUnitId_Chair,
                    OrganizationUnitIdName = orgDict.TryGetValue(s.OrganizationUnitId.ToString(), out var name) ? name : "Không tìm thấy",
                    ShortOrganizationUnitName = shortOrgDict.TryGetValue(s.OrganizationUnitId.ToString(), out var shortName) ? shortName : "Không tìm thấy",
                    WorkingScheduleC = s.WorkingScheduleC?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleCName = "",
                    WorkingScheduleEP = s.WorkingScheduleEP?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleEPName = "",
                    WorkingScheduleEPH = s.WorkingScheduleEPH?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleEPHName = "",
                    WorkingScheduleOU = s.WorkingScheduleOU?.Select(ws => ws.OrganizationUnitId.ToString()).ToList()
                        ?? new List<string>(),
                    WorkingScheduleOUName = null,
                    WorkingScheduleResultId = 0,
                    OrganizationUnitId_ChairName = null,
                    MTEntityState = 0,
                    CreatedDate = s.CreatedDate,
                    ModifiedDate = s.ModifiedDate,
                    ModifiedBy = s.ModifiedBy,
                    CreatedBy = s.CreatedBy,
                    SortOrder = s.SortOrder,
                    Announced = s.Announced,
                }).ToList();

                foreach (var item in scheduleDataList)
                {
                    item.WorkingScheduleCName = String.Join(", ", item.WorkingScheduleC?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPName = String.Join(", ", item.WorkingScheduleEP?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPHName = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                        employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleOUName = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                        orgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.OrganizationUnitId_ChairName = orgDict.TryGetValue(item.OrganizationUnitId_Chair.ToString(), out var name) ? name : "Không tìm thấy";
                    item.WorkingScheduleOUName_Short = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                     shortOrgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleCName_Short = String.Join(", ", item.WorkingScheduleC?.Select(ws =>
                    employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPName_Short = String.Join(", ", item.WorkingScheduleEP?.Select(ws =>
                      employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.WorkingScheduleEPHName_Short = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                      employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                }

                var res = new List<WorkingScheduleResponse>
                    {
                        new WorkingScheduleResponse
                        {
                            Success = true,
                            ErrorType = 0,
                            Id = scheduleDataList.FirstOrDefault()?.Id ?? 0,
                            Total = scheduleDataList.Count,
                            Code = 200,
                            Data = scheduleDataList
                        }
                    };

                return res;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại GetWorkingScheduleById: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }


        public async Task<bool> UpdateWorkingScheduleAsync(UpdateWorkingScheduleRequest request)
        {
            try
            {
                if (!DateTime.TryParse(request.Date, out var parsedDate))
                {
                    throw new ArgumentException("Định dạng ngày không hợp lệ.", nameof(request.Date));
                }
                DateTime date = parsedDate;

                var workingSchedule = await _unitOfWork.WorkingSchedule.AsQueryable()
                    .Include(x => x.WorkingScheduleC)
                    .Include(x => x.WorkingScheduleEP)
                    .Include(x => x.WorkingScheduleEPH)
                    .Include(x => x.WorkingScheduleOU)
                    .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (workingSchedule == null)
                {
                    throw new NotFoundException($"Không tìm thấy lịch làm việc với Id: {request.Id}");
                }

                workingSchedule.OrganizationUnitId = Convert.ToInt32(request.OrganizationUnitId);
                workingSchedule.Year = Convert.ToInt32(request.Year);
                workingSchedule.Week = Convert.ToInt32(request.Week);
                workingSchedule.Date = date;
                workingSchedule.CoChair = request.CoChair;
                workingSchedule.Time = Convert.ToInt16(request.Time);

                if (!DateTime.TryParse(request.TimeFrom, out DateTime timeFromValue))
                {
                    throw new ArgumentException($"Định dạng TimeFrom không hợp lệ: {request.TimeFrom}");
                }
                workingSchedule.TimeFrom = timeFromValue;

                if (!DateTime.TryParse(request.TimeTo, out DateTime timeToValue))
                {
                    throw new ArgumentException($"Định dạng TimeTo không hợp lệ: {request.TimeTo}");
                }
                workingSchedule.TimeTo = timeToValue;

                workingSchedule.Place = request.Place;
                workingSchedule.Contents = request.Contents;
                workingSchedule.Note = request.Note;
                workingSchedule.Message = Convert.ToBoolean(request.Message);
                workingSchedule.OrganizationUnitId_Chair = Convert.ToInt32(request.OrganizationUnitId_Chair);
                workingSchedule.ModifiedDate = DateTime.UtcNow;
                workingSchedule.Register = Convert.ToBoolean(request.Register);
                workingSchedule.Member = request.Member;

                var currentWorkingScheduleC = workingSchedule.WorkingScheduleC?.ToList() ?? new List<WorkingScheduleCEntity>();
                var requestWorkingScheduleC = request.WorkingScheduleC?.Where(id => !string.IsNullOrWhiteSpace(id))
                    .Select(id => Guid.Parse(id).ToString().Trim().ToLower()).ToList() ?? new List<string>();


                var cToRemove = currentWorkingScheduleC
                    .Where(c => !requestWorkingScheduleC.Contains(c.EmployeeId.ToString().Trim().ToLower()))
                    .ToList();
                foreach (var item in cToRemove)
                {
                    workingSchedule.WorkingScheduleC.Remove(item);
                }

                var cToAdd = requestWorkingScheduleC
                    .Where(id => !currentWorkingScheduleC.Any(c => c.EmployeeId.ToString().Trim().ToLower() == id))
                    .ToList();
                foreach (var employeeId in cToAdd)
                {
                    workingSchedule.WorkingScheduleC.Add(new WorkingScheduleCEntity
                    {
                        WorkingScheduleId = request.Id,
                        EmployeeId = Guid.Parse(employeeId)
                    });
                }


                var currentWorkingScheduleEP = workingSchedule.WorkingScheduleEP?.ToList() ?? new List<WorkingScheduleEPEntity>();
                var requestWorkingScheduleEP = request.WorkingScheduleEP?.Where(id => !string.IsNullOrWhiteSpace(id))
                    .Select(id => Guid.Parse(id).ToString().Trim().ToLower()).ToList() ?? new List<string>();

                var epToRemove = currentWorkingScheduleEP
                    .Where(ep => !requestWorkingScheduleEP.Contains(ep.EmployeeId.ToString().Trim().ToLower()))
                    .ToList();
                foreach (var item in epToRemove)
                {
                    workingSchedule.WorkingScheduleEP.Remove(item);
                }

                var epToAdd = requestWorkingScheduleEP
                    .Where(id => !currentWorkingScheduleEP.Any(ep => ep.EmployeeId.ToString().Trim().ToLower() == id))
                    .ToList();
                foreach (var employeeId in epToAdd)
                {
                    workingSchedule.WorkingScheduleEP.Add(new WorkingScheduleEPEntity
                    {
                        WorkingScheduleId = request.Id,
                        EmployeeId = Guid.Parse(employeeId)
                    });
                }

                var currentWorkingScheduleEPH = workingSchedule.WorkingScheduleEPH?.ToList() ?? new List<WorkingScheduleEPHEntity>();
                var requestWorkingScheduleEPH = request.WorkingScheduleEPH?.Where(id => !string.IsNullOrWhiteSpace(id))
                    .Select(id => Guid.Parse(id).ToString().Trim().ToLower()).ToList() ?? new List<string>();

                var ephToRemove = currentWorkingScheduleEPH
                    .Where(eph => !requestWorkingScheduleEPH.Contains(eph.EmployeeId.ToString().Trim().ToLower()))
                    .ToList();
                foreach (var item in ephToRemove)
                {
                    workingSchedule.WorkingScheduleEPH.Remove(item);
                }

                var ephToAdd = requestWorkingScheduleEPH
                    .Where(id => !currentWorkingScheduleEPH.Any(eph => eph.EmployeeId.ToString().Trim().ToLower() == id))
                    .ToList();
                foreach (var employeeId in ephToAdd)
                {
                    workingSchedule.WorkingScheduleEPH.Add(new WorkingScheduleEPHEntity
                    {
                        WorkingScheduleId = request.Id,
                        EmployeeId = Guid.Parse(employeeId)
                    });
                }

                var currentWorkingScheduleOU = workingSchedule.WorkingScheduleOU?.ToList() ?? new List<WorkingScheduleOUEntity>();
                var requestWorkingScheduleOU = request.WorkingScheduleOU?.Where(id => !string.IsNullOrWhiteSpace(id))
                    .Select(id => Convert.ToInt32(id).ToString()).ToList() ?? new List<string>();

                var ouToRemove = currentWorkingScheduleOU
                    .Where(ou => !requestWorkingScheduleOU.Contains(ou.OrganizationUnitId.ToString()))
                    .ToList();
                foreach (var item in ouToRemove)
                {
                    workingSchedule.WorkingScheduleOU.Remove(item);
                }

                var ouToAdd = requestWorkingScheduleOU
                    .Where(id => !currentWorkingScheduleOU.Any(ou => ou.OrganizationUnitId.ToString() == id))
                    .ToList();
                foreach (var orgUnitId in ouToAdd)
                {
                    workingSchedule.WorkingScheduleOU.Add(new WorkingScheduleOUEntity
                    {
                        WorkingScheduleId = request.Id,
                        OrganizationUnitId = Convert.ToInt32(orgUnitId)
                    });
                }

                await _unitOfWork.WorkingSchedule.UpdateAsync(request.Id, workingSchedule);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateWorkingScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }

        public async Task<bool> DeleteWorkingSchedulesAsync(DeleteWorkingScheduleRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    throw new ArgumentException("Danh sách Ids không được để trống.", nameof(request.Ids));
                }

                var workingSchedules = await _unitOfWork.WorkingSchedule.AsQueryable()
                    .Include(x => x.WorkingScheduleC)
                    .Include(x => x.WorkingScheduleEP)
                    .Include(x => x.WorkingScheduleEPH)
                    .Include(x => x.WorkingScheduleOU)
                    .Where(x => request.Ids.Any(id => id == x.Id))
                    .ToListAsync();

                if (workingSchedules == null || !workingSchedules.Any())
                {
                    throw new NotFoundException($"Không tìm thấy bất kỳ lịch làm việc nào với các Id: {string.Join(", ", request.Ids)}");
                }

                foreach (var workingSchedule in workingSchedules)
                {
                    if (workingSchedule.WorkingScheduleC != null)
                    {
                        workingSchedule.WorkingScheduleC.Clear();
                    }

                    if (workingSchedule.WorkingScheduleEP != null)
                    {
                        workingSchedule.WorkingScheduleEP.Clear();
                    }

                    if (workingSchedule.WorkingScheduleEPH != null)
                    {
                        workingSchedule.WorkingScheduleEPH.Clear();
                    }

                    if (workingSchedule.WorkingScheduleOU != null)
                    {
                        workingSchedule.WorkingScheduleOU.Clear();
                    }
                }

                _unitOfWork.WorkingSchedule.RemoveRange(workingSchedules);


                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteWorkingSchedulesAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }


        public async Task<List<WSPlaceResponse>> GetPlaceAsync()
        {
            try
            {
                var result = await _unitOfWork.WorkingSchedule.AsQueryable().AsNoTracking()
                    .Select(s => s.Place)
                    .Distinct()
                    .Select(address => new WSPlaceResponse { Address = address })
                    .ToListAsync();
                return result;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại GetPlaceAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        #region trạng thái dự thảo và ban hành
        public async Task<object> WorkingScheduleIssueAnnouncedAsync(WorkingScheduleIssueAnnouncedRequest request)
        {
            try
            {
                if (request.Week == 0 || request.Year == null)
                {
                    throw new ArgumentException("Week and Year are required.");
                }

                // Tìm các bản ghi trong WorkingSchedule
                IQueryable<WorkingScheduleEntity> workingSchedules = _unitOfWork.WorkingSchedule
                    .AsQueryable()
                    .AsNoTracking()
                    .Where(x => x.Week == request.Week &&
                               x.Year == request.Year &&
                               (request.OrganizationUnitId == null || x.OrganizationUnitId == request.OrganizationUnitId));

                if (workingSchedules == null || !workingSchedules.Any())
                {
                    throw new NotFoundException("Không có lịch họp vào thời gian này ");
                }

                // Cập nhật trường Announced trong WorkingSchedule
                foreach (var ws in workingSchedules)
                {
                    ws.Announced = request.Announced;
                    _dbContext.WorkingSchedule.Update(ws);
                }
                await _unitOfWork.SaveChangesAsync();

                //Kiểm tra xem đã có bản ghi trong WorkingScheduleIssue chưa
                var existingIssue = await _dbContext.WorkingScheduleIssue
                    .FirstOrDefaultAsync(i => i.Week == request.Week &&
                                             i.Year == request.Year &&
                                             (request.OrganizationUnitId == null || i.OrganizationUnitId == request.OrganizationUnitId));

                if (existingIssue == null)
                {
                    // Trường hợp lần đầu: Tạo bản ghi mới trong WorkingScheduleIssue
                    var issueEntity = new WorkingScheduleIssueEntity
                    {
                        OrganizationUnitId = request.OrganizationUnitId,
                        Year = request.Year,
                        Week = request.Week,
                        Number = request.Number,
                        Sign = request.Sign,
                        Date = Convert.ToDateTime(request.Date),
                        Announced = request.Announced,
                        Place = request.Place,
                        Command = request.Command,
                        PersonSigningOther = request.PersonSigningOther,
                        PersonSigning = request.PersonSigning,
                        UnitPositionSigning = request.UnitPositionSigning,
                        CreatedDate = DateTime.UtcNow,
                    };

                    _dbContext.WorkingScheduleIssue.Add(issueEntity);
                    await _unitOfWork.SaveChangesAsync();

                    // Tạo bản ghi mới trong WorkingScheduleIssueDetail
                    if (!string.IsNullOrWhiteSpace(request.ReceiverIDs))
                    {
                        var receiverIds = request.ReceiverIDs.Split(',').Select(r => r.Trim()).Where(r => !string.IsNullOrEmpty(r));
                        foreach (var receiverId in receiverIds)
                        {
                            Guid receiverGuid;
                            if (!Guid.TryParse(receiverId, out receiverGuid))
                            {
                                throw new ArgumentException($"Invalid GUID format for ReceiverId: {receiverId}");
                            }
                            var detailEntity = new WorkingScheduleIssueDetailEntity
                            {
                                WorkingScheduleIssueId = issueEntity.Id,
                                EmployeeId = receiverGuid,
                                CreatedDate = DateTime.UtcNow,
                                IPAddress = issueEntity.IPAddress
                            };
                            _dbContext.WorkingScheduleIssueDetail.Add(detailEntity);
                        }
                        await _unitOfWork.SaveChangesAsync();
                    }
                }
                else
                {
                    // Trường hợp cập nhật lần sau: Chỉ cập nhật Announced trong WorkingScheduleIssue
                    existingIssue.Announced = request.Announced;
                    existingIssue.Year = request.Year;
                    existingIssue.Week = request.Week;
                    existingIssue.Number = request.Number;
                    existingIssue.Sign = request.Sign;
                    existingIssue.Date = Convert.ToDateTime(request.Date);
                    existingIssue.Place = request.Place;
                    existingIssue.Command = request.Command;
                    existingIssue.PersonSigning = request.PersonSigning;
                    existingIssue.PersonSigningOther = request.PersonSigningOther;
                    existingIssue.UnitPositionSigning = request.UnitPositionSigning;
                    _dbContext.WorkingScheduleIssue.Update(existingIssue);
                    await _unitOfWork.SaveChangesAsync();

                    //// Cập nhật ModifiedDate trong WorkingScheduleIssueDetail
                    //if (!string.IsNullOrWhiteSpace(request.ReceiverIDs))
                    //{
                    //    // Chuyển receiverIds thành List<Guid> để sử dụng Contains trong EF Core
                    //    var receiverIds = request.ReceiverIDs.Split(',')
                    //        .Select(r => r.Trim())
                    //        .Where(r => !string.IsNullOrEmpty(r))
                    //        .Select(id =>
                    //        {
                    //            if (!Guid.TryParse(id, out Guid guid))
                    //            {
                    //                throw new ArgumentException($"Invalid GUID format for ReceiverId: {id}");
                    //            }
                    //            return guid;
                    //        })
                    //        .ToList(); // Chuyển thành List<Guid>

                    //    var existingDetails = await _dbContext.WorkingScheduleIssueDetail
                    //        .Where(d => d.WorkingScheduleIssueId == existingIssue.Id &&
                    //                   receiverIds.Contains(d.EmployeeId))
                    //        .ToListAsync();

                    //    foreach (var detail in existingDetails)
                    //    {
                    //        detail.ModifiedDate = DateTime.UtcNow;
                    //        _dbContext.WorkingScheduleIssueDetail.Update(detail);
                    //    }
                    //    await _unitOfWork.SaveChangesAsync();
                    //}
                }

                return new { Success = true };
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại WorkingScheduleIssueAnnouncedAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }
        #endregion

        #region search trạng thái ban hành
        public async Task<BaseSearchResponse<WorkingScheduleAnnouncedResponse>> SearchWorkingScheduleAnnouncedAsync(SearchWorkingScheduleAnnouncedRequest request)
        {
            try
            {

                int week = Convert.ToInt32(request.Week);
                int year = Convert.ToInt32(request.Year);

                var employees = await _unitOfWork.Employees.GetAllEmployeesAsync();
                var employessNameShort = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ShortName)
                    ?? new Dictionary<string, string>();
                var employeeDict = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.FullName)
                    ?? new Dictionary<string, string>();
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();
                var shortOrgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.ShortOrganizationUnitName) ?? new Dictionary<string, string>();

                IQueryable<WorkingScheduleIssueEntity> query = _unitOfWork.WorkingScheduleAnnounced
                .AsQueryable()
                .AsNoTracking()
                .Include(x => x.WorkingScheduleIssueDetail)
                .Where(x => x.Week == week && x.Year == year &&
                 (request.OrganizationUnitId == null || x.OrganizationUnitId == request.OrganizationUnitId));

                var workingSchedules = await query.ToListAsync() ?? new List<WorkingScheduleIssueEntity>();
                var totalCount = workingSchedules.Count;

                var scheduleDataList = workingSchedules.Select(s => new WorkingScheduleAnnouncedResponse
                {
                    Id = s.Id,
                    Year = s.Year,
                    Week = s.Week,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Number = s.Number,
                    Date = s.Date,
                    Sign = s.Sign,
                    Announced = s.Announced,
                    Place = s.Place,
                    Command = s.Command,
                    PersonSigning = s.PersonSigning,
                    PersonSigningOther = s.PersonSigningOther,
                    UnitPositonSigning = s.UnitPositionSigning,
                    Receive = s.WorkingScheduleIssueDetail?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                        ?? new List<string>()
                }).ToList();

                foreach (var item in scheduleDataList)
                {
                    item.Receive_Name = string.Join(", ", item.Receive?.Select(ws =>
                        employeeDict.TryGetValue(ws.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    item.PersonSigning_Name = employeeDict.TryGetValue(item.PersonSigning?.ToString() ?? "", out var name)
                        ? name
                        : "Không tìm thấy";
                    //item.WorkingScheduleEPHName = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                    //    employeeDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    //item.WorkingScheduleOUName = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                    //    orgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    //item.OrganizationUnitId_ChairName = orgDict.TryGetValue(item.OrganizationUnitId_Chair.ToString(), out var name) ? name : "Không tìm thấy";
                    //item.WorkingScheduleOUName_Short = String.Join(", ", item.WorkingScheduleOU?.Select(ws =>
                    //   shortOrgDict.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    //item.WorkingScheduleCName_Short = String.Join(", ", item.WorkingScheduleC?.Select(ws =>
                    //  employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    //item.WorkingScheduleEPName_Short = String.Join(", ", item.WorkingScheduleEP?.Select(ws =>
                    //  employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());
                    //item.WorkingScheduleEPHName_Short = String.Join(", ", item.WorkingScheduleEPH?.Select(ws =>
                    //  employessNameShort.TryGetValue(ws, out var name) ? name : "Không tìm thấy") ?? new List<string>());

                }

                var response = new BaseSearchResponse<WorkingScheduleAnnouncedResponse>();
                typeof(BaseSearchResponse<WorkingScheduleAnnouncedResponse>)
                    .GetField("_data", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.SetValue(response, scheduleDataList);
                typeof(BaseSearchResponse<WorkingScheduleAnnouncedResponse>)
                    .GetProperty("TotalCount")
                    ?.SetValue(response, totalCount);
                return response;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchWorkingScheduleAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }
        #endregion

        public async Task<MemoryStream> CreateScheduleWordFile(string templatePath, SearchWorkingScheduleRequest request)
        {
            dynamic tableData = await handleData(request);     // Dữ liệu lịch làm việc
            var firstDayOfWeek = GetFirstDayOfWeek(Convert.ToInt32(request.year), Convert.ToInt32(request.week));
            var lastDayOfWeek = firstDayOfWeek.AddDays(6);
            MemoryStream memoryStream = new MemoryStream();
            // Mở file mẫu ở chế độ đọc
            using (WordprocessingDocument sourceDocument = WordprocessingDocument.Open(templatePath, false))
            {
                // Tạo file mới
                using (WordprocessingDocument wordDocument = WordprocessingDocument.Create(memoryStream, WordprocessingDocumentType.Document))
                {
                    // Sao chép MainDocumentPart từ file nguồn sang file mới
                    MainDocumentPart mainPart = wordDocument.AddMainDocumentPart();
                    mainPart.Document = (Document)sourceDocument.MainDocumentPart.Document.CloneNode(true);

                    Body body = mainPart.Document.Body;

                    // Đặt lại SpacingAfter cho tất cả các đoạn trong Body, bao gồm từ file mẫu
                    foreach (var paragraph in body.Elements<Paragraph>())
                    {
                        if (paragraph.ParagraphProperties == null)
                        {
                            paragraph.ParagraphProperties = new ParagraphProperties();
                        }
                        paragraph.ParagraphProperties.SpacingBetweenLines = new SpacingBetweenLines { After = "0" };
                    }

                    // Đảm bảo style mặc định (Normal) không áp dụng SpacingAfter
                    StyleDefinitionsPart stylePart = mainPart.StyleDefinitionsPart ?? mainPart.AddNewPart<StyleDefinitionsPart>();
                    var normalStyle = stylePart.Styles?.Elements<Style>().FirstOrDefault(s => s.StyleId?.Value == "Normal");
                    if (normalStyle != null && normalStyle.StyleParagraphProperties != null)
                    {
                        normalStyle.StyleParagraphProperties.SpacingBetweenLines = new SpacingBetweenLines { After = "0" };
                    }
                    else if (normalStyle == null)
                    {
                        stylePart.Styles = new Styles();
                        normalStyle = new Style { Type = StyleValues.Paragraph, StyleId = "Normal", Default = true };
                        normalStyle.Append(new StyleName { Val = "Normal" });
                        normalStyle.Append(new StyleParagraphProperties(
                            new SpacingBetweenLines { After = "0" }
                        ));
                        stylePart.Styles.Append(normalStyle);
                    }

                    // Thêm tiêu đề lịch làm việc với SpacingAfter = 0
                    Paragraph scheduleTitle = body.AppendChild(new Paragraph());
                    scheduleTitle.Append(
                        CreateRun($"LỊCH LÀM VIỆC TUẦN CỦA {(request.typeExport == "1" ? "PHÒNG" : "THỦ TRƯỞNG CỤC")}", 28, true, false),
                        new Break(),
                        CreateRun($"Từ ngày {firstDayOfWeek:dd/MM} đến ngày {lastDayOfWeek:dd/MM/yyyy}", 24, false, false),
                        new Break(),
                        CreateRun($"Trực Chỉ huy: ", 26, true, true)
                    );
                    scheduleTitle.ParagraphProperties = new ParagraphProperties
                    {
                        Justification = new Justification { Val = JustificationValues.Center },
                        SpacingBetweenLines = new SpacingBetweenLines { After = "0" }
                    };

                    int index = 0;
                    foreach (var item in tableData.Employees)
                    {
                        // Tạo bảng lịch làm việc
                        Table table = body.AppendChild(new Table());
                        TableGrid tableGrid = new TableGrid(
                            new GridColumn { Width = "80" },  // Cột 1: 15% (750/5000)
                            new GridColumn { }, // Cột 2: 42.5% (2125/5000)
                            new GridColumn { }  // Cột 3: 42.5% (2125/5000)
                        );
                        table.AppendChild(tableGrid);

                        // Định dạng viền bảng
                        TableProperties tableProps = new TableProperties(
                            new TableBorders(
                                new TopBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 },
                                new BottomBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 },
                                new LeftBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 },
                                new RightBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 },
                                new InsideHorizontalBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 },
                                new InsideVerticalBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 6 }
                            ),
                            new TableWidth { Width = "5000", Type = TableWidthUnitValues.Pct },
                            new TableLayout { Type = TableLayoutValues.Fixed }
                        );
                        table.AppendChild(tableProps);

                        if (request.typeExport == "1")
                        {
                            // Hàng tiêu đề 1 (dòng đầu tiên)
                            TableRow headerRow1 = table.AppendChild(new TableRow());
                            headerRow1.Append(
                                CreateTableCell("Thứ/ngày", true, true),
                                CreateTableCell("Sáng", true, true),
                                CreateTableCell("Chiều", true, true)
                            );
                        }
                        else
                        {
                            // Hàng tiêu đề 1 (dòng đầu tiên)
                            TableRow headerRow1 = table.AppendChild(new TableRow());
                            headerRow1.Append(
                                CreateTableCell("Thứ/ngày", true, true, MergedCellValues.Restart),
                                CreateMergedTableCell($"Đ/c {item.EmployeeName}", "Chức Vụ", true, true, 2) // Merge 2 cột
                            );

                            // Hàng tiêu đề 2 (dòng thứ hai)
                            TableRow headerRow2 = table.AppendChild(new TableRow());
                            headerRow2.Append(
                                CreateTableCell("", false, false, MergedCellValues.Continue),
                                CreateTableCell("Sáng", true, true),
                                CreateTableCell("Chiều", true, true)
                            );
                        }

                        // Thêm dữ liệu vào bảng
                        foreach (var rowData in item.Data)
                        {
                            TableRow row = table.AppendChild(new TableRow());
                            row.Append(
                                CreateTableCell(rowData.Day, true, true),
                                CreateTableCell(rowData.MorningContent, false, false),
                                CreateTableCell(rowData.AfternoonContent, false, false)
                            );
                        }

                        // Thêm ngắt trang để sang trang mới
                        if (index < tableData.Employees.Count - 1)
                        {
                            Paragraph pageBreakParagraph = body.AppendChild(new Paragraph());
                            pageBreakParagraph.AppendChild(new Break() { Type = BreakValues.Page });
                        }
                        index++;
                    }
                }
            }
            memoryStream.Position = 0;
            return memoryStream;
        }

        // Hàm riêng để thêm văn bản động
        //private void AddDynamicText(Body body)
        //{
        //    foreach (var paragraph in body.Elements<Paragraph>().ToList())
        //    {
        //        var text = string.Join("", paragraph.Descendants<Text>().Select(t => t.Text));
        //        if (text.Contains("CỤC KHÓA HỌC QUÂN SỰ"))
        //        {
        //            // Tạo paragraph mới cho "Số: 12/LT-KHQS"
        //            Paragraph numberParagraph = new Paragraph();
        //            numberParagraph.Append(
        //                CreateRun("Số: 12/LT-KHQS", 20, false, false)
        //            );
        //            numberParagraph.ParagraphProperties = new ParagraphProperties
        //            {
        //                Justification = new Justification { Val = JustificationValues.Left },
        //                SpacingBetweenLines = new SpacingBetweenLines { After = "0" }
        //            };
        //            // Chèn "Số: 12/LT-KHQS" ngay sau đoạn văn hiện tại
        //            paragraph.InsertAfterSelf(numberParagraph);

        //            // Tạo paragraph mới cho "dự thảo"
        //            Paragraph draftParagraph = new Paragraph();
        //            draftParagraph.Append(
        //                CreateRun("dự thảo", 20, false, false)
        //            );
        //            draftParagraph.ParagraphProperties = new ParagraphProperties
        //            {
        //                Justification = new Justification { Val = JustificationValues.Left },
        //                SpacingBetweenLines = new SpacingBetweenLines { After = "0" }
        //            };
        //            // Chèn "dự thảo" ngay sau "Số: 12/LT-KHQS"
        //            numberParagraph.InsertAfterSelf(draftParagraph);
        //            break;
        //        }
        //    }
        //}

        #region private
        #region   cấu hình và xử lý dữ liệu để xuất file word
        public DateTime GetFirstDayOfWeek(int year, int week)
        {
            // Sử dụng lịch chuẩn
            System.Globalization.Calendar calendar = CultureInfo.InvariantCulture.Calendar;

            // Tìm ngày 4/1 vì nó luôn nằm trong tuần 1 theo ISO 8601
            DateTime jan4 = new DateTime(year, 1, 4);

            // Tìm thứ Hai của tuần chứa 4/1 (tuần 1)
            int daysToMonday = ((int)jan4.DayOfWeek - (int)DayOfWeek.Monday + 7) % 7;
            DateTime firstMonday = jan4.AddDays(-daysToMonday);

            // Tính thứ Hai của tuần được yêu cầu
            DateTime targetMonday = firstMonday.AddDays((week - 1) * 7);

            // Kiểm tra tính hợp lệ của tuần
            int weeksInYear = calendar.GetWeekOfYear(
                new DateTime(year, 12, 31),
                CalendarWeekRule.FirstFourDayWeek,
                DayOfWeek.Monday
            );
            if (week < 1 || week > weeksInYear)
            {
                throw new ArgumentException($"Tuần {week} không hợp lệ trong năm {year}. Năm {year} có {weeksInYear} tuần.");
            }

            return targetMonday;
        }
        private static Run CreateRun(string text, int fontSizeUnits, bool isBold = false, bool isItalic = false)
        {
            Run run = new Run();
            RunProperties runProps = new RunProperties(
                new RunFonts { Ascii = "Times New Roman", HighAnsi = "Times New Roman", EastAsia = "Times New Roman" },
                new FontSize { Val = fontSizeUnits.ToString() }
            );

            if (isBold)
            {
                runProps.Append(new Bold());
            }
            if (isItalic)
            {
                runProps.Append(new Italic());
            }

            run.AppendChild(runProps);
            run.AppendChild(new Text(text));
            return run;
        }

        private static TableCell CreateTableCell(string text, bool isHeader, bool isCenter = false, MergedCellValues? verticalMerge = null)
        {
            TableCell cell = new TableCell();
            Paragraph para = cell.AppendChild(new Paragraph());

            string[] lines = text.Split(new[] { "\n" }, StringSplitOptions.None);

            if (lines.Length == 1)
            {
                Run run = CreateRun(lines[0], 26, isHeader);
                para.AppendChild(run);
            }
            else
            {
                foreach (string line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        para.AppendChild(CreateRun(line.Trim(), 26, isHeader));
                        if (line != lines.Last())
                            para.AppendChild(new Break());
                    }
                }
            }

            TableCellProperties cellProperties = new TableCellProperties(
                new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
            );

            if (verticalMerge.HasValue)
            {
                cellProperties.Append(new VerticalMerge { Val = verticalMerge.Value });
            }
            cell.AppendChild(cellProperties);

            ParagraphProperties paraProps = new ParagraphProperties
            {
                SpacingBetweenLines = new SpacingBetweenLines { After = "0" },
                Indentation = new Indentation { Left = "144", Right = "144" }
            };

            if (isCenter)
                paraProps.Justification = new Justification { Val = JustificationValues.Center };

            para.ParagraphProperties = paraProps;

            return cell;
        }

        private static TableCell CreateMergedTableCell(string mainText, string subText, bool isHeader, bool isCenter, int gridSpan)
        {
            TableCell cell = new TableCell();
            Paragraph para = cell.AppendChild(new Paragraph());

            // Thêm dòng chính
            Run mainRun = CreateRun(mainText, 26, true); // Tiêu đề chính, in đậm
            para.AppendChild(mainRun);
            para.AppendChild(new Break());

            // Thêm dòng phụ (subheader) với cỡ chữ nhỏ hơn
            Run subRun = CreateRun(subText, 26, true); // Phụ đề, không in đậm
            para.AppendChild(subRun);

            // Định nghĩa GridSpan để merge cột
            TableCellProperties cellProperties = new TableCellProperties(
                new GridSpan { Val = gridSpan }, // Merge số cột chỉ định
                new TableCellVerticalAlignment { Val = TableVerticalAlignmentValues.Center }
            );
            cell.AppendChild(cellProperties);

            ParagraphProperties paraProps = new ParagraphProperties
            {
                SpacingBetweenLines = new SpacingBetweenLines { After = "0" },
                Indentation = new Indentation { Left = "144", Right = "144" } // 0.1 inch = 144 twips
            };

            if (isHeader || isCenter)
            {
                paraProps.Justification = new Justification { Val = JustificationValues.Center };
            }

            para.ParagraphProperties = paraProps;

            return cell;
        }

        private async Task<object> handleData(SearchWorkingScheduleRequest request)
        {
            try
            {
                var departmentHead = await _employeeService.GetAlDepartmentHeadAsync();
                using (var scope = _serviceProvider.CreateScope())
                {
                    var workingScheduleService = scope.ServiceProvider.GetRequiredService<IWorkingScheduleService>();
                    var scheduleData = (await workingScheduleService.SearchWorkingScheduleAsync(request)).Data.ToList()[0].Data.ToList();
                    var employeeMap = departmentHead.ToDictionary(
                        head => head.Id.ToString(),
                        head => head.FullName
                    );
                    // Lấy danh sách nhân viên từ các trường và loại bỏ trùng lặp
                    var employeeNames = scheduleData
                        .SelectMany(item => new[]
                        {
                            request.typeExport != "1" ? item.WorkingScheduleC : null,
                            request.typeExport != "1" ? item.WorkingScheduleEP : null,
                            request.typeExport != "1" ? item.WorkingScheduleEPH : null,
                            request.typeExport == "1" ? item.WorkingScheduleOU : null
                        }
                        .Where(names => names != null)
                        .SelectMany(names => names))
                        .Distinct()
                        .ToList();
                    var departmentHeadNames = new List<string>();
                    if (request.typeExport != "1") departmentHeadNames = employeeNames.Where(id => employeeMap.ContainsKey(id)).ToList();
                    else departmentHeadNames = employeeNames;

                    // Tính ngày bắt đầu của tuần (Thứ 2)
                    DateTime weekStart = GetFirstDayOfWeek(Convert.ToInt32(request.year), Convert.ToInt32(request.week));
                    string[] daysOfWeek = new string[7];

                    // Tạo mảng daysOfWeek động
                    string[] dayNames = new string[] { "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7", "Chủ nhật" };
                    for (int i = 0; i < 7; i++)
                    {
                        DateTime currentDay = weekStart.AddDays(i);
                        daysOfWeek[i] = $"{dayNames[i]}\n{currentDay:dd/M}";
                    }

                    // Chuẩn bị dữ liệu cho từng ngày
                    var employees = new List<object>();
                    foreach (var employee in departmentHeadNames)
                    {
                        var tableData = new List<ScheduleRowData>();
                        for (int i = 0; i < daysOfWeek.Length; i++)
                        {
                            DateTime? currentDay = weekStart.AddDays(i);

                            // Lọc dữ liệu theo ngày và nhân viên
                            var itemsForDay = scheduleData
                                .Where(item => item.Date == currentDay &&
                                    ((item.WorkingScheduleC?.Contains(employee) == true && request.typeExport != "1") ||
                                     (item.WorkingScheduleEP?.Contains(employee) == true && request.typeExport != "1") ||
                                     (item.WorkingScheduleEPH?.Contains(employee) == true && request.typeExport != "1") ||
                                     (item.WorkingScheduleOU?.Contains(employee) == true && request.typeExport == "1"))
                                    )
                                .ToList();

                            // Phân loại dữ liệu theo buổi Sáng/Chiều
                            var morningItems = itemsForDay
                                .Where(item => item.TimeFrom?.Hour < 13 || (item.TimeTo?.Hour <= 13 && item.TimeTo?.Hour > 0))
                                .OrderBy(item => item.TimeFrom)
                                .ToList();
                            var afternoonItems = itemsForDay
                                .Where(item => item.TimeFrom?.Hour >= 13 || item.TimeTo?.Hour > 13)
                                .OrderBy(item => item.TimeFrom)
                                .ToList();

                            // Định dạng nội dung cho cột Sáng và Chiều
                            string morningContent = FormatScheduleItems(morningItems, currentDay);
                            string afternoonContent = FormatScheduleItems(afternoonItems, currentDay);

                            tableData.Add(new ScheduleRowData
                            {
                                Day = daysOfWeek[i],
                                MorningContent = morningContent,
                                AfternoonContent = afternoonContent
                            });
                        }
                        employees.Add(new
                        {
                            EmployeeName = request.typeExport == "1" ? "" : employeeMap[employee],
                            Data = tableData
                        });
                    }

                    return new
                    {
                        Employees = employees
                    };
                }
            }catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        private string FormatScheduleItems(List<ScheduleData> items, DateTime? currentDay)
        {
            bool isWeekend = currentDay.HasValue &&
                     (currentDay.Value.DayOfWeek == DayOfWeek.Saturday ||
                      currentDay.Value.DayOfWeek == DayOfWeek.Sunday);

            // Nếu không có lịch và là cuối tuần, trả về "Nghỉ"
            if (!items.Any() && isWeekend)
                return "Nghỉ";
            // Nếu không có lịch và không phải cuối tuần, trả về "Làm việc tại cơ quan"
            else if (!items.Any())
                return "Làm việc tại cơ quan";

            var sb = new StringBuilder();
            var isOne = 0;
            foreach (var item in items)
            {
                if (sb.Length > 0)
                    sb.Append("\n\n");

                sb.Append($"{item.TimeFrom:HH:mm} - {item.TimeTo:HH:mm}: {item.Contents}\n");
                sb.Append($"TP: {item.WorkingScheduleCName_Short}");
                if (!string.IsNullOrWhiteSpace(item.WorkingScheduleOUName_Short))
                    sb.Append($", {item.WorkingScheduleOUName_Short}");
                if (!string.IsNullOrWhiteSpace(item.WorkingScheduleEPHName_Short))
                    sb.Append($", {item.WorkingScheduleEPHName_Short}");
                if (!string.IsNullOrWhiteSpace(item.WorkingScheduleEPName_Short))
                    sb.Append($", {item.WorkingScheduleEPName_Short}");
                if (isOne == items.Count - 1) sb.Append($"\nTại: {item.Place ?? ""}");
                else sb.Append($"\nTại: {item.Place ?? ""}\n");

                ++isOne;
            }

            return sb.ToString();
        }
        public class ScheduleRowData
        {
            public string Day { get; set; }
            public string MorningContent { get; set; }
            public string AfternoonContent { get; set; }
        }

        public async Task<List<ScheduleData>> CheckDuplicateSchedule(CreateWorkingScheduleRequest request)
        {
            try
            {
                var dateStrings = request.Date.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var dates = new List<DateTime>();
                var duplicateSchedules = new List<ScheduleData>();

                foreach (var dateStr in dateStrings)
                {
                    if (!DateTime.TryParse(dateStr.Trim(), out var parsedDate))
                    {
                        throw new ArgumentException($"Định dạng ngày không hợp lệ: {dateStr}.", nameof(request.Date));
                    }
                    dates.Add(parsedDate.Date);
                }

                if (!dates.Any())
                {
                    throw new ArgumentException("Danh sách ngày không hợp lệ.", nameof(request.Date));
                }

                // Parse TimeFrom và TimeTo từ request
                DateTime requestTimeFrom, requestTimeTo;
                if (!DateTime.TryParse(request.TimeFrom, out requestTimeFrom))
                {
                    throw new ArgumentException($"Định dạng TimeFrom không hợp lệ: {request.TimeFrom}");
                }
                if (!DateTime.TryParse(request.TimeTo, out requestTimeTo))
                {
                    throw new ArgumentException($"Định dạng TimeTo không hợp lệ: {request.TimeTo}");
                }

                var employees = await _unitOfWork.Employees.GetAllEmployeesAsync();
                var employessNameShort = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ShortName)
                    ?? new Dictionary<string, string>();
                var employeeDict = employees?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.FullName)
                    ?? new Dictionary<string, string>();
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();
                var shortOrgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.ShortOrganizationUnitName) ?? new Dictionary<string, string>();

                // Kiểm tra từng ngày trong danh sách
                foreach (var date in dates)
                {
                    // Thêm thời gian vào ngày
                    var requestTimeFromWithDate = date.Add(requestTimeFrom.TimeOfDay);
                    var requestTimeToWithDate = date.Add(requestTimeTo.TimeOfDay);

                    var existingSchedules = await _unitOfWork.WorkingSchedule
                        .AsQueryable()
                        .Include(x => x.WorkingScheduleC)
                        .Include(x => x.WorkingScheduleEP)
                        .Include(x => x.WorkingScheduleEPH)
                        .Include(x => x.WorkingScheduleOU)
                        .Where(x => x.Year == Convert.ToInt32(request.Year) &&
                                  x.Week == Convert.ToInt32(request.Week) &&
                                  x.Date == date &&
                                  x.OrganizationUnitId == Convert.ToInt32(request.OrganizationUnitId) &&
                                  (!request.Id.HasValue || x.Id != request.Id))
                        .ToListAsync();

                    if (existingSchedules.Any())
                    {
                        // Kiểm tra xem có nhân viên nào trong WorkingScheduleC trùng lặp không
                        foreach (var schedule in existingSchedules)
                        {
                            // Kiểm tra xem có trùng thời gian không
                            bool isTimeOverlap = false;
                            string overlapType = "";

                            if (requestTimeFromWithDate >= schedule.TimeFrom && requestTimeToWithDate <= schedule.TimeTo)
                            {
                                isTimeOverlap = true;
                                overlapType = "Lịch mới nằm hoàn toàn trong lịch cũ";
                            }

                            else if (requestTimeFromWithDate <= schedule.TimeFrom && requestTimeToWithDate >= schedule.TimeTo)
                            {
                                isTimeOverlap = true;
                                overlapType = "Lịch cũ nằm hoàn toàn trong lịch mới";
                            }

                            else if (requestTimeFromWithDate <= schedule.TimeFrom && requestTimeToWithDate > schedule.TimeFrom && requestTimeToWithDate <= schedule.TimeTo)
                            {
                                isTimeOverlap = true;
                                overlapType = "Lịch mới bắt đầu trước và kết thúc trong lịch cũ";
                            }

                            else if (requestTimeFromWithDate >= schedule.TimeFrom && requestTimeFromWithDate < schedule.TimeTo && requestTimeToWithDate >= schedule.TimeTo)
                            {
                                isTimeOverlap = true;
                                overlapType = "Lịch mới bắt đầu trong lịch cũ và kết thúc sau";
                            }

                            else if (requestTimeFromWithDate == schedule.TimeTo)
                            {
                                isTimeOverlap = false; 
                                overlapType = "Lịch mới bắt đầu đúng lúc lịch cũ kết thúc";
                            }

                            else if (requestTimeToWithDate == schedule.TimeFrom)
                            {
                                isTimeOverlap = false; 
                                overlapType = "Lịch mới kết thúc đúng lúc lịch cũ bắt đầu";
                            }

                            else
                            {
                                isTimeOverlap = false;
                                overlapType = "Không có sự chồng chéo thời gian";
                            }

                            if (!isTimeOverlap)
                            {
                                continue; 
                            }

                            var existingEmployeeIds = schedule.WorkingScheduleC?
                                .Select(c => c.EmployeeId.ToString().ToLower())
                                .ToList() ?? new List<string>();

                            var newEmployeeIds = request.WorkingScheduleC?
                                .Select(id => id.Trim().ToLower())
                                .ToList() ?? new List<string>();

                            // Nếu có bất kỳ nhân viên nào trùng lặp
                            var duplicateEmployeeIds = existingEmployeeIds
                                .Where(id => newEmployeeIds.Contains(id))
                                .ToList();

                            if (duplicateEmployeeIds.Any())
                            {
                                var scheduleData = new ScheduleData
                                {
                                    Id = schedule.Id,
                                    Classify = schedule.Classify,
                                    OrganizationUnitId = schedule.OrganizationUnitId,
                                    Year = schedule.Year,
                                    Week = schedule.Week,
                                    Date = schedule.Date,
                                    Time = schedule.Time,
                                    TimeFrom = schedule.TimeFrom,
                                    TimeTo = schedule.TimeTo,
                                    Place = schedule.Place,
                                    Contents = schedule.Contents,
                                    Note = schedule.Note,
                                    Message = schedule.Message,
                                    Member = schedule.Member,
                                    OrganizationUnitId_Chair = schedule.OrganizationUnitId_Chair,
                                    OrganizationUnitIdName = orgDict.TryGetValue(schedule.OrganizationUnitId.ToString(), out var name) ? name : "Không tìm thấy",
                                    ShortOrganizationUnitName = shortOrgDict.TryGetValue(schedule.OrganizationUnitId.ToString(), out var shortName) ? shortName : "Không tìm thấy",
                                    WorkingScheduleC = schedule.WorkingScheduleC?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                                        ?? new List<string>(),
                                                    WorkingScheduleEP = schedule.WorkingScheduleEP?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                                        ?? new List<string>(),
                                                    WorkingScheduleEPH = schedule.WorkingScheduleEPH?.Select(ws => ws.EmployeeId.ToString().Trim().ToLower()).ToList()
                                        ?? new List<string>(),
                                                    WorkingScheduleOU = schedule.WorkingScheduleOU?.Select(ws => ws.OrganizationUnitId.ToString()).ToList()
                                        ?? new List<string>(),
                                    MTEntityState = 0,
                                    CreatedDate = schedule.CreatedDate,
                                    ModifiedDate = schedule.ModifiedDate,
                                    ModifiedBy = schedule.ModifiedBy,
                                    CreatedBy = schedule.CreatedBy,
                                    SortOrder = schedule.SortOrder,
                                    Announced = schedule.Announced,
                                    WorkingScheduleCName = String.Join(", ", schedule.WorkingScheduleC?.Select(ws =>
                                        employeeDict.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleEPName = String.Join(", ", schedule.WorkingScheduleEP?.Select(ws =>
                                        employeeDict.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleEPHName = String.Join(", ", schedule.WorkingScheduleEPH?.Select(ws =>
                                        employeeDict.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleOUName = String.Join(", ", schedule.WorkingScheduleOU?.Select(ws =>
                                        orgDict.TryGetValue(ws.OrganizationUnitId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    OrganizationUnitId_ChairName = orgDict.TryGetValue(schedule.OrganizationUnitId_Chair.ToString(), out var nameChair) ? nameChair : "Không tìm thấy",
                                    WorkingScheduleOUName_Short = String.Join(", ", schedule.WorkingScheduleOU?.Select(ws =>
                                        shortOrgDict.TryGetValue(ws.OrganizationUnitId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleCName_Short = String.Join(", ", schedule.WorkingScheduleC?.Select(ws =>
                                        employessNameShort.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleEPName_Short = String.Join(", ", schedule.WorkingScheduleEP?.Select(ws =>
                                        employessNameShort.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                    WorkingScheduleEPHName_Short = String.Join(", ", schedule.WorkingScheduleEPH?.Select(ws =>
                                        employessNameShort.TryGetValue(ws.EmployeeId.ToString(), out var name) ? name : "Không tìm thấy") ?? new List<string>()),
                                };

                                // Thêm thông tin về loại trùng lặp vào Note
                                //scheduleData.Note = string.IsNullOrEmpty(scheduleData.Note) 
                                //    ? $"Trùng lặp: {overlapType}" 
                                //    : $"{scheduleData.Note}\nTrùng lặp: {overlapType}";

                                duplicateSchedules.Add(scheduleData);
                            }
                        }
                    }
                }

                return duplicateSchedules;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CheckDuplicateSchedule: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR: " + ex.ToString());
            }
        }
        #endregion
        #endregion
    }
}
