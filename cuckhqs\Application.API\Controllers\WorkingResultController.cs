﻿using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Azure.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class WorkingResultController : ControllerBase
    {
        private readonly IWorkingResultService _workingResultService;

        public WorkingResultController(IWorkingResultService workingResultService)
        {
            _workingResultService = workingResultService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchWorkingResultAsync([FromBody] SearchWorkingResultRequest request)
        {
            try
            {
                var response = await _workingResultService.SearchWorkingResultAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateWorkingResultAsync([FromBody] List<CreateWorkingResultRequest> request)
        {
            var response = await _workingResultService.CreateWorkingResultListAsync(request);
            return Ok(response);
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteWorkingResultAsync([FromBody] DeleteWorkingResultRequest request)
        {
            try
            {
                var response = await _workingResultService.DeleteWorkingResultAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPut("Update")]
        public async Task<IActionResult> UpdateWorkingResultAsync([FromBody] UpdateWorkingResultRequest request)
        {
            try
            {
                var response = await _workingResultService.UpdateWorkingResultAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
