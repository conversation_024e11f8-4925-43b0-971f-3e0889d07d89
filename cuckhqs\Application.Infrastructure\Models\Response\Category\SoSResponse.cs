﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class SoSResponse
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public bool? IsRoot { get; set; }
        public string? SoSCode { get; set; }
        public string? SoSName { get; set; }
        public string? Active { get; set; }
        public int? Year{ get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public bool? Expandable { get; set; }
        public List<SoSResponse> Children { get; set; } = new();


        public static Expression<Func<SoSEntity, SoSResponse>> Expression
        {
            get
            {
                return entity => new SoSResponse()
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    SoSCode = entity.SoSCode,
                    SoSName = entity.SoSName,
                    IsRoot = entity.IsRoot,
                    SortOrder = entity.SortOrder,
                    Active = entity.Active.ToString(),
                    IPAddress = entity.IPAddress,
                    Year = entity.Year,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static SoSResponse Create(SoSEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
