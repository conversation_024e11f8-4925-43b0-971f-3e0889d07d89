﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class AdvertisementRepository : GenericRepository<AdvertisementEntity, int>, IAdvertisementRepository
    {
        public AppDbContext Context { get; set; }

        public AdvertisementRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(AdvertisementEntity requestObject, AdvertisementEntity targetObject)
        {
            targetObject.AdvertisementCode = requestObject.AdvertisementCode;
            targetObject.AdvertisementName = requestObject.AdvertisementName;
            targetObject.Start = requestObject.Start;
            targetObject.StartHour = requestObject.StartHour;
            targetObject.StartMinute = requestObject.StartMinute;
            targetObject.Ends = requestObject.Ends;
            targetObject.EndsHour = requestObject.EndsHour;
            targetObject.EndsMinute = requestObject.EndsMinute;
            targetObject.Year = requestObject.Year;
            targetObject.Active = requestObject.Active;
            targetObject.ModifiedDate = requestObject.ModifiedDate;
            targetObject.ModifiedBy = requestObject.ModifiedBy;
        }
    }
}
