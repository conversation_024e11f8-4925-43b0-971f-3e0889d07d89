﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.OnDutyCommand;
using Application.Infrastructure.Models.Response;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IOnDutyCommandService
    {
        Task<BaseSearchResponse<OnDutyCommandResponse>> SearchOnDutyCommandAsync(SearchOnDutyCommandRequest request);
        Task<OnDutyCommandResponse> CreateOnDutyCommandAsync(CreateOnDutyCommandRequest request);
        Task<bool> UpdateOnDutyCommandAsync(UpdateOnDutyCommandRequest request);
    }
}
