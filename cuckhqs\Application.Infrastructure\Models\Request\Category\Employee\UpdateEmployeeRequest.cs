﻿using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Employee
{
    public class UpdateEmployeeRequest : CreateEmployeeRequest
    {
        public Guid Id { get; set; }
        public static Expression<Func<UpdateEmployeeRequest, EmployeeCEntity>> Expression
        {
            get
            {
                return s => new EmployeeCEntity
                {
                    FirstName = s.FirstName,
                    LastName = s.LastName,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Classify = s.Classify,
                    //   OrganizationUnitName = "",
                    EmployeeCode = s.EmployeeCode,
                    FullName = s.FullName,
                    RankId = s.RankId,
                    // RankName = s.RankName,
                    Gender = s.Gender,
                    //  Gender_Name = s.Gender == 1 ? "Nam" : "Nữ",
                    AcademicRankId = s.AcademicRankId,
                    YearOfAcademicRank = s.YearOfAcademicRank,
                    DegreeId = s.DegreeId,
                    //  Degree_Name = "",
                    YearOfDegree = s.YearOfDegree,
                    PositionId = s.PositionId,
                    // PositionName = "",
                    PositionType = s.PositionType,
                    PartyPositionId = s.PartyPositionId,
                    BirthDay = Convert.ToDateTime(s.BirthDay),
                    Owned = s.Owned,
                    Active = s.Active,
                    ActiveAccount = s.ActiveAccount,
                    IsAdministrator = s.IsAdministrator,
                    //  RankName = s.RankName
                    LongFullName = s.LongFullName,
                    BirthPlace = s.BirthPlace,
                    HomeLand = s.HomeLand,
                    NativeAddress = s.NativeAddress,
                    Tel = s.Tel,
                    HomeTel = s.HomeTel,
                    Mobile = s.Mobile,
                    Fax = s.Fax,
                    Email = s.Email,
                    OfficeAddress = s.OfficeAddress,
                    HomeAddress = s.HomeAddress,
                    Website = s.Website,
                    Description = s.Description,
                    IDNumber = s.IDNumber,
                    IssuedBy = s.IssuedBy,
                    DateBy = Convert.ToDateTime(s.DateBy),
                    AccountNumber = s.AccountNumber,
                    Bank = s.Bank,
                    Avatar = s.Avatar,
                    //SortOrder = s.SortOrder,
                    //CreatedDate = s.CreatedDate,
                    //ModifiedDate = s.ModifiedDate,
                    //IPAddress = s.IPAddress,
                    //ModifiedBy = s.ModifiedBy,
                    //CreatedBy = s.CreatedBy
                };
            }
        }

        public static EmployeeCEntity Create(UpdateEmployeeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
