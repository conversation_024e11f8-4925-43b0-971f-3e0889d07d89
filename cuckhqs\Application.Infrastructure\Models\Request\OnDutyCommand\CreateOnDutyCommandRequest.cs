﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Advertisement;

namespace Application.Infrastructure.Models.Request.OnDutyCommand
{
    public class CreateOnDutyCommandRequest
    {
        public int? Year { set; get; }
        public int? Week { set; get; }
        public DateTime? Date { set; get; }
        public Guid? EmployeeId_H { set; get; }
        public string? Description { set; get; }
        public bool? Active { set; get; } = true;
        public int? SortOrder { set; get; } = 0;

        public static Expression<Func<CreateOnDutyCommandRequest, OnDutyCommandEntity>> Expression
        {
            get
            {
                return entity => new OnDutyCommandEntity
                {
                    Year = entity.Year,
                    Week = entity.Week,
                    Date = entity.Date,
                    EmployeeId_H = entity.EmployeeId_H,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder
                };
            }
        }

        public static OnDutyCommandEntity Create(CreateOnDutyCommandRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
