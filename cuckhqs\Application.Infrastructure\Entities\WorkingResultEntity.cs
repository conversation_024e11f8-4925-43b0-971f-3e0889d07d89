﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingResultEntity :BaseEntity<int>
    {
        public int? OrganizationUnitId {  get; set; }    
        public string? Name {  get; set; }
        public int? Year { get; set; }
        public int? Week {  get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public int? Class {  get; set; }
        public int? Status { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder {  get; set; } 
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? Contents { get; set; }    
        public string? Contents1 { get; set; }    
        public string? Contents2 { get; set; }    
        public string? Contents3 { get; set; }
        public string? Note { get; set; }

    }
}
