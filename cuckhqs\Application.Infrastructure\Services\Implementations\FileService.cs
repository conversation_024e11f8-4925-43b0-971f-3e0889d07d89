﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class FileService : IFileService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AppDbContext _dbContext;
        private readonly IServiceProvider _serviceProvider;
        private readonly IEmployeeCService _employeeService;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public FileService(IUnitOfWork unitOfWork, AppDbContext dbContext, IServiceProvider serviceProvider, IEmployeeCService employeeService)
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
            _serviceProvider = serviceProvider;
            _employeeService = employeeService;
        }
        public async Task<FileUploadResult> UploadFileAsync(IFormFile file, IWebHostEnvironment env)
        {
            try
            {
                // Kiểm tra file
                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("No file provided.");
                }
                if (file.Length > 10 * 1024 * 1024) // Giới hạn 10MB
                {
                    throw new ArgumentException("File size exceeds 10MB.");
                }

                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".png", ".txt" };
                if (!allowedExtensions.Contains(fileExtension))
                {
                    throw new ArgumentException("Invalid file format. Allowed formats: pdf, doc, docx, jpg, png, txt.");
                }

                // Tạo thư mục uploads
                var uploadPath = Path.Combine(env.WebRootPath, "Uploads");
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                // Tạo tên file duy nhất dạng [Guid]_[tên gốc file].[đuôi file]
                var guid = Guid.NewGuid().ToString();
                var originalFileNameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName);
                var originalFileName = Path.GetFileName(file.FileName);
                var uniqueFileName = $"{guid}_{originalFileNameWithoutExtension}{fileExtension}";
                var filePath = Path.Combine(uploadPath, uniqueFileName);

                // Lưu file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Kết hợp FileName và OriginalFileName
                var combinedFileName = $"Uploads/{uniqueFileName}|{originalFileName}";

                return new FileUploadResult
                {
                    FileName = guid,
                    Contents = originalFileNameWithoutExtension,
                    FileType = fileExtension,
                    FileSize = file.Length / 1024.0
                };
            }
            catch (Exception ex)
            {
                log.Error($"Lỗi tại UploadFileAsync: {ex}");
                throw new Exception($"UNKNOWN_ERROR: {ex.Message}", ex);
            }
        }

        public async Task<FileDownloadResult> DownloadFileAsync(string filename, IWebHostEnvironment env)
        {
            try
            {
                // Kiểm tra filename
                if (string.IsNullOrWhiteSpace(filename))
                {
                    throw new ArgumentException("Filename cannot be empty.");
                }

                // Xây dựng đường dẫn file trong wwwroot/uploads
                var filePath = Path.Combine(env.WebRootPath, "Uploads", filename);
                if (!System.IO.File.Exists(filePath))
                {
                    throw new ArgumentException("File does not exist on server.");
                }

                // Đọc file vào stream
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

                // Lấy tên gốc file từ filename (loại bỏ phần [Guid]_)
                var originalFileName = filename;
                if (filename.Contains('_'))
                {
                    originalFileName = string.Join("_", filename.Split('_').Skip(1));
                }

                // Xác định Content-Type dựa trên đuôi file
                var fileExtension = Path.GetExtension(filename).ToLower();
                var contentType = fileExtension switch
                {
                    ".pdf" => "application/pdf",
                    ".doc" => "application/msword",
                    ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    ".jpg" => "image/jpeg",
                    ".png" => "image/png",
                    ".txt" => "text/plain",
                    _ => "application/octet-stream"
                };

                return new FileDownloadResult
                {
                    FileStream = fileStream,
                    FileName = filename,
                    OriginalFileName = originalFileName,
                    FileType = contentType
                };
            }
            catch (Exception ex)
            {
                log.Error($"Lỗi tại DownloadFileAsync: {ex}");
                throw new Exception($"UNKNOWN_ERROR: {ex.Message}", ex);
            }
        }

    }
}
