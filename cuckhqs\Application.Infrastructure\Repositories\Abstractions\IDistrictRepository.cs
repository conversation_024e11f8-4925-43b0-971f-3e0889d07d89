﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions
{
    public interface IDistrictRepository : IRepository<DistrictEntity, int>
    {
        Task<List<ProvinceEntity>> GetAllProvinceAsync();
        Task<List<DistrictEntity>> GetAllDistrictAsync();
    }
}
