﻿using Application.API;
using Application.Infrastructure.Entities.User;
using Microsoft.AspNetCore.Identity;
using Serilog;
using System.Globalization;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console(formatProvider: CultureInfo.InvariantCulture)
    .CreateBootstrapLogger();

Log.Information("Starting API resource server");

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Cấu hình CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowAngularApp",
            builder =>
            {
                builder.WithOrigins(
                    "http://localhost:4200",
                    "https://localhost:4200",
                    "http://*************:4200",
                    "https://*************:4200"
                )
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials();
            });
    });
    // Optional: Customize Kestrel
    builder.WebHost.ConfigureKestrel(serverOptions =>
    {
        serverOptions.ListenAnyIP(6583);  // HTTP
        //serverOptions.ListenAnyIP(6583, listenOptions =>
        //{
        //    listenOptions.UseHttps("localhost.pfx", "password123");
        //});
    }).UseIIS()            // Enables IIS integration
    .UseIISIntegration(); // Required for proper IIS request handling;

    builder.Host.UseSerilog((context, loggerConfiguration) => loggerConfiguration
        .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level}] {SourceContext}{NewLine}{Message:lj}{NewLine}{Exception}{NewLine}", formatProvider: CultureInfo.InvariantCulture)
        .ReadFrom.Configuration(context.Configuration));

    var app = builder
        .ConfigureServices()
        .ConfigurePipeline();
    

    // Đảm bảo CORS được áp dụng trước các middleware khác
    app.UseCors("AllowAngularApp");

    using (var scope = app.Services.CreateScope())
    {
        var services = scope.ServiceProvider;
        var loggerFactory = services.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("app");
        try
        {
            var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
            await Application.Infrastructure.Seeds.DefaultRoles.SeedAsync(userManager, roleManager);
            await Application.Infrastructure.Seeds.DefaultUsers.SeedBasicUserAsync(userManager, roleManager);
            await Application.Infrastructure.Seeds.DefaultUsers.SeedSuperAdminAsync(userManager, roleManager);
            logger.LogInformation("Finished Seeding Default Data");
            logger.LogInformation("Application Starting");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "An error occurred seeding the DB");
        }
    }
    await app.RunAsync();
}
catch (Exception ex) when (ex.GetType().Name is not "StopTheHostException" && ex.GetType().Name is not "HostAbortedException")
{
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    Log.Information("Shut down complete");
    await Log.CloseAndFlushAsync();
}