﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class WorkingResultService : IWorkingResultService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public WorkingResultService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
            
        }

        public async Task<BaseSearchResponse<WorkingResultResponse>> SearchWorkingResultAsync(SearchWorkingResultRequest request)
        {
            try
            {
                IQueryable<WorkingResultResponse> query = _unitOfWork.WorkingResult.AsQueryable().AsNoTracking()
                    .Where(x => (request.org == x.OrganizationUnitId.ToString()) && (request.year == x.Year.ToString()) && (request.week == x.Week.ToString()))
                    .Select(s => new WorkingResultResponse()
                    {
                        Id = s.Id,
                        Name = s.Name,
                        OrganizationUnitId = s.OrganizationUnitId,
                        DateFrom = s.DateFrom,
                        DateTo = s.DateTo,
                        Class = s.Class,
                        Contents = s.Contents,
                        Contents1 = s.Contents1,
                        Contents2 = s.Contents2,
                        Contents3 = s.Contents3,
                        Status = s.Status,
                        Active = s.Active,
                        Year = s.Year,
                        Week = s.Week,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        CreatedBy = s.CreatedBy,
                        Note = s.Note,
                    });

                return await BaseSearchResponse<WorkingResultResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                    log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                    throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<WorkingResultResponse> CreateWorkingResultAsync(CreateWorkingResultRequest request)
        {
            try
            {
                var entity = CreateWorkingResultRequest.Create(request);

                await _unitOfWork.WorkingResult.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return WorkingResultResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> CreateWorkingResultListAsync(List<CreateWorkingResultRequest> request)
        {
            try
            {
                foreach (var item in request)
                {
                    var entity = CreateWorkingResultRequest.Create(item);

                    await _unitOfWork.WorkingResult.AddAsync(entity);
                    await _unitOfWork.CommitChangesAsync();
                }
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteWorkingResultAsync(DeleteWorkingResultRequest request)
        {
            try
            {
                var record = await _unitOfWork.WorkingResult.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.WorkingResult.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateWorkingResultAsync(UpdateWorkingResultRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.WorkingResult.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateWorkingResultRequest.Create(request);

                await _unitOfWork.WorkingResult.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
