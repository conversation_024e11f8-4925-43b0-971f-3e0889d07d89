﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Repositories.Implementations;

public class WorkingResultSyntheticRepository: GenericRepository<WorkingResultSyntheticEntity,int>,IWorkingResultSyntheticRepository
{
    public AppDbContext Context { get; set; }

    public WorkingResultSyntheticRepository(AppDbContext dbContext) : base(dbContext)
    {
    }

    protected override void Update(WorkingResultSyntheticEntity requestObject,
        WorkingResultSyntheticEntity targetObject)
    {
        targetObject.CreatedDate = requestObject.CreatedDate;
        targetObject.CreatedBy = requestObject.CreatedBy;
        targetObject.IPAddress = requestObject.IPAddress;
        targetObject.ModifiedDate = requestObject.ModifiedDate;
        targetObject.ModifiedBy = requestObject.ModifiedBy;
        targetObject.Active = requestObject.Active;
        targetObject.Announced = requestObject.Announced;
        targetObject.Contents = requestObject.Contents;
        targetObject.OrganizationUnitId = requestObject.OrganizationUnitId;
        targetObject.SortOrder = requestObject.SortOrder;
        targetObject.Week = requestObject.Week;
        targetObject.Year = requestObject.Year;
    }
}