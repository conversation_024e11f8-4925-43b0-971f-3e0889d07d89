﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class JournalResponse
    {
        public int? Id { get; set; }
        public string? JournalCode { get; set; }
        public string? JournalName { get; set; }
        public string? ISSN { get; set; }
        public int? JournalTypeId { get; set; }
        public string? JournalTypeCode { get; set; }
        public string? JournalTypeName { get; set; }
        public string? JournalTypeId_AN { get; set; }
        public int? JournalGroupId { get; set; }
        public string? JournalGroupCode { get; set; }
        public string? JournalGroupName { get; set; }
        public string? JournalGroupId_AN { get; set; }
        public string? JournalSpecialCode { get; set; }
        public string? PublishingAgency { get; set; }
        public Decimal? PointFrom { get; set; }
        public Decimal? PointTo { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<JournalEntity, JournalResponse>> Expression
        {
            get
            {
                return entity => new JournalResponse()
                {
                    Id = entity.Id,
                    JournalCode = entity.JournalCode,
                    JournalName = entity.JournalName,
                    ISSN = entity.ISSN,
                    JournalTypeId = entity.JournalTypeId,
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeId_AN = entity.JournalTypeId_AN,
                    JournalGroupId = entity.JournalGroupId,
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupId_AN = entity.JournalGroupId_AN,
                    PublishingAgency = entity.PublishingAgency,
                    PointFrom = entity.PointFrom,
                    PointTo = entity.PointTo,
                    Description = entity.Description,
                    Active = entity.Active,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static JournalResponse Create(JournalEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
