﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class WorkingScheduleResultRepository : GenericRepository<WorkingScheduleResultEntity, int>, IWorkingScheduleResultRepository
    {
        public AppDbContext Context { get; set; }

        public WorkingScheduleResultRepository(AppDbContext Context) : base(Context)
        { }

        protected override void Update(WorkingScheduleResultEntity requestObject, WorkingScheduleResultEntity targetObject)
        {
            targetObject.WorkingScheduleResult_AttachDetail= requestObject.WorkingScheduleResult_AttachDetail;
            targetObject.Date = requestObject.Date;
            targetObject.Result = requestObject.Result;
        }

    }
}
