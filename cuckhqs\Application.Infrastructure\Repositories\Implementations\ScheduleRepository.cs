﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class ScheduleRepository : GenericRepository<ScheduleEntity, int>, IScheduleRepository
    {
        public AppDbContext Context { get; set; }

        public ScheduleRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(ScheduleEntity requestObject, ScheduleEntity targetObject)
        {
            targetObject.Name = requestObject.Name;
            targetObject.Active = requestObject.Active;
            targetObject.SortOrder = requestObject.SortOrder;
            targetObject.CreatedDate = requestObject.CreatedDate;
            targetObject.ModifiedDate = requestObject.ModifiedDate;
            targetObject.IPAddress = requestObject.IPAddress;
            targetObject.CreatedBy = requestObject.CreatedBy;
            targetObject.ModifiedBy = requestObject.ModifiedBy;
            targetObject.Rank = requestObject.Rank;
            targetObject.Date = requestObject.Date;
            targetObject.Year = requestObject.Year;
            targetObject.Week = requestObject.Week;

        }
    }
}
