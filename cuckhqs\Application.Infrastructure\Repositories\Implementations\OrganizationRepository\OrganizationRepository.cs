﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations.OrganizationRepository
{
    public class OrganizationUnitRepository  : GenericRepository<OrganizationUnitEntity, int>, IOrganizationUnitRepository
    {
        public AppDbContext _context { get; set; }

        public OrganizationUnitRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<OrganizationUnitEntity>> GetAllOrganiztionUnitAsync()
        {
            return await _context.OrganizationUnits.ToListAsync();
        }

        protected override void Update(OrganizationUnitEntity requestObject, OrganizationUnitEntity targetObject)
        {
            targetObject.OrganizationUnitCode = requestObject.OrganizationUnitCode;
            targetObject.Name = requestObject.Name;
            targetObject.ShortOrganizationUnitName = requestObject.ShortOrganizationUnitName;
            targetObject.ParentId = requestObject.ParentId;
            targetObject.ClassifyGroup = requestObject.ClassifyGroup;
            targetObject.TrainingMaterialCode = requestObject.TrainingMaterialCode;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}
