﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Fido2" Version="3.0.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <!--<PackageReference Include="Dapper" Version="2.1.66" />-->
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
	  <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="FluentValidation" Version="11.9.2" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="MailKit" Version="4.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Physical" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="OpenIddict.EntityFrameworkCore" Version="6.1.1" />
    <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0.2" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="9.103.8.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Middlewares\" />
    <Folder Include="Validations\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Repositories\Implementations\OrganizationUnitDbRepository.cs" />
    <Compile Remove="Repositories\Abstractions\IOrganizationUnitDbRepository.cs" />
    <Compile Remove="Entities\OrganizationUnitDbEntity.cs" />
    <Compile Remove="Configurations\OrganizationUnitDbConfiguration.cs" />
  </ItemGroup>
</Project>
