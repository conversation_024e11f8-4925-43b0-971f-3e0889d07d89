﻿using Application.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleIssueDetailEntity : BaseEntity<long>
    {
        public Guid EmployeeId { get; set; }
        public int? WorkingScheduleIssueId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public string? IPAddress { get; set; }
        public WorkingScheduleIssueEntity WorkingScheduleAnnounced { get; set; }
    }
}
