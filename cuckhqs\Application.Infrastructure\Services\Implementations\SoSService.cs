﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.SoS;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class SoSService : ISoSService
    {
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);
        private readonly IUnitOfWork _unitOfWork;

        public SoSService(
              IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<SoSResponse>> GetAllSoSAsync()
        {
            try
            {
                var result = await _unitOfWork.SoS.GetAllSoSAsync(1);

                var mappedResult = result.Select(entity => new SoSResponse
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    Active = entity.Active.ToString(),
                    SoSName = entity.SoSName,
                    SoSCode = entity.SoSCode,
                    SortOrder = entity.SortOrder,
                    Year = entity.Year,
                    IsRoot = entity.IsRoot,
                    IPAddress = entity.IPAddress,
                    CreatedBy = entity.CreatedBy,   
                    CreatedDate = entity.CreatedDate,
                    ModifiedBy = entity.ModifiedBy,
                    ModifiedDate = entity.ModifiedDate,
                }).ToList();

                return mappedResult;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw new Exception(e.Message);
            }
        }


        public async Task<List<SoSResponse>> GetAllSoSBuildTreeAsync()
        {
            try
            {
                var result = await GetAllSoSAsync();

                var tree = BuildSoSTree(result);

                return tree;

                //return result;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw new Exception(e.Message);
            }
        }


        public List<SoSResponse> BuildSoSTree(List<SoSResponse> flatList)
        {
            var lookup = flatList.ToDictionary(x => x.Id, x => x);

            List<SoSResponse> roots = new();

            foreach (var item in flatList)
            {
                if (item.ParentId.HasValue && lookup.ContainsKey(item.ParentId.Value))
                {
                    var parent = lookup[item.ParentId.Value];
                    parent.Children.Add(item);
                }
                else
                {
                    roots.Add(item);
                }
            }

            return roots;
        }

        public async Task<BaseSearchResponse<SoSResponse>> SearchSoSAsync(SearchSoSRequest request)
        {
            try
            {
                int? parent_id;
                var org = await _unitOfWork.SoS.GetAllSoSAsync(0);
                var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();
                if (request.ParentId == null || request.ParentId == 0)
                {
                    parent_id = org?.Where(e => e.IsRoot == true)
                             .Select(e => e.Id)
                             .FirstOrDefault() ?? 0;
                    
                }
                else parent_id = request.ParentId;
                IQueryable<SoSResponse> query = _unitOfWork.SoS
                                                               .AsQueryable()
                                                               .AsNoTracking()
                                                               .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                                                      x.SoSName.Contains(request.keyword) ||
                                                                     x.SoSCode.Contains(request.keyword)) &&
                                                                   x.ParentId == parent_id)
                                                               .Select(s => new SoSResponse()
                                                               {
                                                                   Id = s.Id,
                                                                   ParentId = s.ParentId,
                                                                   IsRoot = s.IsRoot,
                                                                   SoSName = s.SoSName,
                                                                   SoSCode = s.SoSCode,
                                                                   Year = s.Year,
                                                                   Active = s.Active == true ? "Có" : "Không",
                                                                   SortOrder = s.SortOrder,
                                                                   CreatedDate = s.CreatedDate,
                                                                   ModifiedDate = s.ModifiedDate,
                                                                   IPAddress = s.IPAddress,
                                                                   ModifiedBy = s.ModifiedBy,
                                                                   CreatedBy = s.CreatedBy,
                                                                   Expandable = parentIds.Contains(s.Id)
                                                               });
                return await BaseSearchResponse<SoSResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }

        public async Task<List<SoSResponse>> GetSoSById(int Id)
        {
            try
            {
                var org = await _unitOfWork.SoS.GetAllSoSAsync(0);
                var parentIds = org?.Select(x => x.ParentId).Where(id => id != null).Distinct().ToList() ?? new List<int?>();
                
                var Emm = await _unitOfWork.SoS
               .AsQueryable()
               .AsNoTracking()
               .Where(s => s.Id == Id)
               .ToListAsync();

                var Org_id = Emm.Select(s => new SoSResponse
                {
                    Id = s.Id,
                    ParentId = s.ParentId,
                    IsRoot = s.IsRoot,
                    SoSName = s.SoSName,
                    SoSCode = s.SoSCode,
                    Year = s.Year,
                    Active = s.Active.ToString(),
                    SortOrder = s.SortOrder,
                    CreatedDate = s.CreatedDate,
                    ModifiedDate = s.ModifiedDate,
                    IPAddress = s.IPAddress,
                    ModifiedBy = s.ModifiedBy,
                    CreatedBy = s.CreatedBy,
                    Expandable = parentIds.Contains(s.Id)
                }).ToList();

                return Org_id;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }

        public async Task<SoSResponse> CreateSoSAsync(CreateSoSRequest request)
        {
            try
            {
                var entity = CreateSoSRequest.Create(request);

                await _unitOfWork.SoS.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return SoSResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateSoSAsync(UpdateSoSRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.SoS.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateSoSRequest.Create(request);

                await _unitOfWork.SoS.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<string> DeleteSoSAsync(DeleteSoSRequest request)
        {
            try
            {
                var position = await _unitOfWork.SoS.GetAllSoSAsync(0);
                var hasParentReference = position.Any(o => o.ParentId.HasValue && request.Ids.Contains(o.ParentId.Value));
                if (hasParentReference)
                {
                    return "Xóa không thành công: Một hoặc nhiều chức vụ có chức vụ con";
                }
                var record = await _unitOfWork.SoS.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.SoS.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return "Xóa thành công";
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
