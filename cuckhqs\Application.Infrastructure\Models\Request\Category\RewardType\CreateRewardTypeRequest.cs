﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.RewardType
{
    public class CreateRewardTypeRequest
    {
        public string? RewardTypeCode { get; set; }
        public string? RewardTypeName { get; set; }
        public int? Class { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateRewardTypeRequest, RewardTypeEntity>> Expression
        {
            get
            {
                return entity => new RewardTypeEntity
                {
                    RewardTypeCode = entity.RewardTypeCode,
                    RewardTypeName = entity.RewardTypeName,
                    Class = entity.Class,
                    Active = entity.Active,
                };
            }
        }

        public static RewardTypeEntity Create(CreateRewardTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
