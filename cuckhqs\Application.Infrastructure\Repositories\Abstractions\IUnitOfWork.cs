﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.Category;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Repositories.Implementations;
using Application.Infrastructure.Services.Implementations;

namespace Application.Infrastructure.Repositories.Abstractions
{
    public interface IUnitOfWork
    {
        IDepartmentRepository Department { get; }
        IWorkingScheduleRepository WorkingSchedule { get; }
        IAdvertisementRepository Advertisement { get; }
        IWorkingResultRepository WorkingResult { get; }
        IOnDutyCommandRepository OnDutyCommand { get; }
        IWorkingResultSyntheticRepository WorkingResultSynthetic { get; }
        IWorkingScheduleAnnouncedRepository WorkingScheduleAnnounced { get; }
        IViewEmployeeRepository ViewEmployee { get; }
        IScheduleRepository Schedule { get; }
        #region DAnh mục
        IEmployeeRepository Employees { get; }
        IOrganizationUnitRepository Organizations { get; }
        IRankRepository Rank {  get; }
        IPositionRepository Position { get; }
        IAcademicRankRepository AcademicRank { get; }
        IDegreeRepository Degree { get; }
        ICountryRepository Country { get; }
        IProvinceRepository Province { get; }
        IDistrictRepository District { get; }
        IWardRepository Ward { get; }
        IDecisionLevelRepository DecisionLevel { get; }
        IRewardTypeRepository RewardType { get; }
        IDisciplineTypeRepository DisciplineType { get; }
        IEducationLevelRepository EducationLevel { get; }
        IJournalTypeRepository JournalType { get; }
        IJournalGroupRepository JournalGroup { get; }
        IJournalRepository Journal { get; }
        ISoSRepository SoS{ get; }
        IWorkingScheduleResultRepository WorkingScheduleResult { get; }

        #endregion


        #region Ít sửa
        void BeginTransaction();

        Task<int> SaveChangesAsync();
        Task CommitChangesAsync();

        void RollbackTransaction();
        #endregion
    }
}
