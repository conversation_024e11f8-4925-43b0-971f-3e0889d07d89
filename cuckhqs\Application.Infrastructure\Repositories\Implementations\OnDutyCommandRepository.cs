﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class OnDutyCommandRepository : GenericRepository<OnDutyCommandEntity, int>, IOnDutyCommandRepository
    {
        public AppDbContext Context { get; set; }

        public OnDutyCommandRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(OnDutyCommandEntity requestObject, OnDutyCommandEntity targetObject)
        {
            targetObject.EmployeeId_H = requestObject.EmployeeId_H;
        }
    }
}
