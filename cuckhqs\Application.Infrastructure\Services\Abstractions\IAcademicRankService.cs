﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IAcademicRankService
    {
        Task<BaseSearchResponse<AcademicRankResponse>> SearchAcademicRankAsync(SearchAcademicRankRequest request);
        Task<AcademicRankResponse> CreateAcademicRankAsync(CreateAcademicRankRequest request);
        Task<bool> UpdateAcademicRankAsync(UpdateAcademicRankRequest request);
        Task<bool> DeleteAcademicRankAsync(DeleteAcademicRankRequest request);
    }
}
