﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Application.Infrastructure.Configurations;

public class WorkingResultSyntheticConfiguration: IEntityTypeConfiguration<WorkingResultSyntheticEntity>
{
    public void Configure(EntityTypeBuilder<WorkingResultSyntheticEntity> builder)
    {
        builder.ToTable("WorkingResultSynthetic");
        builder.<PERSON><PERSON>ey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("Id");
        builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
        builder.Property(x => x.Year).HasColumnName("Year");
        builder.Property(x => x.Week).HasColumnName("Week");
        builder.Property(x => x.Contents).HasColumnName("Contents");
        builder.Property(x => x.Announced).HasColumnName("Announced");
        builder.Property(x => x.Active).HasColumnName("Active");
        builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
        builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
        builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
        builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
        builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
    } 
}