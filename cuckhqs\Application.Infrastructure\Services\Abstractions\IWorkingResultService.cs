﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IWorkingResultService
    {
        Task<BaseSearchResponse<WorkingResultResponse>> SearchWorkingResultAsync(SearchWorkingResultRequest request);
        Task<WorkingResultResponse> CreateWorkingResultAsync(CreateWorkingResultRequest request);
        Task<bool> CreateWorkingResultListAsync(List<CreateWorkingResultRequest> request);
        Task<bool> DeleteWorkingResultAsync(DeleteWorkingResultRequest request);
        Task<bool> UpdateWorkingResultAsync(UpdateWorkingResultRequest request);
    }
}
