﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using DocumentFormat.OpenXml.Vml.Office;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class AcademicRankRepository : GenericRepository<AcademicRankEntity, int>, IAcademicRankRepository
    {
        public AppDbContext Context { get; set; }

        public AcademicRankRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(AcademicRankEntity requestObject, AcademicRankEntity targetObject)
        {
            targetObject.AcademicRankCode = requestObject.AcademicRankCode;
            targetObject.AcademicRankName = requestObject.AcademicRankName;
            targetObject.AcademicRankShortName = requestObject.AcademicRankShortName;
            targetObject.Active = requestObject.Active;
            targetObject.Description = requestObject.Description;
        }
    }
}
