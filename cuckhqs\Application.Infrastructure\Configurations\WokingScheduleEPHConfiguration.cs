﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class WorkingScheduleEPHConfiguration : IEntityTypeConfiguration<WorkingScheduleEPHEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleEPHEntity> builder)
        {
            builder.ToTable("WorkingScheduleEPH");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.EmployeeId).HasColumnName("EmployeeId");
            builder.Property(x => x.WorkingScheduleId).HasColumnName("WorkingScheduleId");
        }
    }
}
