﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class JournalGroupRepository : GenericRepository<JournalGroupEntity, int>, IJournalGroupRepository
    {
        public AppDbContext Context { get; set; }

        public JournalGroupRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(JournalGroupEntity requestObject, JournalGroupEntity targetObject)
        {
            targetObject.JournalGroupCode = requestObject.JournalGroupCode;
            targetObject.JournalGroupName = requestObject.JournalGroupName;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}
