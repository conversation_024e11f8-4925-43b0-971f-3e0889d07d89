﻿using Application.Infrastructure.Models;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;

namespace Application.Infrastructure.Models.Response
{
    public class DepartmentResponse
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? NameShort { get; set; }
        public string? Code { get; set; }
        public int? SortOrder { get; set; }
        public string? Address { get; set; }
        public string? IsActive { get; set; }
        public int? ParentId { get; set; }
        public string? ParentIdCode { get; set; }
        public string? PhoneNumber { get; set; }
        public string? DepartmentType { get; set; }
        public string? Description { get; set; }
        public string? CreatedName { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Status { get; set; } 

        [NotMapped]
        public List<DepartmentEntity> Children { get; set; } = new List<DepartmentEntity>();


        public static Expression<Func<DepartmentEntity, DepartmentResponse>> Expression
        {
            get
            {
                return entity => new DepartmentResponse()
                {
                    Id = entity.Id,
                    Name = entity.Name,
                    NameShort = entity.NameShort,
                    Code = entity.Code,
                    SortOrder = entity.SortOrder,
                    Address = entity.Address,
                    ParentId = entity.ParentId,
                    ParentIdCode = entity.ParentIdCode,
                    PhoneNumber = entity.PhoneNumber,
                    DepartmentType = entity.DepartmentType,
                    Description = entity.Description,
                    IsActive = entity.IsActive,
                    CreatedBy = entity.CreatedBy,
                    CreatedDate = entity.CreatedDate,
                    UpdatedBy = entity.UpdatedBy,
                    UpdatedDate = entity.UpdatedDate,
                    Status = entity.Status,
                };
            }
        }

        public static DepartmentResponse Create(DepartmentEntity? request)
        {
            if (request == null) return null;

            return Expression.Compile().Invoke(request);
        }
    }
}