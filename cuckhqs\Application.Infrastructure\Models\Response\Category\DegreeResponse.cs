﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class DegreeResponse
    {
        public int? Id { get; set; }
        public string? DegreeCode { get; set; }
        public string? DegreeName { get; set; }
        public string? DegreeShortName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<DegreeEntity, DegreeResponse>> Expression
        {
            get
            {
                return entity => new DegreeResponse()
                {
                    Id = entity.Id,
                    DegreeCode = entity.DegreeCode,
                    DegreeName = entity.DegreeName,
                    DegreeShortName = entity.DegreeShortName,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static DegreeResponse Create(DegreeEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
