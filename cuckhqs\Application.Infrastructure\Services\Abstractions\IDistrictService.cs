﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.District;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IDistrictService
    {
        Task<BaseSearchResponse<DistrictResponse>> SearchDistrictAsync(SearchDistrictRequest request);
        Task<DistrictResponse> CreateDistrictAsync(CreateDistrictRequest request);
        Task<bool> UpdateDistrictAsync(UpdateDistrictRequest request);
        Task<string> DeleteDistrictAsync(DeleteDistrictRequest request);
    }
}
