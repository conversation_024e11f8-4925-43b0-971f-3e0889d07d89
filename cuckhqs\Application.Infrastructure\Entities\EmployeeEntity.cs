﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class EmployeeEntity : BaseEntity<Guid>
    {
        public string? FullName { get; set; }
        public string? ShortName { get; set; }
        
        public int? OrganizationUnitId { get; set; }
        public int? Classify { get; set; }
        public string? OrganizationUnitName { get; set; }
        public string? EmployeeCode { get; set; }
        public int? RankId { get; set; }
        public int? Gender{ get; set; }
        public int? AcademicRankId { get; set; }
        public int? YearOfAcademicRank { get; set; }
        public int? DegreeId { get; set; }
        public int? YearOfDegree { get; set; }
        public int? PositionId { get; set; }
        public string? PositionName {  get; set; }
        public short? PositionType { get; set; }
        public int? PartyPositionId { get; set; }
        public DateTime? BirthDay { get; set; }
        public bool? Owned {  get; set; }
        public bool? Active { get; set; }
        public bool? ActiveAccount { get; set;}
        public bool? IsAdministrator { get; set; }
        public string? RankName { get; set; }
    }
}
