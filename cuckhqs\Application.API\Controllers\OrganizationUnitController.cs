using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers;
[ApiController]
[Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
[Route("api/[controller]")]
public class OrganizationUnitController : ControllerBase
{
    private readonly IOrganizationUnitService _organizationUnitService;

    public OrganizationUnitController(IOrganizationUnitService organizationUnitService)
    {
        _organizationUnitService = organizationUnitService;
    }
    [HttpGet("get-all")]
    public async Task<IActionResult> GetAllOrganizationUnitAsync()
    {
        try
        {
            var response = await _organizationUnitService.GetAllOrganizationUnitAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("get-tree")]
    public async Task<IActionResult> GetOrganizationUnitTreeAsync()
    {
        try
        {
            var response = await _organizationUnitService.GetOrganizationUnitTreeAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("get-people-Receiced")]
    public async Task<IActionResult> OrganizationUnitCascadeEmployee()
    {
        try
        {
            var response = await _organizationUnitService.OrganizationUnitCascadeEmployee();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("Search")]
    public async Task<IActionResult> SearchOrganizationUnitAsync([FromBody] SearchOrganizationUnitRequest request)
    {
        try
        {
            var response = await _organizationUnitService.SearchOrganizationUnitAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("GetOrganizationUnitById")]
    public async Task<IActionResult> GetOrganizationUnitById([FromQuery] int Id)
    {
        try
        {
            var response = await _organizationUnitService.GetOrganizationUnitById(Id);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("Create")]
    public async Task<IActionResult> CreateOrganizationUnitAsync([FromBody] CreateOrganizationUnitRequest request)
    {
        var response = await _organizationUnitService.CreateOrganizationUnitAsync(request);
        return Ok(response);
    }

    [HttpPut("Update")]
    public async Task<IActionResult> UpdateWorkingResultAsync([FromBody] UpdateOrganizationUnitRequest request)
    {
        try
        {
            var response = await _organizationUnitService.UpdateOrganizationUnitAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("Delete")]
    public async Task<IActionResult> DeleteOrganizationUnitAsync([FromBody] DeleteOrganizationUnitRequest request)
    {
        try
        {
            var response = await _organizationUnitService.DeleteOrganizationUnitAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}