﻿using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class FileController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IFileService _fileService;
        private readonly string _filePath;


        public FileController(IFileService fileService, IWebHostEnvironment webHostEnvironment)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            _fileService = fileService;
            _webHostEnvironment = webHostEnvironment;
        }


        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            try
            {
                var result = await _fileService.UploadFileAsync(file, _webHostEnvironment);
                return Ok(new
                {
                    Success = true,
                    Data = result,
                    Message = "File uploaded successfully."
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = $"Internal server error: {ex.Message}"
                });
            }
        }

        [HttpGet("download-by-filename/{filename}")]
        public async Task<IActionResult> DownloadFile(string filename)
        {
            try
            {
                var result = await _fileService.DownloadFileAsync(filename, _webHostEnvironment);
                return File(result.FileStream, result.FileType, result.OriginalFileName);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Success = false, Message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Success = false, Message = $"Internal server error: {ex.Message}" });
            }
        }
    }
}
