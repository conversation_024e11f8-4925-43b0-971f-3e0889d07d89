﻿using Application.Infrastructure.Entities.OrganizationUnit;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities.User
{
    public class ApplicationUser : IdentityUser, IHaveOrganizationUnit
    {
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; }

        [Required]
        [StringLength(50)]
        public string LastName { get; set; }

        public DateTime? PasswordLastChanged { get; set; }
        public int? OrganizationUnitId { get; set; }
    }
}
