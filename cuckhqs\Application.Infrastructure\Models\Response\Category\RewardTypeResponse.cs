﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class RewardTypeResponse
    {
        public int? Id { get; set; }
        public string? RewardTypeCode { get; set; }
        public string? RewardTypeName { get; set; }
        public int? Class { get; set; }
        public int? Year { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<RewardTypeEntity, RewardTypeResponse>> Expression
        {
            get
            {
                return entity => new RewardTypeResponse()
                {
                    Id = entity.Id,
                    RewardTypeCode = entity.RewardTypeCode,
                    RewardTypeName = entity.RewardTypeName,
                    Class = entity.Class,
                    Year = entity.Year,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static RewardTypeResponse Create(RewardTypeEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
