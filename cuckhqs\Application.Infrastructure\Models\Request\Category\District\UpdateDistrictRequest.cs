﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.Degree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.District
{
    public class UpdateDistrictRequest : CreateDistrictRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateDistrictRequest, DistrictEntity>> Expression
        {
            get
            {
                return entity => new DistrictEntity
                {
                    Id = entity.Id,
                    ProvinceId = entity.ProvinceId,
                    DistrictCode = entity.DistrictCode,
                    DistrictName = entity.DistrictName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static DistrictEntity Create(UpdateDistrictRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
