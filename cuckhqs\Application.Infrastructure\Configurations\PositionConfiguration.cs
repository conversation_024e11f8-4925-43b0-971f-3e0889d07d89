﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class PositionConfiguration : IEntityTypeConfiguration<PositionEntity>
    {
        public void Configure(EntityTypeBuilder<PositionEntity> builder)
        {
            builder.ToTable("Position");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.ParentId).HasColumnName("ParentId");
            builder.Property(x => x.IsRoot).HasColumnName("IsRoot");
            builder.Property(x => x.PositionCode).HasColumnName("PositionCode");
            builder.Property(x => x.ParentCode).HasColumnName("ParentCode");
            builder.Property(x => x.Classify).HasColumnName("Classify");
            builder.Property(x => x.PositionName).HasColumnName("PositionName");
            builder.Property(x => x.ShortPositionName).HasColumnName("ShortPositionName");
            builder.Property(x => x.FullPositionName).HasColumnName("FullPositionName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}