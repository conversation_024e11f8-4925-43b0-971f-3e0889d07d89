﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Position;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions;

public interface IPositionService
{
    Task<List<PositionResponse>> GetAllPositionAsync();
    Task<List<PositionResponse>> GetAllPositionBuildTreeAsync();
    Task<BaseSearchResponse<PositionResponse>> SearchPositionAsync(SearchPositionRequest request);
    Task<List<PositionResponse>> GetPositionById(int Id);
    Task<PositionResponse> CreatePositionAsync(CreatePositionRequest request);

    Task<string> DeletePositionAsync(DeletePositionRequest request);
    Task<bool> UpdatePositionAsync(UpdatePositionRequest request);
}