#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@angular-devkit/build-angular/node_modules/postcss-loader/node_modules/semver/bin/semver.js" "$@"
  ret=$?
else 
  node  "$basedir/../@angular-devkit/build-angular/node_modules/postcss-loader/node_modules/semver/bin/semver.js" "$@"
  ret=$?
fi
exit $ret
