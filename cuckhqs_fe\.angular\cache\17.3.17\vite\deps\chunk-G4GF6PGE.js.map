{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus = false;\n  focused = false;\n  platformId = inject(PLATFORM_ID);\n  document = inject(DOCUMENT);\n  host = inject(ElementRef);\n  ngAfterContentChecked() {\n    // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n    if (this.autofocus === false) {\n      this.host.nativeElement.removeAttribute('autofocus');\n    } else {\n      this.host.nativeElement.setAttribute('autofocus', true);\n    }\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  ngAfterViewChecked() {\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  autoFocus() {\n    if (isPlatformBrowser(this.platformId) && this.autofocus) {\n      setTimeout(() => {\n        const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n        if (focusableElements.length === 0) {\n          this.host.nativeElement.focus();\n        }\n        if (focusableElements.length > 0) {\n          focusableElements[0].focus();\n        }\n        this.focused = true;\n      });\n    }\n  }\n  static ɵfac = function AutoFocus_Factory(t) {\n    return new (t || AutoFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AutoFocus,\n    selectors: [[\"\", \"pAutoFocus\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[pAutoFocus]',\n      standalone: true,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], null, {\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass AutoFocusModule {\n  static ɵfac = function AutoFocusModule_Factory(t) {\n    return new (t || AutoFocusModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AutoFocusModule,\n    imports: [AutoFocus],\n    exports: [AutoFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocusModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AutoFocus],\n      exports: [AutoFocus]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa,OAAO,WAAW;AAAA,EAC/B,WAAW,OAAO,QAAQ;AAAA,EAC1B,OAAO,OAAO,UAAU;AAAA,EACxB,wBAAwB;AAEtB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,KAAK,cAAc,gBAAgB,WAAW;AAAA,IACrD,OAAO;AACL,WAAK,KAAK,cAAc,aAAa,aAAa,IAAI;AAAA,IACxD;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,kBAAkB,KAAK,UAAU,KAAK,KAAK,WAAW;AACxD,iBAAW,MAAM;AACf,cAAM,oBAAoB,WAAW,qBAAqB,KAAK,MAAM,aAAa;AAClF,YAAI,kBAAkB,WAAW,GAAG;AAClC,eAAK,KAAK,cAAc,MAAM;AAAA,QAChC;AACA,YAAI,kBAAkB,SAAS,GAAG;AAChC,4BAAkB,CAAC,EAAE,MAAM;AAAA,QAC7B;AACA,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,GAAG;AAC1C,WAAO,KAAK,KAAK,YAAW;AAAA,EAC9B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,gBAAgB;AAAA,IACpG;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,wBAAwB;AAAA,EACxC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS;AAAA,IACnB,SAAS,CAAC,SAAS;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS;AAAA,MACnB,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}