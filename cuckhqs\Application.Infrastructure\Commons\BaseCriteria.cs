﻿using System.Linq.Expressions;

namespace Application.Infrastructure.Commons
{
    public class BaseCriteria
    {
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 20;
        public string? Sorts { get; set; } = null;
        public string? Orders { get; set; } = string.Empty;
        public string? QueryString { get; set; } = string.Empty ;
        //public Expression<Func<TModel, bool>>? Filter { get; set; }

        //protected virtual void ApplyCriteria(Expression<Func<TModel, bool>> cirteria)
        //{
        //    Filter = cirteria;
        //}
    }
}
