﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using DocumentFormat.OpenXml.Vml.Office;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class RankRepository : GenericRepository<RankEntity, int>, IRankRepository
    {
        public AppDbContext Context { get; set; }

        public RankRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(RankEntity requestObject, RankEntity targetObject)
        {
            targetObject.RankCode = requestObject.RankCode;
            targetObject.RankName = requestObject.RankName;
            targetObject.Description = requestObject.Description;
        }
    }
}
