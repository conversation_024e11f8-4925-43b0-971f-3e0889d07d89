﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class WorkingResultRepository : GenericRepository<WorkingResultEntity, int>, IWorkingResultRepository
    {
        public AppDbContext Context { get; set; }

        public WorkingResultRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(WorkingResultEntity requestObject, WorkingResultEntity targetObject)
        {
            targetObject.OrganizationUnitId = requestObject.OrganizationUnitId;
            targetObject.Name = requestObject.Name;
            targetObject.DateFrom = requestObject.DateFrom;
            targetObject.DateTo = requestObject.DateTo;
            targetObject.Class = requestObject.Class;
            targetObject.Contents = requestObject.Contents;
            targetObject.Status = requestObject.Status;
            targetObject.Active = requestObject.Active;
            targetObject.Year = requestObject.Year;
            targetObject.Week = requestObject.Week;
            targetObject.Active = requestObject.Active;
            targetObject.SortOrder = requestObject.SortOrder;
            targetObject.Contents1 = requestObject.Contents1;
            targetObject.Contents2 = requestObject.Contents2;
            targetObject.Contents3 = requestObject.Contents3;
            targetObject.Note = requestObject.Note;
        }
    }
}
