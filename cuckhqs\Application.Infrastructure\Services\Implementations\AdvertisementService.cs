﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class AdvertisementService : IAdvertisementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public AdvertisementService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<BaseSearchResponse<AdvertisementResponse>> SearchAdvertisementAsync(SearchAdvertisementRequest request)
        {
            try
            {
                IQueryable<AdvertisementResponse> query = _unitOfWork.Advertisement.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword)))
                    .Select(s => new AdvertisementResponse()
                    {
                        Id = s.Id,
                        AdvertisementCode = s.AdvertisementCode,
                        AdvertisementName = s.AdvertisementName,
                        Start = s.Start,
                        StartHour = s.StartHour,
                        StartMinute = s.StartMinute,
                        Ends = s.Ends,
                        EndsHour = s.EndsHour,
                        EndsMinute = s.EndsMinute,
                        Year = s.Year,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy
                    });

                return await BaseSearchResponse<AdvertisementResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<AdvertisementResponse> CreateAdvertisementAsync(CreateAdvertisementRequest request)
        {
            try
            {
                var s = CreateAdvertisementRequest.Create(request);

                await _unitOfWork.Advertisement.AddAsync(s);
                await _unitOfWork.CommitChangesAsync();
                return AdvertisementResponse.Create(s);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<AdvertisementResponse> UpdateAdvertisementAsync(UpdateAdvertisementRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Advertisement.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateAdvertisementRequest.Create(request);

                await _unitOfWork.Advertisement.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return AdvertisementResponse.Create(updateRecord);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteAdvertisementAsync(DeleteAdvertisementRequest request)
        {
            try
            {
                var record = await _unitOfWork.Advertisement.AsQueryable().Where(records => request.AdvertisementIds.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Advertisement.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
