﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.DisciplineType;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IDisciplineTypeService
    {
        Task<BaseSearchResponse<DisciplineTypeResponse>> SearchDisciplineTypeAsync(SearchDisciplineTypeRequest request);
        Task<DisciplineTypeResponse> CreateDisciplineTypeAsync(CreateDisciplineTypeRequest request);
        Task<bool> UpdateDisciplineTypeAsync(UpdateDisciplineTypeRequest request);
        Task<bool> DeleteDisciplineTypeAsync(DeleteDisciplineTypeRequest request);
    }
}
