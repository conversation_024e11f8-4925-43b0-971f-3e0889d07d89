﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class ProvinceResponse
    {
        public int? Id { get; set; }
        public string? ProvinceCode { get; set; }
        public string? ProvinceName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<ProvinceEntity, ProvinceResponse>> Expression
        {
            get
            {
                return entity => new ProvinceResponse()
                {
                    Id = entity.Id,
                    ProvinceCode = entity.ProvinceCode,
                    ProvinceName = entity.ProvinceName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static ProvinceResponse Create(ProvinceEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
