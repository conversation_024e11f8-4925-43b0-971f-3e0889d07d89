﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Constants
{
    public static class Permissions
    {
        public static List<string> GeneratePermissionsForModule(string module)
        {
            return new List<string>()
        {
            $"Permission.{module}.Create",
            $"Permission.{module}.View",
            $"Permission.{module}.Edit",
            $"Permission.{module}.Delete",
        };
        }
        public static class Products
        {
            public const string View = "Permission.Products.View";
            public const string Create = "Permission.Products.Create";
            public const string Edit = "Permission.Products.Edit";
            public const string Delete = "Permission.Products.Delete";
        }
        public static class Users
        {
            public const string View = "Permission.Users.View";
            public const string Create = "Permission.Users.Create";
            public const string Edit = "Permission.Users.Edit";
            public const string Delete = "Permission.Users.Delete";
        }

        public static class Roles
        {
            public const string View = "Permission.Roles.View";
            public const string Create = "Permission.Roles.Create";
            public const string Edit = "Permission.Roles.Edit";
            public const string Delete = "Permission.Roles.Delete";
        }
    }
}
