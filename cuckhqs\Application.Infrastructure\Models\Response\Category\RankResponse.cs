﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class RankResponse
    {
        public int? Id { get; set; }
        public string? RankCode { get; set; }
        public string? RankName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<RankEntity, RankResponse>> Expression
        {
            get
            {
                return entity => new RankResponse()
                {
                    Id = entity.Id,
                    RankCode = entity.RankCode,
                    RankName = entity.RankName,
                    Description = entity.Description,
                    Active = true,
                };
            }
        }

        public static RankResponse Create(RankEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
