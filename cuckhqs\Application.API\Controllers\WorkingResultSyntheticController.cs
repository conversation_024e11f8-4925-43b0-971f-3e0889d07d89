﻿using Application.Infrastructure.Models.Request.WorkingResultSynthetic;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace Application.API.Controllers;

[ApiController]
[Route("api/wrs")]
public class WorkingResultSyntheticController : ControllerBase
{
    private readonly IWorkingResultSyntheticService _workingResultSyntheticService;

    public WorkingResultSyntheticController(IWorkingResultSyntheticService workingResultSyntheticService)
    {
        _workingResultSyntheticService = workingResultSyntheticService;
    }

    [HttpPost("search")]
    public async Task<IActionResult> SearchWorkingResultSyntheticAsync(
        [FromBody] SearchWorkingResultSyntheticRequest request)
    {
        try
        {
            var response = await _workingResultSyntheticService.SearchWorkingResultSyntheticAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
    [HttpPost("create")]
    public async Task<IActionResult> CreateWorkingResultSyntheticAsync(
        [FromBody] CreateWorkingResultSyntheticRequest request)
    {
        try
        {
            var response = await _workingResultSyntheticService.CreateWorkingResultSyntheticAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
    [HttpPut("update")]
    public async Task<IActionResult> UpdateWorkingResultSyntheticAsync(
        [FromBody] UpdateWorkingResultSyntheticRequest request)
    {
        try
        {
            var response = await _workingResultSyntheticService.UpdateWorkingResultSyntheticAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
    [HttpDelete("delete")]
    public async Task<IActionResult> DeleteWorkingResultSyntheticAsync(
        [FromBody] DeleteWorkingResultSyntheticRequest request)
    {
        try
        {
            var response = await _workingResultSyntheticService.DeleteWorkingResultSyntheticAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}