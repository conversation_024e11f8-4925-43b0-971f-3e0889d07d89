﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class JournalTypeRepository : GenericRepository<JournalTypeEntity, int>, IJournalTypeRepository
    {
        public AppDbContext Context { get; set; }

        public JournalTypeRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(JournalTypeEntity requestObject, JournalTypeEntity targetObject)
        {
            targetObject.JournalTypeCode = requestObject.JournalTypeCode;
            targetObject.JournalTypeName = requestObject.JournalTypeName;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}
