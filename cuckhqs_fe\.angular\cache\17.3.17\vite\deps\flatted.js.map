{"version": 3, "sources": ["../../../../../node_modules/flatted/esm/index.js"], "sourcesContent": ["/// <reference types=\"../types/index.d.ts\" />\n\n// (c) 2020-present <PERSON>\n\nconst {parse: $parse, stringify: $stringify} = JSON;\nconst {keys} = Object;\n\nconst Primitive = String;   // it could be Number\nconst primitive = 'string'; // it could be 'number'\n\nconst ignore = {};\nconst object = 'object';\n\nconst noop = (_, value) => value;\n\nconst primitives = value => (\n  value instanceof Primitive ? Primitive(value) : value\n);\n\nconst Primitives = (_, value) => (\n  typeof value === primitive ? new Primitive(value) : value\n);\n\nconst revive = (input, parsed, output, $) => {\n  const lazy = [];\n  for (let ke = keys(output), {length} = ke, y = 0; y < length; y++) {\n    const k = ke[y];\n    const value = output[k];\n    if (value instanceof Primitive) {\n      const tmp = input[value];\n      if (typeof tmp === object && !parsed.has(tmp)) {\n        parsed.add(tmp);\n        output[k] = ignore;\n        lazy.push({k, a: [input, parsed, tmp, $]});\n      }\n      else\n        output[k] = $.call(output, k, tmp);\n    }\n    else if (output[k] !== ignore)\n      output[k] = $.call(output, k, value);\n  }\n  for (let {length} = lazy, i = 0; i < length; i++) {\n    const {k, a} = lazy[i];\n    output[k] = $.call(output, k, revive.apply(null, a));\n  }\n  return output;\n};\n\nconst set = (known, input, value) => {\n  const index = Primitive(input.push(value) - 1);\n  known.set(value, index);\n  return index;\n};\n\n/**\n * Converts a specialized flatted string into a JS value.\n * @param {string} text\n * @param {(this: any, key: string, value: any) => any} [reviver]\n * @returns {any}\n */\nexport const parse = (text, reviver) => {\n  const input = $parse(text, Primitives).map(primitives);\n  const value = input[0];\n  const $ = reviver || noop;\n  const tmp = typeof value === object && value ?\n              revive(input, new Set, value, $) :\n              value;\n  return $.call({'': tmp}, '', tmp);\n};\n\n/**\n * Converts a JS value into a specialized flatted string.\n * @param {any} value\n * @param {((this: any, key: string, value: any) => any) | (string | number)[] | null | undefined} [replacer]\n * @param {string | number | undefined} [space]\n * @returns {string}\n */\nexport const stringify = (value, replacer, space) => {\n  const $ = replacer && typeof replacer === object ?\n            (k, v) => (k === '' || -1 < replacer.indexOf(k) ? v : void 0) :\n            (replacer || noop);\n  const known = new Map;\n  const input = [];\n  const output = [];\n  let i = +set(known, input, $.call({'': value}, '', value));\n  let firstRun = !i;\n  while (i < input.length) {\n    firstRun = true;\n    output[i] = $stringify(input[i++], replace, space);\n  }\n  return '[' + output.join(',') + ']';\n  function replace(key, value) {\n    if (firstRun) {\n      firstRun = !firstRun;\n      return value;\n    }\n    const after = $.call(this, key, value);\n    switch (typeof after) {\n      case object:\n        if (after === null) return after;\n      case primitive:\n        return known.get(after) || set(known, input, after);\n    }\n    return after;\n  }\n};\n\n/**\n * Converts a generic value into a JSON serializable object without losing recursion.\n * @param {any} value\n * @returns {any}\n */\nexport const toJSON = value => $parse(stringify(value));\n\n/**\n * Converts a previously serialized object with recursion into a recursive one.\n * @param {any} value\n * @returns {any}\n */\nexport const fromJSON = value => parse($stringify(value));\n"], "mappings": ";;;AAIA,IAAM,EAAC,OAAO,QAAQ,WAAW,WAAU,IAAI;AAC/C,IAAM,EAAC,KAAI,IAAI;AAEf,IAAM,YAAY;AAClB,IAAM,YAAY;AAElB,IAAM,SAAS,CAAC;AAChB,IAAM,SAAS;AAEf,IAAM,OAAO,CAAC,GAAG,UAAU;AAE3B,IAAM,aAAa,WACjB,iBAAiB,YAAY,UAAU,KAAK,IAAI;AAGlD,IAAM,aAAa,CAAC,GAAG,UACrB,OAAO,UAAU,YAAY,IAAI,UAAU,KAAK,IAAI;AAGtD,IAAM,SAAS,CAAC,OAAO,QAAQ,QAAQ,MAAM;AAC3C,QAAM,OAAO,CAAC;AACd,WAAS,KAAK,KAAK,MAAM,GAAG,EAAC,OAAM,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK;AACjE,UAAM,IAAI,GAAG,CAAC;AACd,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,iBAAiB,WAAW;AAC9B,YAAM,MAAM,MAAM,KAAK;AACvB,UAAI,OAAO,QAAQ,UAAU,CAAC,OAAO,IAAI,GAAG,GAAG;AAC7C,eAAO,IAAI,GAAG;AACd,eAAO,CAAC,IAAI;AACZ,aAAK,KAAK,EAAC,GAAG,GAAG,CAAC,OAAO,QAAQ,KAAK,CAAC,EAAC,CAAC;AAAA,MAC3C;AAEE,eAAO,CAAC,IAAI,EAAE,KAAK,QAAQ,GAAG,GAAG;AAAA,IACrC,WACS,OAAO,CAAC,MAAM;AACrB,aAAO,CAAC,IAAI,EAAE,KAAK,QAAQ,GAAG,KAAK;AAAA,EACvC;AACA,WAAS,EAAC,OAAM,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChD,UAAM,EAAC,GAAG,EAAC,IAAI,KAAK,CAAC;AACrB,WAAO,CAAC,IAAI,EAAE,KAAK,QAAQ,GAAG,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,EACrD;AACA,SAAO;AACT;AAEA,IAAM,MAAM,CAAC,OAAO,OAAO,UAAU;AACnC,QAAM,QAAQ,UAAU,MAAM,KAAK,KAAK,IAAI,CAAC;AAC7C,QAAM,IAAI,OAAO,KAAK;AACtB,SAAO;AACT;AAQO,IAAM,QAAQ,CAAC,MAAM,YAAY;AACtC,QAAM,QAAQ,OAAO,MAAM,UAAU,EAAE,IAAI,UAAU;AACrD,QAAM,QAAQ,MAAM,CAAC;AACrB,QAAM,IAAI,WAAW;AACrB,QAAM,MAAM,OAAO,UAAU,UAAU,QAC3B,OAAO,OAAO,oBAAI,OAAK,OAAO,CAAC,IAC/B;AACZ,SAAO,EAAE,KAAK,EAAC,IAAI,IAAG,GAAG,IAAI,GAAG;AAClC;AASO,IAAM,YAAY,CAAC,OAAO,UAAU,UAAU;AACnD,QAAM,IAAI,YAAY,OAAO,aAAa,SAChC,CAAC,GAAG,MAAO,MAAM,MAAM,KAAK,SAAS,QAAQ,CAAC,IAAI,IAAI,SACrD,YAAY;AACvB,QAAM,QAAQ,oBAAI;AAClB,QAAM,QAAQ,CAAC;AACf,QAAM,SAAS,CAAC;AAChB,MAAI,IAAI,CAAC,IAAI,OAAO,OAAO,EAAE,KAAK,EAAC,IAAI,MAAK,GAAG,IAAI,KAAK,CAAC;AACzD,MAAI,WAAW,CAAC;AAChB,SAAO,IAAI,MAAM,QAAQ;AACvB,eAAW;AACX,WAAO,CAAC,IAAI,WAAW,MAAM,GAAG,GAAG,SAAS,KAAK;AAAA,EACnD;AACA,SAAO,MAAM,OAAO,KAAK,GAAG,IAAI;AAChC,WAAS,QAAQ,KAAKA,QAAO;AAC3B,QAAI,UAAU;AACZ,iBAAW,CAAC;AACZ,aAAOA;AAAA,IACT;AACA,UAAM,QAAQ,EAAE,KAAK,MAAM,KAAKA,MAAK;AACrC,YAAQ,OAAO,OAAO;AAAA,MACpB,KAAK;AACH,YAAI,UAAU,KAAM,QAAO;AAAA,MAC7B,KAAK;AACH,eAAO,MAAM,IAAI,KAAK,KAAK,IAAI,OAAO,OAAO,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;AAOO,IAAM,SAAS,WAAS,OAAO,UAAU,KAAK,CAAC;AAO/C,IAAM,WAAW,WAAS,MAAM,WAAW,KAAK,CAAC;", "names": ["value"]}