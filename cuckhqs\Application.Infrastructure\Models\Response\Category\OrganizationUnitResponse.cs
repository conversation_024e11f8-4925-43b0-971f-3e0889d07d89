﻿using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class OrganizationUnitResponse
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public bool IsRoot { get; set; }
        public string? OrganizationUnitCode { get; set; }
        public string? ParentCode { get; set; }
        public short? Classify { get; set; }
        public int? ClassifyGroup { get; set; }
        public string? TrainingMaterialCode { get; set; }
        public string? OrganizationUnitName { get; set; }
        //  public string? OrganizationUnitName { get; set; }
        public string? ShortOrganizationUnitName { get; set; }
        public string? Tel { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? Website { get; set; }
        public string? Director { get; set; }
        public string? AccountNumber { get; set; }
        public string? BankName { get; set; }
        public string? Description { get; set; }
        public string? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public string? FullOrganizationUnitName { get; set; }
        public bool? Expandable { get; set; }

        public static Expression<Func<OrganizationUnitEntity, OrganizationUnitResponse>> Expression
        {
            get
            {
                return entity => new OrganizationUnitResponse()
                {
                    Id = entity.Id,
                    OrganizationUnitCode = entity.OrganizationUnitCode,
                    OrganizationUnitName = entity.Name,
                    ShortOrganizationUnitName = entity.ShortOrganizationUnitName,
                    ParentId = entity.ParentId,
                    ClassifyGroup = entity.ClassifyGroup,
                    TrainingMaterialCode = entity.TrainingMaterialCode,
                    Description = entity.Description,
                    Active = entity.Active.ToString()
                };
            }
        }

        public static OrganizationUnitResponse Create(OrganizationUnitEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
