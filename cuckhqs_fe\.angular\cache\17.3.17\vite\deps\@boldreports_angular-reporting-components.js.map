{"version": 3, "sources": ["../../../../../node_modules/@boldreports/angular-reporting-components/fesm2020/boldreports-angular-reporting-components.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, Input, Output, NgModule } from '@angular/core';\nexport { ContentChild, Type, forwardRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nlet currentTemplateElement;\nlet firstVal = {};\n/** Internal Helpers */\nclass Utils {\n  static IterateAndGetChanges(obj) {\n    if (ej.isNullOrUndefined(obj.tags) || obj.tags.length === 0) {\n      return null;\n    }\n    let res = {};\n    for (let i = 0, tags = obj.tags; i < tags.length; i++) {\n      let tag = tags[i],\n        tagElement = obj['tag_' + tag.replace(/\\./g, '_')];\n      if (!ej.isNullOrUndefined(tagElement) && tagElement.hasChanges) {\n        res[tag] = tagElement.getChangesAndReset();\n      }\n    }\n    return res;\n  }\n}\nUtils.ɵfac = function Utils_Factory(t) {\n  return new (t || Utils)();\n};\nUtils.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Utils,\n  factory: Utils.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Utils, [{\n    type: Injectable\n  }], null, null);\n})();\n// tslint:disable-next-line:max-line-length\nclass BoldReportComponents {\n  // tslint:disable-next-line:max-line-length\n  constructor(controlName, el, cdRef, tags, ejIterableDiffers, _ejKeyValueDiffers) {\n    this.controlName = controlName;\n    this.el = el;\n    this.cdRef = cdRef;\n    this.tags = tags;\n    this.ejIterableDiffers = ejIterableDiffers;\n    this._ejKeyValueDiffers = _ejKeyValueDiffers;\n    this.model = {};\n    this.outputs = [];\n    this.inputs = [];\n    this.twoways = [];\n    //        this.__shadow = this.dom.getShadowRoot(this.el.nativeElement);\n    this.firstCheck = true;\n    this.ejIterable = this.ejIterableDiffers.find([]).create(null);\n    this.ejKeyValueDif = _ejKeyValueDiffers.find([]).create();\n  }\n  static bindAndRaiseEvent(instance, model, event) {\n    if (!event.startsWith('model.')) {\n      // && instance[event]['observers'].length > 1) {\n      let originalEvt = event.startsWith('bold') ? event.substr(4) : event;\n      model[originalEvt] = function (params) {\n        instance[event + '_output']['emit'](params);\n      };\n    }\n  }\n  createTwoways(twoways) {\n    if (!twoways) {\n      return;\n    }\n    let model = this.model;\n    for (let i = 0; i < twoways.length; i++) {\n      let element = twoways[i].replace(/\\_/g, '.');\n      ej.createObject(element + 'Change', this[twoways[i] + '_twoChange'], model);\n      ej.createObject(element, this.addTwoways(element), model);\n    }\n  }\n  addTwoways(prop) {\n    let model = this.model,\n      value = firstVal; // , originalProp = prop.replace(/-/g, '.');\n    return function (newVal, isApp) {\n      if (value === firstVal) {\n        value = ej.getObject(prop + '_two', model);\n        if (value === undefined) {\n          value = ej.getObject(prop, this === undefined || this.defaults === undefined ? {} : this.defaults);\n        }\n      }\n      if (newVal === undefined) {\n        return value;\n      }\n      if (value === newVal) {\n        return;\n      }\n      value = newVal;\n      if (!isApp) {\n        ej.createObject(prop + '_two', newVal, model);\n        ej.getObject(prop + 'Change', model).emit(newVal);\n      }\n    };\n  }\n  ngOnInit() {\n    for (let key in this) {\n      if (key.indexOf('_input') != -1) this.inputs.push(key);\n      if (key.indexOf('_output') != -1) this.outputs.push(key);\n      if (key.indexOf('_two') != -1 && key.indexOf('_twoChange') == -1) this.twoways.push(key.replace('_two', ''));\n    }\n    if (ej.isNullOrUndefined(this['options'])) {\n      this.createTwoways(this.twoways);\n    }\n  }\n  ngAfterContentInit() {\n    this.firstCheck = false;\n    let model = this.model,\n      events = this.outputs;\n    if (events) {\n      for (let i = 0; i < events.length; i++) {\n        let event = events[i].replace('_output', '');\n        BoldReportComponents.bindAndRaiseEvent(this, model, event);\n      }\n    }\n    if (ej.isNullOrUndefined(this['options'])) {\n      for (let i = 0; i < this.tags.length; i++) {\n        let element = this.tags[i],\n          item = this['tag_' + element.replace(/\\./g, '_')];\n        if (!ej.isNullOrUndefined(item)) {\n          ej.createObject(element, item.getList(), this.model);\n        }\n      }\n      for (let i = 0; i < this.inputs.length; i++) {\n        let property = this.inputs[i];\n        let modelProperty = this.inputs[i].replace('_input', '');\n        if (this[property] != null) {\n          if (modelProperty.indexOf('_') == -1) {\n            if (this.model[modelProperty]) {\n              $.extend(true, this.model[modelProperty], this[property]);\n            } else {\n              this.model[modelProperty] = this[property];\n            }\n          } else if (modelProperty.indexOf('_two') == -1) {\n            let tempObj = {};\n            let key = modelProperty.replace(/\\_/g, '.');\n            ej.createObject(key, this[property], tempObj);\n            let rootProp = key.split('.')[0];\n            if (this.model[rootProp] == undefined) this.model[rootProp] = {};\n            $.extend(true, this.model[rootProp], tempObj[rootProp]);\n          }\n        }\n      }\n      for (let i = 0; i < this.twoways.length; i++) {\n        let twoway = this.twoways[i];\n        let twowayProperty = twoway + '_two';\n        if (this[twowayProperty] != null) {\n          if (twoway.indexOf('_') == -1) {\n            this.model[twowayProperty] = this[twowayProperty];\n          } else {\n            let tempObj = {};\n            let key = twoway.replace(/\\_/g, '.') + '_two';\n            ej.createObject(key, this[twowayProperty], tempObj);\n            let rootProp = twowayProperty.split('_')[0];\n            $.extend(true, this.model[rootProp], tempObj[rootProp]);\n          }\n        }\n      }\n    } else this.model = jQuery.extend(this.model, this['options']);\n  }\n  ngDoCheck() {\n    if (ej.isNullOrUndefined(this['options'])) {\n      this.twoways.forEach(element => {\n        if (this[element + '_two'] instanceof Array) {\n          let changes = this.ejIterable.diff(this[element + '_two']);\n          if (changes) {\n            let ngChanges = {};\n            if (this.widget != undefined) {\n              ngChanges = this.getTwowayChanges(changes.collection, ngChanges, element.replace(/\\_/g, '.'));\n              ej.createObject(element.replace(/\\_/g, '.') + '.two', changes.collection, ngChanges);\n              this.widget['setModel'](ngChanges, $.isPlainObject(ngChanges));\n            }\n          }\n        }\n      });\n    } else {\n      let changes = this.ejKeyValueDif.diff(this['options']);\n      if (changes) {\n        if (this.widget != undefined) {\n          var ngchanges = {};\n          changes.forEachChangedItem(changedprop => {\n            ej.createObject(changedprop.key, changedprop.currentValue, ngchanges);\n          });\n          this.widget['setModel'](ngchanges, $.isPlainObject(ngchanges));\n        }\n      }\n    }\n  }\n  ngAfterViewInit() {\n    let nativeElement = this.isEditor ? $(this.el.nativeElement.children) : $(this.el.nativeElement);\n    let controlName = this.lowerCaseFirstLetter(this.controlName);\n    this.widget = $(nativeElement)[controlName](this.model)[controlName]('instance');\n  }\n  lowerCaseFirstLetter(string) {\n    return string[0].toLowerCase() + string.slice(1);\n  }\n  ngOnChanges(changes) {\n    if (this.firstCheck) {\n      return;\n    }\n    let ngChanges = {};\n    if (ej.isNullOrUndefined(this['options'])) {\n      for (let key in changes) {\n        let element = changes[key];\n        if (element.previousValue === element.currentValue) {\n          break;\n        }\n        key = key.replace('_input', '').replace(/\\_/g, '.');\n        if (key.endsWith('.two')) {\n          let oKey = key.replace('.two', '');\n          ngChanges = this.getTwowayChanges(element.currentValue, ngChanges, oKey);\n        }\n        ej.createObject(key, element.currentValue, ngChanges);\n      }\n      this.widget['setModel'](ngChanges, $.isPlainObject(ngChanges));\n    }\n  }\n  getTwowayChanges(value, ngChanges, prop) {\n    let valFn = ej.getObject(prop, this.widget['model']);\n    valFn(value, true);\n    ej.createObject(prop, valFn, ngChanges);\n    return ngChanges;\n  }\n  ngAfterContentChecked() {\n    // TODO: ChangeDetection Third/Multi level\n    let changes = Utils.IterateAndGetChanges(this);\n    for (let key in changes) {\n      if (changes.hasOwnProperty(key)) {\n        let element = changes[key];\n        this.widget['_' + key](element);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.widget['destroy']();\n  }\n}\nBoldReportComponents.ɵfac = function BoldReportComponents_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\nBoldReportComponents.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BoldReportComponents,\n  factory: BoldReportComponents.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportComponents, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: Array\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i0.KeyValueDiffers\n    }];\n  }, null);\n})();\nclass ComplexTagElement {\n  // tags: Array<string>;\n  constructor(tags) {\n    this.tags = tags;\n    this.valueChange = new EventEmitter();\n    this.complexes = [];\n    this.hasChanges = false;\n    this.firstChange = true;\n    Object.defineProperty(this, 'parent', {\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n    currentTemplateElement = this;\n  }\n  ngOnInit() {\n    this.firstChange = false;\n    for (let key in this) {\n      if (key.indexOf('_') != -1 && key.indexOf('tag_') == -1) this.complexes.push(key);\n    }\n    for (let i = 0; i < this.complexes.length; i++) {\n      let property = this.complexes[i];\n      if (property.indexOf('_') != -1) {\n        let tempObj = {};\n        let key = property.replace(/\\_/g, '.');\n        ej.createObject(key, this[property], tempObj);\n        let rootProp = key.split('.')[0];\n        if (this[rootProp] == undefined) this[rootProp] = {};\n        $.extend(true, this[rootProp], tempObj[rootProp]);\n      }\n    }\n  }\n  ensureCleanObject() {\n    let tags = this.tags;\n    for (let i = 0; i < tags.length; i++) {\n      let element = tags[i],\n        tagElement = this['tag_' + element.replace(/\\./g, '_')];\n      if (i === 0 && this[element]) {\n        return;\n      }\n      if (ej.isNullOrUndefined(tagElement)) {\n        continue;\n      }\n      ej.createObject(element, tagElement.getList(), this);\n    }\n  }\n  ngOnChanges(changes) {\n    if (this.firstChange) {\n      return;\n    }\n    this.recentChanges = changes;\n    this.hasChanges = true;\n  }\n  getChangesAndReset() {\n    if (this.hasChanges === false) {\n      return;\n    }\n    let changes = this.recentChanges || {};\n    let contentChanges = Utils.IterateAndGetChanges(this);\n    if (!$.isEmptyObject(contentChanges)) {\n      for (let key in contentChanges) {\n        if (contentChanges.hasOwnProperty(key)) {\n          let element = contentChanges[key];\n          // this.el.nativeElement.\n          if (!ej.isNullOrUndefined(this.parent.widget['_' + this.property.replace(/\\./g, '_') + '_' + key])) this.parent.widget['_' + this.property.replace(/\\./g, '_') + '_' + key](element);\n        }\n      }\n    }\n    this.hasChanges = false;\n    return changes;\n  }\n  ngAfterContentChecked() {\n    let tags = this.tags;\n    for (let i = 0, len = tags.length; i < len; i++) {\n      let element = tags[i],\n        tagElement = this['tag_' + element.replace(/\\./g, '_')];\n      if (tagElement && tagElement.hasChanges) {\n        this.hasChanges = true;\n      }\n    }\n  }\n}\nComplexTagElement.ɵfac = function ComplexTagElement_Factory(t) {\n  return new (t || ComplexTagElement)(i0.ɵɵinject(Array));\n};\nComplexTagElement.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ComplexTagElement,\n  factory: ComplexTagElement.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ComplexTagElement, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Array\n    }];\n  }, null);\n})();\nclass ArrayTagElement {\n  constructor(propertyName) {\n    this.propertyName = propertyName;\n    this.hasChanges = false;\n  }\n  // TODO: Need to consider dynamic child change\n  ngAfterContentInit() {\n    let index = 0;\n    this.list = this.children.map(child => {\n      child.index = index++;\n      child.property = this.propertyName;\n      return child;\n    });\n  }\n  getList() {\n    let list = this.list;\n    for (let i = 0; i < list.length; i++) {\n      list[i].ensureCleanObject();\n    }\n    return list;\n  }\n  getChangesAndReset() {\n    this.hasChanges = false;\n    return this.recentChanges;\n  }\n  ngAfterContentChecked() {\n    let changes = {},\n      res = changes[this.propertyName] = [],\n      childChange;\n    for (let i = 0, list = this.list; i < list.length; i++) {\n      let child = list[i];\n      if (child.hasChanges) {\n        childChange = child.getChangesAndReset();\n        if (!ej.isNullOrUndefined(childChange)) {\n          res.push({\n            index: child.index,\n            change: childChange\n          });\n        }\n      }\n    }\n    if (res.length > 0) {\n      this.recentChanges = res;\n      this.hasChanges = true;\n    }\n  }\n}\nArrayTagElement.ɵfac = function ArrayTagElement_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\nArrayTagElement.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ArrayTagElement,\n  factory: ArrayTagElement.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ArrayTagElement, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined\n    }];\n  }, null);\n})();\nclass BoldReportViewerComponent extends BoldReportComponents {\n  // tslint:disable-next-line:max-line-length\n  constructor(el, cdRef, _ejIterableDiffers, _ejkeyvaluediffers) {\n    super('BoldReportViewer', el, cdRef, [], _ejIterableDiffers, _ejkeyvaluediffers);\n    this.el = el;\n    this.cdRef = cdRef;\n    this._ejIterableDiffers = _ejIterableDiffers;\n    this._ejkeyvaluediffers = _ejkeyvaluediffers;\n    this.drillThrough_output = new EventEmitter();\n    this.renderingBegin_output = new EventEmitter();\n    this.renderingComplete_output = new EventEmitter();\n    this.reportError_output = new EventEmitter();\n    this.reportExport_output = new EventEmitter();\n    this.reportLoaded_output = new EventEmitter();\n    this.reportCanceled_output = new EventEmitter();\n    this.showError_output = new EventEmitter();\n    this.viewReportClick_output = new EventEmitter();\n    this.ajaxBeforeLoad_output = new EventEmitter();\n    this.ajaxSuccess_output = new EventEmitter();\n    this.ajaxError_output = new EventEmitter();\n    this.toolbarRendering_output = new EventEmitter();\n    this.exportProgressChanged_output = new EventEmitter();\n    this.printProgressChanged_output = new EventEmitter();\n    this.exportItemClick_output = new EventEmitter();\n    this.toolBarItemClick_output = new EventEmitter();\n    this.hyperlink_output = new EventEmitter();\n    this.reportPrint_output = new EventEmitter();\n    this.beforeParameterAdd_output = new EventEmitter();\n  }\n}\nBoldReportViewerComponent.ɵfac = function BoldReportViewerComponent_Factory(t) {\n  return new (t || BoldReportViewerComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.KeyValueDiffers));\n};\nBoldReportViewerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: BoldReportViewerComponent,\n  selectors: [[\"bold-reportviewer\"]],\n  inputs: {\n    dataSources_input: [i0.ɵɵInputFlags.None, \"dataSources\", \"dataSources_input\"],\n    exportSettings_input: [i0.ɵɵInputFlags.None, \"exportSettings\", \"exportSettings_input\"],\n    isResponsive_input: [i0.ɵɵInputFlags.None, \"isResponsive\", \"isResponsive_input\"],\n    locale_input: [i0.ɵɵInputFlags.None, \"locale\", \"locale_input\"],\n    pageSettings_input: [i0.ɵɵInputFlags.None, \"pageSettings\", \"pageSettings_input\"],\n    parameters_input: [i0.ɵɵInputFlags.None, \"parameters\", \"parameters_input\"],\n    extendedAttributes_input: [i0.ɵɵInputFlags.None, \"extendedAttributes\", \"extendedAttributes_input\"],\n    toolbarSettings_input: [i0.ɵɵInputFlags.None, \"toolbarSettings\", \"toolbarSettings_input\"],\n    parameterSettings_input: [i0.ɵɵInputFlags.None, \"parameterSettings\", \"parameterSettings_input\"],\n    printMode_input: [i0.ɵɵInputFlags.None, \"printMode\", \"printMode_input\"],\n    printOption_input: [i0.ɵɵInputFlags.None, \"printOption\", \"printOption_input\"],\n    processingMode_input: [i0.ɵɵInputFlags.None, \"processingMode\", \"processingMode_input\"],\n    renderMode_input: [i0.ɵɵInputFlags.None, \"renderMode\", \"renderMode_input\"],\n    reportPath_input: [i0.ɵɵInputFlags.None, \"reportPath\", \"reportPath_input\"],\n    reportServerUrl_input: [i0.ɵɵInputFlags.None, \"reportServerUrl\", \"reportServerUrl_input\"],\n    reportServiceUrl_input: [i0.ɵɵInputFlags.None, \"reportServiceUrl\", \"reportServiceUrl_input\"],\n    zoomFactor_input: [i0.ɵɵInputFlags.None, \"zoomFactor\", \"zoomFactor_input\"],\n    serviceAuthorizationToken_input: [i0.ɵɵInputFlags.None, \"serviceAuthorizationToken\", \"serviceAuthorizationToken_input\"],\n    embedToken_input: [i0.ɵɵInputFlags.None, \"embedToken\", \"embedToken_input\"],\n    toolbarRenderMode_input: [i0.ɵɵInputFlags.None, \"toolbarRenderMode\", \"toolbarRenderMode_input\"],\n    enableParameterBlockScroller_input: [i0.ɵɵInputFlags.None, \"enableParameterBlockScroller\", \"enableParameterBlockScroller_input\"],\n    enableDatasourceBlockScroller_input: [i0.ɵɵInputFlags.None, \"enableDatasourceBlockScroller\", \"enableDatasourceBlockScroller_input\"],\n    sizeToReportContent_input: [i0.ɵɵInputFlags.None, \"sizeToReportContent\", \"sizeToReportContent_input\"],\n    autoRender_input: [i0.ɵɵInputFlags.None, \"autoRender\", \"autoRender_input\"],\n    enableNotificationBar_input: [i0.ɵɵInputFlags.None, \"enableNotificationBar\", \"enableNotificationBar_input\"],\n    enableDropDownSearch_input: [i0.ɵɵInputFlags.None, \"enableDropDownSearch\", \"enableDropDownSearch_input\"],\n    enableVirtualEvaluation_input: [i0.ɵɵInputFlags.None, \"enableVirtualEvaluation\", \"enableVirtualEvaluation_input\"],\n    smartRendering_input: [i0.ɵɵInputFlags.None, \"smartRendering\", \"smartRendering_input\"],\n    waitingPopupTemplate_input: [i0.ɵɵInputFlags.None, \"waitingPopupTemplate\", \"waitingPopupTemplate_input\"],\n    enableOnScrollNavigation_input: [i0.ɵɵInputFlags.None, \"enableOnScrollNavigation\", \"enableOnScrollNavigation_input\"],\n    customBrandSettings_input: [i0.ɵɵInputFlags.None, \"customBrandSettings\", \"customBrandSettings_input\"],\n    customBrandSettings_link_input: [i0.ɵɵInputFlags.None, \"customBrandSettings.link\", \"customBrandSettings_link_input\"],\n    customBrandSettings_domain_input: [i0.ɵɵInputFlags.None, \"customBrandSettings.domain\", \"customBrandSettings_domain_input\"],\n    customBrandSettings_name_input: [i0.ɵɵInputFlags.None, \"customBrandSettings.name\", \"customBrandSettings_name_input\"],\n    customBrandSettings_links_input: [i0.ɵɵInputFlags.None, \"customBrandSettings.links\", \"customBrandSettings_links_input\"],\n    dataSources_name_input: [i0.ɵɵInputFlags.None, \"dataSources.name\", \"dataSources_name_input\"],\n    dataSources_value_input: [i0.ɵɵInputFlags.None, \"dataSources.value\", \"dataSources_value_input\"],\n    exportSettings_exportOptions_input: [i0.ɵɵInputFlags.None, \"exportSettings.exportOptions\", \"exportSettings_exportOptions_input\"],\n    exportSettings_excelFormat_input: [i0.ɵɵInputFlags.None, \"exportSettings.excelFormat\", \"exportSettings_excelFormat_input\"],\n    exportSettings_wordFormat_input: [i0.ɵɵInputFlags.None, \"exportSettings.wordFormat\", \"exportSettings_wordFormat_input\"],\n    exportSettings_customItems_input: [i0.ɵɵInputFlags.None, \"exportSettings.customItems\", \"exportSettings_customItems_input\"],\n    exportSettings_ImageQuality_input: [i0.ɵɵInputFlags.None, \"exportSettings.ImageQuality\", \"exportSettings_ImageQuality_input\"],\n    exportSettings_UsePrintSizes_input: [i0.ɵɵInputFlags.None, \"exportSettings.UsePrintSizes\", \"exportSettings_UsePrintSizes_input\"],\n    pageSettings_orientation_input: [i0.ɵɵInputFlags.None, \"pageSettings.orientation\", \"pageSettings_orientation_input\"],\n    pageSettings_paperSize_input: [i0.ɵɵInputFlags.None, \"pageSettings.paperSize\", \"pageSettings_paperSize_input\"],\n    pageSettings_height_input: [i0.ɵɵInputFlags.None, \"pageSettings.height\", \"pageSettings_height_input\"],\n    pageSettings_width_input: [i0.ɵɵInputFlags.None, \"pageSettings.width\", \"pageSettings_width_input\"],\n    pageSettings_margins_input: [i0.ɵɵInputFlags.None, \"pageSettings.margins\", \"pageSettings_margins_input\"],\n    pageSettings_hidePageOrientation_input: [i0.ɵɵInputFlags.None, \"pageSettings.hidePageOrientation\", \"pageSettings_hidePageOrientation_input\"],\n    pageSettings_hidePagePaperSizes_input: [i0.ɵɵInputFlags.None, \"pageSettings.hidePagePaperSizes\", \"pageSettings_hidePagePaperSizes_input\"],\n    parameters_labels_input: [i0.ɵɵInputFlags.None, \"parameters.labels\", \"parameters_labels_input\"],\n    parameters_name_input: [i0.ɵɵInputFlags.None, \"parameters.name\", \"parameters_name_input\"],\n    parameters_nullable_input: [i0.ɵɵInputFlags.None, \"parameters.nullable\", \"parameters_nullable_input\"],\n    parameters_prompt_input: [i0.ɵɵInputFlags.None, \"parameters.prompt\", \"parameters_prompt_input\"],\n    parameters_values_input: [i0.ɵɵInputFlags.None, \"parameters.values\", \"parameters_values_input\"],\n    parameterSettings_delimiterChar_input: [i0.ɵɵInputFlags.None, \"parameterSettings.delimiterChar\", \"parameterSettings_delimiterChar_input\"],\n    parameterSettings_position_input: [i0.ɵɵInputFlags.None, \"parameterSettings.position\", \"parameterSettings_position_input\"],\n    parameterSettings_popupHeight_input: [i0.ɵɵInputFlags.None, \"parameterSettings.popupHeight\", \"parameterSettings_popupHeight_input\"],\n    parameterSettings_popupWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.popupWidth\", \"parameterSettings_popupWidth_input\"],\n    parameterSettings_itemWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.itemWidth\", \"parameterSettings_itemWidth_input\"],\n    parameterSettings_labelWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.labelWidth\", \"parameterSettings_labelWidth_input\"],\n    parameterSettings_minDateTime_input: [i0.ɵɵInputFlags.None, \"parameterSettings.minDateTime\", \"parameterSettings_minDateTime_input\"],\n    parameterSettings_maxDateTime_input: [i0.ɵɵInputFlags.None, \"parameterSettings.maxDateTime\", \"parameterSettings_maxDateTime_input\"],\n    parameterSettings_hideTooltip_input: [i0.ɵɵInputFlags.None, \"parameterSettings.hideTooltip\", \"parameterSettings_hideTooltip_input\"],\n    parameterSettings_enablePopupResize_input: [i0.ɵɵInputFlags.None, \"parameterSettings.enablePopupResize\", \"parameterSettings_enablePopupResize_input\"],\n    parameterSettings_hideParameterBlock_input: [i0.ɵɵInputFlags.None, \"parameterSettings.hideParameterBlock\", \"parameterSettings_hideParameterBlock_input\"],\n    parameterSettings_dateTimeFormat_input: [i0.ɵɵInputFlags.None, \"parameterSettings.dateTimeFormat\", \"parameterSettings_dateTimeFormat_input\"],\n    parameterSettings_timeDisplayFormat_input: [i0.ɵɵInputFlags.None, \"parameterSettings.timeDisplayFormat\", \"parameterSettings_timeDisplayFormat_input\"],\n    parameterSettings_timeInterval_input: [i0.ɵɵInputFlags.None, \"parameterSettings.timeInterval\", \"parameterSettings_timeInterval_input\"],\n    parameterSettings_accessInternalValue_input: [i0.ɵɵInputFlags.None, \"parameterSettings.accessInternalValue\", \"parameterSettings_accessInternalValue_input\"],\n    toolbarSettings_click_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.click\", \"toolbarSettings_click_input\"],\n    toolbarSettings_items_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.items\", \"toolbarSettings_items_input\"],\n    toolbarSettings_toolbars_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.toolbars\", \"toolbarSettings_toolbars_input\"],\n    toolbarSettings_showToolbar_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.showToolbar\", \"toolbarSettings_showToolbar_input\"],\n    toolbarSettings_showTooltip_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.showTooltip\", \"toolbarSettings_showTooltip_input\"],\n    toolbarSettings_autoHide_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.autoHide\", \"toolbarSettings_autoHide_input\"],\n    toolbarSettings_autoHideDelay_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.autoHideDelay\", \"toolbarSettings_autoHideDelay_input\"],\n    toolbarSettings_templateId_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.templateId\", \"toolbarSettings_templateId_input\"],\n    toolbarSettings_customItems_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.customItems\", \"toolbarSettings_customItems_input\"],\n    toolbarSettings_customGroups_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.customGroups\", \"toolbarSettings_customGroups_input\"]\n  },\n  outputs: {\n    drillThrough_output: \"drillThrough\",\n    renderingBegin_output: \"renderingBegin\",\n    renderingComplete_output: \"renderingComplete\",\n    reportError_output: \"reportError\",\n    reportExport_output: \"reportExport\",\n    reportLoaded_output: \"reportLoaded\",\n    reportCanceled_output: \"reportCanceled\",\n    showError_output: \"showError\",\n    viewReportClick_output: \"viewReportClick\",\n    ajaxBeforeLoad_output: \"ajaxBeforeLoad\",\n    ajaxSuccess_output: \"ajaxSuccess\",\n    ajaxError_output: \"ajaxError\",\n    toolbarRendering_output: \"toolbarRendering\",\n    exportProgressChanged_output: \"exportProgressChanged\",\n    printProgressChanged_output: \"printProgressChanged\",\n    exportItemClick_output: \"exportItemClick\",\n    toolBarItemClick_output: \"toolBarItemClick\",\n    hyperlink_output: \"hyperlink\",\n    reportPrint_output: \"reportPrint\",\n    beforeParameterAdd_output: \"beforeParameterAdd\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 0,\n  vars: 0,\n  template: function BoldReportViewerComponent_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportViewerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bold-reportviewer',\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i0.KeyValueDiffers\n    }];\n  }, {\n    dataSources_input: [{\n      type: Input,\n      args: ['dataSources']\n    }],\n    exportSettings_input: [{\n      type: Input,\n      args: ['exportSettings']\n    }],\n    isResponsive_input: [{\n      type: Input,\n      args: ['isResponsive']\n    }],\n    locale_input: [{\n      type: Input,\n      args: ['locale']\n    }],\n    pageSettings_input: [{\n      type: Input,\n      args: ['pageSettings']\n    }],\n    parameters_input: [{\n      type: Input,\n      args: ['parameters']\n    }],\n    extendedAttributes_input: [{\n      type: Input,\n      args: ['extendedAttributes']\n    }],\n    toolbarSettings_input: [{\n      type: Input,\n      args: ['toolbarSettings']\n    }],\n    parameterSettings_input: [{\n      type: Input,\n      args: ['parameterSettings']\n    }],\n    printMode_input: [{\n      type: Input,\n      args: ['printMode']\n    }],\n    printOption_input: [{\n      type: Input,\n      args: ['printOption']\n    }],\n    processingMode_input: [{\n      type: Input,\n      args: ['processingMode']\n    }],\n    renderMode_input: [{\n      type: Input,\n      args: ['renderMode']\n    }],\n    reportPath_input: [{\n      type: Input,\n      args: ['reportPath']\n    }],\n    reportServerUrl_input: [{\n      type: Input,\n      args: ['reportServerUrl']\n    }],\n    reportServiceUrl_input: [{\n      type: Input,\n      args: ['reportServiceUrl']\n    }],\n    zoomFactor_input: [{\n      type: Input,\n      args: ['zoomFactor']\n    }],\n    serviceAuthorizationToken_input: [{\n      type: Input,\n      args: ['serviceAuthorizationToken']\n    }],\n    embedToken_input: [{\n      type: Input,\n      args: ['embedToken']\n    }],\n    toolbarRenderMode_input: [{\n      type: Input,\n      args: ['toolbarRenderMode']\n    }],\n    enableParameterBlockScroller_input: [{\n      type: Input,\n      args: ['enableParameterBlockScroller']\n    }],\n    enableDatasourceBlockScroller_input: [{\n      type: Input,\n      args: ['enableDatasourceBlockScroller']\n    }],\n    sizeToReportContent_input: [{\n      type: Input,\n      args: ['sizeToReportContent']\n    }],\n    autoRender_input: [{\n      type: Input,\n      args: ['autoRender']\n    }],\n    enableNotificationBar_input: [{\n      type: Input,\n      args: ['enableNotificationBar']\n    }],\n    enableDropDownSearch_input: [{\n      type: Input,\n      args: ['enableDropDownSearch']\n    }],\n    enableVirtualEvaluation_input: [{\n      type: Input,\n      args: ['enableVirtualEvaluation']\n    }],\n    smartRendering_input: [{\n      type: Input,\n      args: ['smartRendering']\n    }],\n    waitingPopupTemplate_input: [{\n      type: Input,\n      args: ['waitingPopupTemplate']\n    }],\n    enableOnScrollNavigation_input: [{\n      type: Input,\n      args: ['enableOnScrollNavigation']\n    }],\n    customBrandSettings_input: [{\n      type: Input,\n      args: ['customBrandSettings']\n    }],\n    customBrandSettings_link_input: [{\n      type: Input,\n      args: ['customBrandSettings.link']\n    }],\n    customBrandSettings_domain_input: [{\n      type: Input,\n      args: ['customBrandSettings.domain']\n    }],\n    customBrandSettings_name_input: [{\n      type: Input,\n      args: ['customBrandSettings.name']\n    }],\n    customBrandSettings_links_input: [{\n      type: Input,\n      args: ['customBrandSettings.links']\n    }],\n    dataSources_name_input: [{\n      type: Input,\n      args: ['dataSources.name']\n    }],\n    dataSources_value_input: [{\n      type: Input,\n      args: ['dataSources.value']\n    }],\n    exportSettings_exportOptions_input: [{\n      type: Input,\n      args: ['exportSettings.exportOptions']\n    }],\n    exportSettings_excelFormat_input: [{\n      type: Input,\n      args: ['exportSettings.excelFormat']\n    }],\n    exportSettings_wordFormat_input: [{\n      type: Input,\n      args: ['exportSettings.wordFormat']\n    }],\n    exportSettings_customItems_input: [{\n      type: Input,\n      args: ['exportSettings.customItems']\n    }],\n    exportSettings_ImageQuality_input: [{\n      type: Input,\n      args: ['exportSettings.ImageQuality']\n    }],\n    exportSettings_UsePrintSizes_input: [{\n      type: Input,\n      args: ['exportSettings.UsePrintSizes']\n    }],\n    pageSettings_orientation_input: [{\n      type: Input,\n      args: ['pageSettings.orientation']\n    }],\n    pageSettings_paperSize_input: [{\n      type: Input,\n      args: ['pageSettings.paperSize']\n    }],\n    pageSettings_height_input: [{\n      type: Input,\n      args: ['pageSettings.height']\n    }],\n    pageSettings_width_input: [{\n      type: Input,\n      args: ['pageSettings.width']\n    }],\n    pageSettings_margins_input: [{\n      type: Input,\n      args: ['pageSettings.margins']\n    }],\n    pageSettings_hidePageOrientation_input: [{\n      type: Input,\n      args: ['pageSettings.hidePageOrientation']\n    }],\n    pageSettings_hidePagePaperSizes_input: [{\n      type: Input,\n      args: ['pageSettings.hidePagePaperSizes']\n    }],\n    parameters_labels_input: [{\n      type: Input,\n      args: ['parameters.labels']\n    }],\n    parameters_name_input: [{\n      type: Input,\n      args: ['parameters.name']\n    }],\n    parameters_nullable_input: [{\n      type: Input,\n      args: ['parameters.nullable']\n    }],\n    parameters_prompt_input: [{\n      type: Input,\n      args: ['parameters.prompt']\n    }],\n    parameters_values_input: [{\n      type: Input,\n      args: ['parameters.values']\n    }],\n    parameterSettings_delimiterChar_input: [{\n      type: Input,\n      args: ['parameterSettings.delimiterChar']\n    }],\n    parameterSettings_position_input: [{\n      type: Input,\n      args: ['parameterSettings.position']\n    }],\n    parameterSettings_popupHeight_input: [{\n      type: Input,\n      args: ['parameterSettings.popupHeight']\n    }],\n    parameterSettings_popupWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.popupWidth']\n    }],\n    parameterSettings_itemWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.itemWidth']\n    }],\n    parameterSettings_labelWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.labelWidth']\n    }],\n    parameterSettings_minDateTime_input: [{\n      type: Input,\n      args: ['parameterSettings.minDateTime']\n    }],\n    parameterSettings_maxDateTime_input: [{\n      type: Input,\n      args: ['parameterSettings.maxDateTime']\n    }],\n    parameterSettings_hideTooltip_input: [{\n      type: Input,\n      args: ['parameterSettings.hideTooltip']\n    }],\n    parameterSettings_enablePopupResize_input: [{\n      type: Input,\n      args: ['parameterSettings.enablePopupResize']\n    }],\n    parameterSettings_hideParameterBlock_input: [{\n      type: Input,\n      args: ['parameterSettings.hideParameterBlock']\n    }],\n    parameterSettings_dateTimeFormat_input: [{\n      type: Input,\n      args: ['parameterSettings.dateTimeFormat']\n    }],\n    parameterSettings_timeDisplayFormat_input: [{\n      type: Input,\n      args: ['parameterSettings.timeDisplayFormat']\n    }],\n    parameterSettings_timeInterval_input: [{\n      type: Input,\n      args: ['parameterSettings.timeInterval']\n    }],\n    parameterSettings_accessInternalValue_input: [{\n      type: Input,\n      args: ['parameterSettings.accessInternalValue']\n    }],\n    toolbarSettings_click_input: [{\n      type: Input,\n      args: ['toolbarSettings.click']\n    }],\n    toolbarSettings_items_input: [{\n      type: Input,\n      args: ['toolbarSettings.items']\n    }],\n    toolbarSettings_toolbars_input: [{\n      type: Input,\n      args: ['toolbarSettings.toolbars']\n    }],\n    toolbarSettings_showToolbar_input: [{\n      type: Input,\n      args: ['toolbarSettings.showToolbar']\n    }],\n    toolbarSettings_showTooltip_input: [{\n      type: Input,\n      args: ['toolbarSettings.showTooltip']\n    }],\n    toolbarSettings_autoHide_input: [{\n      type: Input,\n      args: ['toolbarSettings.autoHide']\n    }],\n    toolbarSettings_autoHideDelay_input: [{\n      type: Input,\n      args: ['toolbarSettings.autoHideDelay']\n    }],\n    toolbarSettings_templateId_input: [{\n      type: Input,\n      args: ['toolbarSettings.templateId']\n    }],\n    toolbarSettings_customItems_input: [{\n      type: Input,\n      args: ['toolbarSettings.customItems']\n    }],\n    toolbarSettings_customGroups_input: [{\n      type: Input,\n      args: ['toolbarSettings.customGroups']\n    }],\n    drillThrough_output: [{\n      type: Output,\n      args: ['drillThrough']\n    }],\n    renderingBegin_output: [{\n      type: Output,\n      args: ['renderingBegin']\n    }],\n    renderingComplete_output: [{\n      type: Output,\n      args: ['renderingComplete']\n    }],\n    reportError_output: [{\n      type: Output,\n      args: ['reportError']\n    }],\n    reportExport_output: [{\n      type: Output,\n      args: ['reportExport']\n    }],\n    reportLoaded_output: [{\n      type: Output,\n      args: ['reportLoaded']\n    }],\n    reportCanceled_output: [{\n      type: Output,\n      args: ['reportCanceled']\n    }],\n    showError_output: [{\n      type: Output,\n      args: ['showError']\n    }],\n    viewReportClick_output: [{\n      type: Output,\n      args: ['viewReportClick']\n    }],\n    ajaxBeforeLoad_output: [{\n      type: Output,\n      args: ['ajaxBeforeLoad']\n    }],\n    ajaxSuccess_output: [{\n      type: Output,\n      args: ['ajaxSuccess']\n    }],\n    ajaxError_output: [{\n      type: Output,\n      args: ['ajaxError']\n    }],\n    toolbarRendering_output: [{\n      type: Output,\n      args: ['toolbarRendering']\n    }],\n    exportProgressChanged_output: [{\n      type: Output,\n      args: ['exportProgressChanged']\n    }],\n    printProgressChanged_output: [{\n      type: Output,\n      args: ['printProgressChanged']\n    }],\n    exportItemClick_output: [{\n      type: Output,\n      args: ['exportItemClick']\n    }],\n    toolBarItemClick_output: [{\n      type: Output,\n      args: ['toolBarItemClick']\n    }],\n    hyperlink_output: [{\n      type: Output,\n      args: ['hyperlink']\n    }],\n    reportPrint_output: [{\n      type: Output,\n      args: ['reportPrint']\n    }],\n    beforeParameterAdd_output: [{\n      type: Output,\n      args: ['beforeParameterAdd']\n    }]\n  });\n})();\nclass BoldReportViewerModule {}\nBoldReportViewerModule.ɵfac = function BoldReportViewerModule_Factory(t) {\n  return new (t || BoldReportViewerModule)();\n};\nBoldReportViewerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BoldReportViewerModule,\n  declarations: [BoldReportViewerComponent],\n  imports: [CommonModule],\n  exports: [BoldReportViewerComponent]\n});\nBoldReportViewerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportViewerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [BoldReportViewerComponent],\n      exports: [BoldReportViewerComponent]\n    }]\n  }], null, null);\n})();\nclass BoldReportDesignerComponent extends BoldReportComponents {\n  // tslint:disable-next-line:max-line-length\n  constructor(el, cdRef, _ejIterableDiffers, _ejkeyvaluediffers) {\n    super('BoldReportDesigner', el, cdRef, [], _ejIterableDiffers, _ejkeyvaluediffers);\n    this.el = el;\n    this.cdRef = cdRef;\n    this._ejIterableDiffers = _ejIterableDiffers;\n    this._ejkeyvaluediffers = _ejkeyvaluediffers;\n    this.ajaxBeforeLoad_output = new EventEmitter();\n    this.ajaxError_output = new EventEmitter();\n    this.ajaxSuccess_output = new EventEmitter();\n    this.create_output = new EventEmitter();\n    this.destroy_output = new EventEmitter();\n    this.newDataClick_output = new EventEmitter();\n    this.openReportClick_output = new EventEmitter();\n    this.previewReport_output = new EventEmitter();\n    this.reportModified_output = new EventEmitter();\n    this.reportOpened_output = new EventEmitter();\n    this.reportSaved_output = new EventEmitter();\n    this.saveReportClick_output = new EventEmitter();\n    this.toolbarClick_output = new EventEmitter();\n    this.toolbarRendering_output = new EventEmitter();\n    this.encryptData_output = new EventEmitter();\n    this.decryptData_output = new EventEmitter();\n  }\n}\nBoldReportDesignerComponent.ɵfac = function BoldReportDesignerComponent_Factory(t) {\n  return new (t || BoldReportDesignerComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.KeyValueDiffers));\n};\nBoldReportDesignerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: BoldReportDesignerComponent,\n  selectors: [[\"bold-reportdesigner\"]],\n  inputs: {\n    configurePaneSettings_input: [i0.ɵɵInputFlags.None, \"configurePaneSettings\", \"configurePaneSettings_input\"],\n    dataTabIndex_input: [i0.ɵɵInputFlags.None, \"dataTabIndex\", \"dataTabIndex_input\"],\n    disableCodeModule_input: [i0.ɵɵInputFlags.None, \"disableCodeModule\", \"disableCodeModule_input\"],\n    enableRuler_input: [i0.ɵɵInputFlags.None, \"enableRuler\", \"enableRuler_input\"],\n    enablePageMargin_input: [i0.ɵɵInputFlags.None, \"enablePageMargin\", \"enablePageMargin_input\"],\n    embedToken_input: [i0.ɵɵInputFlags.None, \"embedToken\", \"embedToken_input\"],\n    enableImpersonate_input: [i0.ɵɵInputFlags.None, \"enableImpersonate\", \"enableImpersonate_input\"],\n    enableImageBlobing_input: [i0.ɵɵInputFlags.None, \"enableImageBlobing\", \"enableImageBlobing_input\"],\n    enableTableMultipleDataAssign_input: [i0.ɵɵInputFlags.None, \"enableTableMultipleDataAssign\", \"enableTableMultipleDataAssign_input\"],\n    filterDataConnectors_input: [i0.ɵɵInputFlags.None, \"filterDataConnectors\", \"filterDataConnectors_input\"],\n    filterReportItems_input: [i0.ɵɵInputFlags.None, \"filterReportItems\", \"filterReportItems_input\"],\n    fontNames_input: [i0.ɵɵInputFlags.None, \"fontNames\", \"fontNames_input\"],\n    locale_input: [i0.ɵɵInputFlags.None, \"locale\", \"locale_input\"],\n    permissionSettings_input: [i0.ɵɵInputFlags.None, \"permissionSettings\", \"permissionSettings_input\"],\n    previewOptions_input: [i0.ɵɵInputFlags.None, \"previewOptions\", \"previewOptions_input\"],\n    reportDataExtensions_input: [i0.ɵɵInputFlags.None, \"reportDataExtensions\", \"reportDataExtensions_input\"],\n    reportItemExtensions_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions\", \"reportItemExtensions_input\"],\n    reportPath_input: [i0.ɵɵInputFlags.None, \"reportPath\", \"reportPath_input\"],\n    reportVersion_input: [i0.ɵɵInputFlags.None, \"reportVersion\", \"reportVersion_input\"],\n    reportType_input: [i0.ɵɵInputFlags.None, \"reportType\", \"reportType_input\"],\n    reportServerUrl_input: [i0.ɵɵInputFlags.None, \"reportServerUrl\", \"reportServerUrl_input\"],\n    serviceAuthorizationToken_input: [i0.ɵɵInputFlags.None, \"serviceAuthorizationToken\", \"serviceAuthorizationToken_input\"],\n    serviceUrl_input: [i0.ɵɵInputFlags.None, \"serviceUrl\", \"serviceUrl_input\"],\n    toolbarSettings_input: [i0.ɵɵInputFlags.None, \"toolbarSettings\", \"toolbarSettings_input\"],\n    waitingPopupTemplate_input: [i0.ɵɵInputFlags.None, \"waitingPopupTemplate\", \"waitingPopupTemplate_input\"],\n    zoomFactor_input: [i0.ɵɵInputFlags.None, \"zoomFactor\", \"zoomFactor_input\"],\n    configurePaneSettings_items_input: [i0.ɵɵInputFlags.None, \"configurePaneSettings.items\", \"configurePaneSettings_items_input\"],\n    configurePaneSettings_showPane_input: [i0.ɵɵInputFlags.None, \"configurePaneSettings.showPane\", \"configurePaneSettings_showPane_input\"],\n    dataSources_name_input: [i0.ɵɵInputFlags.None, \"dataSources.name\", \"dataSources_name_input\"],\n    dataSources_value_input: [i0.ɵɵInputFlags.None, \"dataSources.value\", \"dataSources_value_input\"],\n    exportSettings_exportOptions_input: [i0.ɵɵInputFlags.None, \"exportSettings.exportOptions\", \"exportSettings_exportOptions_input\"],\n    exportSettings_excelFormat_input: [i0.ɵɵInputFlags.None, \"exportSettings.excelFormat\", \"exportSettings_excelFormat_input\"],\n    exportSettings_wordFormat_input: [i0.ɵɵInputFlags.None, \"exportSettings.wordFormat\", \"exportSettings_wordFormat_input\"],\n    exportSettings_customItems_input: [i0.ɵɵInputFlags.None, \"exportSettings.customItems\", \"exportSettings_customItems_input\"],\n    pageSettings_orientation_input: [i0.ɵɵInputFlags.None, \"pageSettings.orientation\", \"pageSettings_orientation_input\"],\n    pageSettings_paperSize_input: [i0.ɵɵInputFlags.None, \"pageSettings.paperSize\", \"pageSettings_paperSize_input\"],\n    pageSettings_height_input: [i0.ɵɵInputFlags.None, \"pageSettings.height\", \"pageSettings_height_input\"],\n    pageSettings_width_input: [i0.ɵɵInputFlags.None, \"pageSettings.width\", \"pageSettings_width_input\"],\n    pageSettings_margins_input: [i0.ɵɵInputFlags.None, \"pageSettings.margins\", \"pageSettings_margins_input\"],\n    pageSettings_hidePageOrientation_input: [i0.ɵɵInputFlags.None, \"pageSettings.hidePageOrientation\", \"pageSettings_hidePageOrientation_input\"],\n    parameters_labels_input: [i0.ɵɵInputFlags.None, \"parameters.labels\", \"parameters_labels_input\"],\n    parameters_name_input: [i0.ɵɵInputFlags.None, \"parameters.name\", \"parameters_name_input\"],\n    parameters_nullable_input: [i0.ɵɵInputFlags.None, \"parameters.nullable\", \"parameters_nullable_input\"],\n    parameters_prompt_input: [i0.ɵɵInputFlags.None, \"parameters.prompt\", \"parameters_prompt_input\"],\n    parameters_values_input: [i0.ɵɵInputFlags.None, \"parameters.values\", \"parameters_values_input\"],\n    parameterSettings_delimiterChar_input: [i0.ɵɵInputFlags.None, \"parameterSettings.delimiterChar\", \"parameterSettings_delimiterChar_input\"],\n    parameterSettings_popupHeight_input: [i0.ɵɵInputFlags.None, \"parameterSettings.popupHeight\", \"parameterSettings_popupHeight_input\"],\n    parameterSettings_popupWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.popupWidth\", \"parameterSettings_popupWidth_input\"],\n    parameterSettings_itemWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.itemWidth\", \"parameterSettings_itemWidth_input\"],\n    parameterSettings_labelWidth_input: [i0.ɵɵInputFlags.None, \"parameterSettings.labelWidth\", \"parameterSettings_labelWidth_input\"],\n    permissionSettings_dataSet_input: [i0.ɵɵInputFlags.None, \"permissionSettings.dataSet\", \"permissionSettings_dataSet_input\"],\n    permissionSettings_dataSource_input: [i0.ɵɵInputFlags.None, \"permissionSettings.dataSource\", \"permissionSettings_dataSource_input\"],\n    previewOptions_autoRender_input: [i0.ɵɵInputFlags.None, \"previewOptions.autoRender\", \"previewOptions_autoRender_input\"],\n    previewOptions_dataSources_input: [i0.ɵɵInputFlags.None, \"previewOptions.dataSources\", \"previewOptions_dataSources_input\"],\n    previewOptions_enableNotificationBar_input: [i0.ɵɵInputFlags.None, \"previewOptions.enableNotificationBar\", \"previewOptions_enableNotificationBar_input\"],\n    previewOptions_enableVirtualEvaluation_input: [i0.ɵɵInputFlags.None, \"previewOptions.enableVirtualEvaluation\", \"previewOptions_enableVirtualEvaluation_input\"],\n    previewOptions_enableParameterBlockScroller_input: [i0.ɵɵInputFlags.None, \"previewOptions.enableParameterBlockScroller\", \"previewOptions_enableParameterBlockScroller_input\"],\n    previewOptions_enableDatasourceBlockScroller_input: [i0.ɵɵInputFlags.None, \"previewOptions.enableDatasourceBlockScroller\", \"previewOptions_enableDatasourceBlockScroller_input\"],\n    previewOptions_enableDropDownSearch_input: [i0.ɵɵInputFlags.None, \"previewOptions.enableDropDownSearch\", \"previewOptions_enableDropDownSearch_input\"],\n    previewOptions_exportSettings_input: [i0.ɵɵInputFlags.None, \"previewOptions.exportSettings\", \"previewOptions_exportSettings_input\"],\n    previewOptions_pageSettings_input: [i0.ɵɵInputFlags.None, \"previewOptions.pageSettings\", \"previewOptions_pageSettings_input\"],\n    previewOptions_parameters_input: [i0.ɵɵInputFlags.None, \"previewOptions.parameters\", \"previewOptions_parameters_input\"],\n    previewOptions_parameterSettings_input: [i0.ɵɵInputFlags.None, \"previewOptions.parameterSettings\", \"previewOptions_parameterSettings_input\"],\n    previewOptions_printMode_input: [i0.ɵɵInputFlags.None, \"previewOptions.printMode\", \"previewOptions_printMode_input\"],\n    previewOptions_printOption_input: [i0.ɵɵInputFlags.None, \"previewOptions.printOption\", \"previewOptions_printOption_input\"],\n    previewOptions_sizeToReportContent_input: [i0.ɵɵInputFlags.None, \"previewOptions.sizeToReportContent\", \"previewOptions_sizeToReportContent_input\"],\n    previewOptions_toolbarSettings_input: [i0.ɵɵInputFlags.None, \"previewOptions.toolbarSettings\", \"previewOptions_toolbarSettings_input\"],\n    previewOptions_zoomFactor_input: [i0.ɵɵInputFlags.None, \"previewOptions.zoomFactor\", \"previewOptions_zoomFactor_input\"],\n    reportDataExtensions_name_input: [i0.ɵɵInputFlags.None, \"reportDataExtensions.name\", \"reportDataExtensions_name_input\"],\n    reportDataExtensions_className_input: [i0.ɵɵInputFlags.None, \"reportDataExtensions.className\", \"reportDataExtensions_className_input\"],\n    reportDataExtensions_imageClass_input: [i0.ɵɵInputFlags.None, \"reportDataExtensions.imageClass\", \"reportDataExtensions_imageClass_input\"],\n    reportDataExtensions_displayName_input: [i0.ɵɵInputFlags.None, \"reportDataExtensions.displayName\", \"reportDataExtensions_displayName_input\"],\n    reportItemExtensions_name_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.name\", \"reportItemExtensions_name_input\"],\n    reportItemExtensions_className_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.className\", \"reportItemExtensions_className_input\"],\n    reportItemExtensions_imageClass_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.imageClass\", \"reportItemExtensions_imageClass_input\"],\n    reportItemExtensions_displayName_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.displayName\", \"reportItemExtensions_displayName_input\"],\n    reportItemExtensions_category_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.category\", \"reportItemExtensions_category_input\"],\n    reportItemExtensions_allowHeaderFooter_input: [i0.ɵɵInputFlags.None, \"reportItemExtensions.allowHeaderFooter\", \"reportItemExtensions_allowHeaderFooter_input\"],\n    toolbarSettings_items_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.items\", \"toolbarSettings_items_input\"],\n    toolbarSettings_showToolbar_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.showToolbar\", \"toolbarSettings_showToolbar_input\"],\n    toolbarSettings_templateId_input: [i0.ɵɵInputFlags.None, \"toolbarSettings.templateId\", \"toolbarSettings_templateId_input\"]\n  },\n  outputs: {\n    ajaxBeforeLoad_output: \"ajaxBeforeLoad\",\n    ajaxError_output: \"ajaxError\",\n    ajaxSuccess_output: \"ajaxSuccess\",\n    create_output: \"create\",\n    destroy_output: \"destroy\",\n    newDataClick_output: \"newDataClick\",\n    openReportClick_output: \"openReportClick\",\n    previewReport_output: \"previewReport\",\n    reportModified_output: \"reportModified\",\n    reportOpened_output: \"reportOpened\",\n    reportSaved_output: \"reportSaved\",\n    saveReportClick_output: \"saveReportClick\",\n    toolbarClick_output: \"toolbarClick\",\n    toolbarRendering_output: \"toolbarRendering\",\n    encryptData_output: \"encryptData\",\n    decryptData_output: \"decryptData\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 0,\n  vars: 0,\n  template: function BoldReportDesignerComponent_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportDesignerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bold-reportdesigner',\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i0.KeyValueDiffers\n    }];\n  }, {\n    configurePaneSettings_input: [{\n      type: Input,\n      args: ['configurePaneSettings']\n    }],\n    dataTabIndex_input: [{\n      type: Input,\n      args: ['dataTabIndex']\n    }],\n    disableCodeModule_input: [{\n      type: Input,\n      args: ['disableCodeModule']\n    }],\n    enableRuler_input: [{\n      type: Input,\n      args: ['enableRuler']\n    }],\n    enablePageMargin_input: [{\n      type: Input,\n      args: ['enablePageMargin']\n    }],\n    embedToken_input: [{\n      type: Input,\n      args: ['embedToken']\n    }],\n    enableImpersonate_input: [{\n      type: Input,\n      args: ['enableImpersonate']\n    }],\n    enableImageBlobing_input: [{\n      type: Input,\n      args: ['enableImageBlobing']\n    }],\n    enableTableMultipleDataAssign_input: [{\n      type: Input,\n      args: ['enableTableMultipleDataAssign']\n    }],\n    filterDataConnectors_input: [{\n      type: Input,\n      args: ['filterDataConnectors']\n    }],\n    filterReportItems_input: [{\n      type: Input,\n      args: ['filterReportItems']\n    }],\n    fontNames_input: [{\n      type: Input,\n      args: ['fontNames']\n    }],\n    locale_input: [{\n      type: Input,\n      args: ['locale']\n    }],\n    permissionSettings_input: [{\n      type: Input,\n      args: ['permissionSettings']\n    }],\n    previewOptions_input: [{\n      type: Input,\n      args: ['previewOptions']\n    }],\n    reportDataExtensions_input: [{\n      type: Input,\n      args: ['reportDataExtensions']\n    }],\n    reportItemExtensions_input: [{\n      type: Input,\n      args: ['reportItemExtensions']\n    }],\n    reportPath_input: [{\n      type: Input,\n      args: ['reportPath']\n    }],\n    reportVersion_input: [{\n      type: Input,\n      args: ['reportVersion']\n    }],\n    reportType_input: [{\n      type: Input,\n      args: ['reportType']\n    }],\n    reportServerUrl_input: [{\n      type: Input,\n      args: ['reportServerUrl']\n    }],\n    serviceAuthorizationToken_input: [{\n      type: Input,\n      args: ['serviceAuthorizationToken']\n    }],\n    serviceUrl_input: [{\n      type: Input,\n      args: ['serviceUrl']\n    }],\n    toolbarSettings_input: [{\n      type: Input,\n      args: ['toolbarSettings']\n    }],\n    waitingPopupTemplate_input: [{\n      type: Input,\n      args: ['waitingPopupTemplate']\n    }],\n    zoomFactor_input: [{\n      type: Input,\n      args: ['zoomFactor']\n    }],\n    configurePaneSettings_items_input: [{\n      type: Input,\n      args: ['configurePaneSettings.items']\n    }],\n    configurePaneSettings_showPane_input: [{\n      type: Input,\n      args: ['configurePaneSettings.showPane']\n    }],\n    dataSources_name_input: [{\n      type: Input,\n      args: ['dataSources.name']\n    }],\n    dataSources_value_input: [{\n      type: Input,\n      args: ['dataSources.value']\n    }],\n    exportSettings_exportOptions_input: [{\n      type: Input,\n      args: ['exportSettings.exportOptions']\n    }],\n    exportSettings_excelFormat_input: [{\n      type: Input,\n      args: ['exportSettings.excelFormat']\n    }],\n    exportSettings_wordFormat_input: [{\n      type: Input,\n      args: ['exportSettings.wordFormat']\n    }],\n    exportSettings_customItems_input: [{\n      type: Input,\n      args: ['exportSettings.customItems']\n    }],\n    pageSettings_orientation_input: [{\n      type: Input,\n      args: ['pageSettings.orientation']\n    }],\n    pageSettings_paperSize_input: [{\n      type: Input,\n      args: ['pageSettings.paperSize']\n    }],\n    pageSettings_height_input: [{\n      type: Input,\n      args: ['pageSettings.height']\n    }],\n    pageSettings_width_input: [{\n      type: Input,\n      args: ['pageSettings.width']\n    }],\n    pageSettings_margins_input: [{\n      type: Input,\n      args: ['pageSettings.margins']\n    }],\n    pageSettings_hidePageOrientation_input: [{\n      type: Input,\n      args: ['pageSettings.hidePageOrientation']\n    }],\n    parameters_labels_input: [{\n      type: Input,\n      args: ['parameters.labels']\n    }],\n    parameters_name_input: [{\n      type: Input,\n      args: ['parameters.name']\n    }],\n    parameters_nullable_input: [{\n      type: Input,\n      args: ['parameters.nullable']\n    }],\n    parameters_prompt_input: [{\n      type: Input,\n      args: ['parameters.prompt']\n    }],\n    parameters_values_input: [{\n      type: Input,\n      args: ['parameters.values']\n    }],\n    parameterSettings_delimiterChar_input: [{\n      type: Input,\n      args: ['parameterSettings.delimiterChar']\n    }],\n    parameterSettings_popupHeight_input: [{\n      type: Input,\n      args: ['parameterSettings.popupHeight']\n    }],\n    parameterSettings_popupWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.popupWidth']\n    }],\n    parameterSettings_itemWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.itemWidth']\n    }],\n    parameterSettings_labelWidth_input: [{\n      type: Input,\n      args: ['parameterSettings.labelWidth']\n    }],\n    permissionSettings_dataSet_input: [{\n      type: Input,\n      args: ['permissionSettings.dataSet']\n    }],\n    permissionSettings_dataSource_input: [{\n      type: Input,\n      args: ['permissionSettings.dataSource']\n    }],\n    previewOptions_autoRender_input: [{\n      type: Input,\n      args: ['previewOptions.autoRender']\n    }],\n    previewOptions_dataSources_input: [{\n      type: Input,\n      args: ['previewOptions.dataSources']\n    }],\n    previewOptions_enableNotificationBar_input: [{\n      type: Input,\n      args: ['previewOptions.enableNotificationBar']\n    }],\n    previewOptions_enableVirtualEvaluation_input: [{\n      type: Input,\n      args: ['previewOptions.enableVirtualEvaluation']\n    }],\n    previewOptions_enableParameterBlockScroller_input: [{\n      type: Input,\n      args: ['previewOptions.enableParameterBlockScroller']\n    }],\n    previewOptions_enableDatasourceBlockScroller_input: [{\n      type: Input,\n      args: ['previewOptions.enableDatasourceBlockScroller']\n    }],\n    previewOptions_enableDropDownSearch_input: [{\n      type: Input,\n      args: ['previewOptions.enableDropDownSearch']\n    }],\n    previewOptions_exportSettings_input: [{\n      type: Input,\n      args: ['previewOptions.exportSettings']\n    }],\n    previewOptions_pageSettings_input: [{\n      type: Input,\n      args: ['previewOptions.pageSettings']\n    }],\n    previewOptions_parameters_input: [{\n      type: Input,\n      args: ['previewOptions.parameters']\n    }],\n    previewOptions_parameterSettings_input: [{\n      type: Input,\n      args: ['previewOptions.parameterSettings']\n    }],\n    previewOptions_printMode_input: [{\n      type: Input,\n      args: ['previewOptions.printMode']\n    }],\n    previewOptions_printOption_input: [{\n      type: Input,\n      args: ['previewOptions.printOption']\n    }],\n    previewOptions_sizeToReportContent_input: [{\n      type: Input,\n      args: ['previewOptions.sizeToReportContent']\n    }],\n    previewOptions_toolbarSettings_input: [{\n      type: Input,\n      args: ['previewOptions.toolbarSettings']\n    }],\n    previewOptions_zoomFactor_input: [{\n      type: Input,\n      args: ['previewOptions.zoomFactor']\n    }],\n    reportDataExtensions_name_input: [{\n      type: Input,\n      args: ['reportDataExtensions.name']\n    }],\n    reportDataExtensions_className_input: [{\n      type: Input,\n      args: ['reportDataExtensions.className']\n    }],\n    reportDataExtensions_imageClass_input: [{\n      type: Input,\n      args: ['reportDataExtensions.imageClass']\n    }],\n    reportDataExtensions_displayName_input: [{\n      type: Input,\n      args: ['reportDataExtensions.displayName']\n    }],\n    reportItemExtensions_name_input: [{\n      type: Input,\n      args: ['reportItemExtensions.name']\n    }],\n    reportItemExtensions_className_input: [{\n      type: Input,\n      args: ['reportItemExtensions.className']\n    }],\n    reportItemExtensions_imageClass_input: [{\n      type: Input,\n      args: ['reportItemExtensions.imageClass']\n    }],\n    reportItemExtensions_displayName_input: [{\n      type: Input,\n      args: ['reportItemExtensions.displayName']\n    }],\n    reportItemExtensions_category_input: [{\n      type: Input,\n      args: ['reportItemExtensions.category']\n    }],\n    reportItemExtensions_allowHeaderFooter_input: [{\n      type: Input,\n      args: ['reportItemExtensions.allowHeaderFooter']\n    }],\n    toolbarSettings_items_input: [{\n      type: Input,\n      args: ['toolbarSettings.items']\n    }],\n    toolbarSettings_showToolbar_input: [{\n      type: Input,\n      args: ['toolbarSettings.showToolbar']\n    }],\n    toolbarSettings_templateId_input: [{\n      type: Input,\n      args: ['toolbarSettings.templateId']\n    }],\n    ajaxBeforeLoad_output: [{\n      type: Output,\n      args: ['ajaxBeforeLoad']\n    }],\n    ajaxError_output: [{\n      type: Output,\n      args: ['ajaxError']\n    }],\n    ajaxSuccess_output: [{\n      type: Output,\n      args: ['ajaxSuccess']\n    }],\n    create_output: [{\n      type: Output,\n      args: ['create']\n    }],\n    destroy_output: [{\n      type: Output,\n      args: ['destroy']\n    }],\n    newDataClick_output: [{\n      type: Output,\n      args: ['newDataClick']\n    }],\n    openReportClick_output: [{\n      type: Output,\n      args: ['openReportClick']\n    }],\n    previewReport_output: [{\n      type: Output,\n      args: ['previewReport']\n    }],\n    reportModified_output: [{\n      type: Output,\n      args: ['reportModified']\n    }],\n    reportOpened_output: [{\n      type: Output,\n      args: ['reportOpened']\n    }],\n    reportSaved_output: [{\n      type: Output,\n      args: ['reportSaved']\n    }],\n    saveReportClick_output: [{\n      type: Output,\n      args: ['saveReportClick']\n    }],\n    toolbarClick_output: [{\n      type: Output,\n      args: ['toolbarClick']\n    }],\n    toolbarRendering_output: [{\n      type: Output,\n      args: ['toolbarRendering']\n    }],\n    encryptData_output: [{\n      type: Output,\n      args: ['encryptData']\n    }],\n    decryptData_output: [{\n      type: Output,\n      args: ['decryptData']\n    }]\n  });\n})();\nclass BoldReportDesignerModule {}\nBoldReportDesignerModule.ɵfac = function BoldReportDesignerModule_Factory(t) {\n  return new (t || BoldReportDesignerModule)();\n};\nBoldReportDesignerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BoldReportDesignerModule,\n  declarations: [BoldReportDesignerComponent],\n  imports: [CommonModule],\n  exports: [BoldReportDesignerComponent]\n});\nBoldReportDesignerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportDesignerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [BoldReportDesignerComponent],\n      exports: [BoldReportDesignerComponent]\n    }]\n  }], null, null);\n})();\nclass BoldReportsModule {}\nBoldReportsModule.ɵfac = function BoldReportsModule_Factory(t) {\n  return new (t || BoldReportsModule)();\n};\nBoldReportsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BoldReportsModule,\n  imports: [CommonModule, BoldReportViewerModule, BoldReportDesignerModule],\n  exports: [BoldReportViewerModule, BoldReportDesignerModule]\n});\nBoldReportsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, BoldReportViewerModule, BoldReportDesignerModule], BoldReportViewerModule, BoldReportDesignerModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BoldReportsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, BoldReportViewerModule, BoldReportDesignerModule],\n      declarations: [],\n      exports: [BoldReportViewerModule, BoldReportDesignerModule]\n    }]\n  }], null, null);\n})();\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { ArrayTagElement, BoldReportComponents, BoldReportDesignerComponent, BoldReportDesignerModule, BoldReportViewerComponent, BoldReportViewerModule, BoldReportsModule, ComplexTagElement, Utils, currentTemplateElement };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AACJ,IAAI,WAAW,CAAC;AAEhB,IAAM,QAAN,MAAY;AAAA,EACV,OAAO,qBAAqB,KAAK;AAC/B,QAAI,GAAG,kBAAkB,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,KAAK,QAAQ,KAAK;AACrD,UAAI,MAAM,KAAK,CAAC,GACd,aAAa,IAAI,SAAS,IAAI,QAAQ,OAAO,GAAG,CAAC;AACnD,UAAI,CAAC,GAAG,kBAAkB,UAAU,KAAK,WAAW,YAAY;AAC9D,YAAI,GAAG,IAAI,WAAW,mBAAmB;AAAA,MAC3C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,MAAM,OAAO,SAAS,cAAc,GAAG;AACrC,SAAO,KAAK,KAAK,OAAO;AAC1B;AACA,MAAM,QAA0B,mBAAmB;AAAA,EACjD,OAAO;AAAA,EACP,SAAS,MAAM;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,uBAAN,MAAM,sBAAqB;AAAA;AAAA,EAEzB,YAAY,aAAa,IAAI,OAAO,MAAM,mBAAmB,oBAAoB;AAC/E,SAAK,cAAc;AACnB,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,QAAQ,CAAC;AACd,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,CAAC;AAEhB,SAAK,aAAa;AAClB,SAAK,aAAa,KAAK,kBAAkB,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;AAC7D,SAAK,gBAAgB,mBAAmB,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EAC1D;AAAA,EACA,OAAO,kBAAkB,UAAU,OAAO,OAAO;AAC/C,QAAI,CAAC,MAAM,WAAW,QAAQ,GAAG;AAE/B,UAAI,cAAc,MAAM,WAAW,MAAM,IAAI,MAAM,OAAO,CAAC,IAAI;AAC/D,YAAM,WAAW,IAAI,SAAU,QAAQ;AACrC,iBAAS,QAAQ,SAAS,EAAE,MAAM,EAAE,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,QAAQ,KAAK;AACjB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,UAAU,QAAQ,CAAC,EAAE,QAAQ,OAAO,GAAG;AAC3C,SAAG,aAAa,UAAU,UAAU,KAAK,QAAQ,CAAC,IAAI,YAAY,GAAG,KAAK;AAC1E,SAAG,aAAa,SAAS,KAAK,WAAW,OAAO,GAAG,KAAK;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,WAAW,MAAM;AACf,QAAI,QAAQ,KAAK,OACf,QAAQ;AACV,WAAO,SAAU,QAAQ,OAAO;AAC9B,UAAI,UAAU,UAAU;AACtB,gBAAQ,GAAG,UAAU,OAAO,QAAQ,KAAK;AACzC,YAAI,UAAU,QAAW;AACvB,kBAAQ,GAAG,UAAU,MAAM,SAAS,UAAa,KAAK,aAAa,SAAY,CAAC,IAAI,KAAK,QAAQ;AAAA,QACnG;AAAA,MACF;AACA,UAAI,WAAW,QAAW;AACxB,eAAO;AAAA,MACT;AACA,UAAI,UAAU,QAAQ;AACpB;AAAA,MACF;AACA,cAAQ;AACR,UAAI,CAAC,OAAO;AACV,WAAG,aAAa,OAAO,QAAQ,QAAQ,KAAK;AAC5C,WAAG,UAAU,OAAO,UAAU,KAAK,EAAE,KAAK,MAAM;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,aAAS,OAAO,MAAM;AACpB,UAAI,IAAI,QAAQ,QAAQ,KAAK,GAAI,MAAK,OAAO,KAAK,GAAG;AACrD,UAAI,IAAI,QAAQ,SAAS,KAAK,GAAI,MAAK,QAAQ,KAAK,GAAG;AACvD,UAAI,IAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,QAAQ,YAAY,KAAK,GAAI,MAAK,QAAQ,KAAK,IAAI,QAAQ,QAAQ,EAAE,CAAC;AAAA,IAC7G;AACA,QAAI,GAAG,kBAAkB,KAAK,SAAS,CAAC,GAAG;AACzC,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa;AAClB,QAAI,QAAQ,KAAK,OACf,SAAS,KAAK;AAChB,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,EAAE,QAAQ,WAAW,EAAE;AAC3C,8BAAqB,kBAAkB,MAAM,OAAO,KAAK;AAAA,MAC3D;AAAA,IACF;AACA,QAAI,GAAG,kBAAkB,KAAK,SAAS,CAAC,GAAG;AACzC,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACzC,YAAI,UAAU,KAAK,KAAK,CAAC,GACvB,OAAO,KAAK,SAAS,QAAQ,QAAQ,OAAO,GAAG,CAAC;AAClD,YAAI,CAAC,GAAG,kBAAkB,IAAI,GAAG;AAC/B,aAAG,aAAa,SAAS,KAAK,QAAQ,GAAG,KAAK,KAAK;AAAA,QACrD;AAAA,MACF;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,YAAI,WAAW,KAAK,OAAO,CAAC;AAC5B,YAAI,gBAAgB,KAAK,OAAO,CAAC,EAAE,QAAQ,UAAU,EAAE;AACvD,YAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,cAAI,cAAc,QAAQ,GAAG,KAAK,IAAI;AACpC,gBAAI,KAAK,MAAM,aAAa,GAAG;AAC7B,gBAAE,OAAO,MAAM,KAAK,MAAM,aAAa,GAAG,KAAK,QAAQ,CAAC;AAAA,YAC1D,OAAO;AACL,mBAAK,MAAM,aAAa,IAAI,KAAK,QAAQ;AAAA,YAC3C;AAAA,UACF,WAAW,cAAc,QAAQ,MAAM,KAAK,IAAI;AAC9C,gBAAI,UAAU,CAAC;AACf,gBAAI,MAAM,cAAc,QAAQ,OAAO,GAAG;AAC1C,eAAG,aAAa,KAAK,KAAK,QAAQ,GAAG,OAAO;AAC5C,gBAAI,WAAW,IAAI,MAAM,GAAG,EAAE,CAAC;AAC/B,gBAAI,KAAK,MAAM,QAAQ,KAAK,OAAW,MAAK,MAAM,QAAQ,IAAI,CAAC;AAC/D,cAAE,OAAO,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,YAAI,SAAS,KAAK,QAAQ,CAAC;AAC3B,YAAI,iBAAiB,SAAS;AAC9B,YAAI,KAAK,cAAc,KAAK,MAAM;AAChC,cAAI,OAAO,QAAQ,GAAG,KAAK,IAAI;AAC7B,iBAAK,MAAM,cAAc,IAAI,KAAK,cAAc;AAAA,UAClD,OAAO;AACL,gBAAI,UAAU,CAAC;AACf,gBAAI,MAAM,OAAO,QAAQ,OAAO,GAAG,IAAI;AACvC,eAAG,aAAa,KAAK,KAAK,cAAc,GAAG,OAAO;AAClD,gBAAI,WAAW,eAAe,MAAM,GAAG,EAAE,CAAC;AAC1C,cAAE,OAAO,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,MAAO,MAAK,QAAQ,OAAO,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,EAC/D;AAAA,EACA,YAAY;AACV,QAAI,GAAG,kBAAkB,KAAK,SAAS,CAAC,GAAG;AACzC,WAAK,QAAQ,QAAQ,aAAW;AAC9B,YAAI,KAAK,UAAU,MAAM,aAAa,OAAO;AAC3C,cAAI,UAAU,KAAK,WAAW,KAAK,KAAK,UAAU,MAAM,CAAC;AACzD,cAAI,SAAS;AACX,gBAAI,YAAY,CAAC;AACjB,gBAAI,KAAK,UAAU,QAAW;AAC5B,0BAAY,KAAK,iBAAiB,QAAQ,YAAY,WAAW,QAAQ,QAAQ,OAAO,GAAG,CAAC;AAC5F,iBAAG,aAAa,QAAQ,QAAQ,OAAO,GAAG,IAAI,QAAQ,QAAQ,YAAY,SAAS;AACnF,mBAAK,OAAO,UAAU,EAAE,WAAW,EAAE,cAAc,SAAS,CAAC;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,UAAU,KAAK,cAAc,KAAK,KAAK,SAAS,CAAC;AACrD,UAAI,SAAS;AACX,YAAI,KAAK,UAAU,QAAW;AAC5B,cAAI,YAAY,CAAC;AACjB,kBAAQ,mBAAmB,iBAAe;AACxC,eAAG,aAAa,YAAY,KAAK,YAAY,cAAc,SAAS;AAAA,UACtE,CAAC;AACD,eAAK,OAAO,UAAU,EAAE,WAAW,EAAE,cAAc,SAAS,CAAC;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,gBAAgB,KAAK,WAAW,EAAE,KAAK,GAAG,cAAc,QAAQ,IAAI,EAAE,KAAK,GAAG,aAAa;AAC/F,QAAI,cAAc,KAAK,qBAAqB,KAAK,WAAW;AAC5D,SAAK,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,KAAK,KAAK,EAAE,WAAW,EAAE,UAAU;AAAA,EACjF;AAAA,EACA,qBAAqB,QAAQ;AAC3B,WAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC;AAAA,EACjD;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,QAAI,YAAY,CAAC;AACjB,QAAI,GAAG,kBAAkB,KAAK,SAAS,CAAC,GAAG;AACzC,eAAS,OAAO,SAAS;AACvB,YAAI,UAAU,QAAQ,GAAG;AACzB,YAAI,QAAQ,kBAAkB,QAAQ,cAAc;AAClD;AAAA,QACF;AACA,cAAM,IAAI,QAAQ,UAAU,EAAE,EAAE,QAAQ,OAAO,GAAG;AAClD,YAAI,IAAI,SAAS,MAAM,GAAG;AACxB,cAAI,OAAO,IAAI,QAAQ,QAAQ,EAAE;AACjC,sBAAY,KAAK,iBAAiB,QAAQ,cAAc,WAAW,IAAI;AAAA,QACzE;AACA,WAAG,aAAa,KAAK,QAAQ,cAAc,SAAS;AAAA,MACtD;AACA,WAAK,OAAO,UAAU,EAAE,WAAW,EAAE,cAAc,SAAS,CAAC;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,WAAW,MAAM;AACvC,QAAI,QAAQ,GAAG,UAAU,MAAM,KAAK,OAAO,OAAO,CAAC;AACnD,UAAM,OAAO,IAAI;AACjB,OAAG,aAAa,MAAM,OAAO,SAAS;AACtC,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AAEtB,QAAI,UAAU,MAAM,qBAAqB,IAAI;AAC7C,aAAS,OAAO,SAAS;AACvB,UAAI,QAAQ,eAAe,GAAG,GAAG;AAC/B,YAAI,UAAU,QAAQ,GAAG;AACzB,aAAK,OAAO,MAAM,GAAG,EAAE,OAAO;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS,EAAE;AAAA,EACzB;AACF;AACA,qBAAqB,OAAO,SAAS,6BAA6B,GAAG;AACnE,EAAG,iBAAiB;AACtB;AACA,qBAAqB,QAA0B,mBAAmB;AAAA,EAChE,OAAO;AAAA,EACP,SAAS,qBAAqB;AAChC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,YAAY,CAAC;AAClB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,WAAO,eAAe,MAAM,UAAU;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AACD,6BAAyB;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,SAAK,cAAc;AACnB,aAAS,OAAO,MAAM;AACpB,UAAI,IAAI,QAAQ,GAAG,KAAK,MAAM,IAAI,QAAQ,MAAM,KAAK,GAAI,MAAK,UAAU,KAAK,GAAG;AAAA,IAClF;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,UAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,UAAI,SAAS,QAAQ,GAAG,KAAK,IAAI;AAC/B,YAAI,UAAU,CAAC;AACf,YAAI,MAAM,SAAS,QAAQ,OAAO,GAAG;AACrC,WAAG,aAAa,KAAK,KAAK,QAAQ,GAAG,OAAO;AAC5C,YAAI,WAAW,IAAI,MAAM,GAAG,EAAE,CAAC;AAC/B,YAAI,KAAK,QAAQ,KAAK,OAAW,MAAK,QAAQ,IAAI,CAAC;AACnD,UAAE,OAAO,MAAM,KAAK,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,OAAO,KAAK;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,UAAU,KAAK,CAAC,GAClB,aAAa,KAAK,SAAS,QAAQ,QAAQ,OAAO,GAAG,CAAC;AACxD,UAAI,MAAM,KAAK,KAAK,OAAO,GAAG;AAC5B;AAAA,MACF;AACA,UAAI,GAAG,kBAAkB,UAAU,GAAG;AACpC;AAAA,MACF;AACA,SAAG,aAAa,SAAS,WAAW,QAAQ,GAAG,IAAI;AAAA,IACrD;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AACA,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,eAAe,OAAO;AAC7B;AAAA,IACF;AACA,QAAI,UAAU,KAAK,iBAAiB,CAAC;AACrC,QAAI,iBAAiB,MAAM,qBAAqB,IAAI;AACpD,QAAI,CAAC,EAAE,cAAc,cAAc,GAAG;AACpC,eAAS,OAAO,gBAAgB;AAC9B,YAAI,eAAe,eAAe,GAAG,GAAG;AACtC,cAAI,UAAU,eAAe,GAAG;AAEhC,cAAI,CAAC,GAAG,kBAAkB,KAAK,OAAO,OAAO,MAAM,KAAK,SAAS,QAAQ,OAAO,GAAG,IAAI,MAAM,GAAG,CAAC,EAAG,MAAK,OAAO,OAAO,MAAM,KAAK,SAAS,QAAQ,OAAO,GAAG,IAAI,MAAM,GAAG,EAAE,OAAO;AAAA,QACrL;AAAA,MACF;AAAA,IACF;AACA,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,QAAI,OAAO,KAAK;AAChB,aAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,UAAI,UAAU,KAAK,CAAC,GAClB,aAAa,KAAK,SAAS,QAAQ,QAAQ,OAAO,GAAG,CAAC;AACxD,UAAI,cAAc,WAAW,YAAY;AACvC,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,kBAAkB,OAAO,SAAS,0BAA0B,GAAG;AAC7D,SAAO,KAAK,KAAK,mBAAsB,SAAS,KAAK,CAAC;AACxD;AACA,kBAAkB,QAA0B,mBAAmB;AAAA,EAC7D,OAAO;AAAA,EACP,SAAS,kBAAkB;AAC7B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,cAAc;AACxB,SAAK,eAAe;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,QAAQ;AACZ,SAAK,OAAO,KAAK,SAAS,IAAI,WAAS;AACrC,YAAM,QAAQ;AACd,YAAM,WAAW,KAAK;AACtB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,QAAI,OAAO,KAAK;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAK,CAAC,EAAE,kBAAkB;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,wBAAwB;AACtB,QAAI,UAAU,CAAC,GACb,MAAM,QAAQ,KAAK,YAAY,IAAI,CAAC,GACpC;AACF,aAAS,IAAI,GAAG,OAAO,KAAK,MAAM,IAAI,KAAK,QAAQ,KAAK;AACtD,UAAI,QAAQ,KAAK,CAAC;AAClB,UAAI,MAAM,YAAY;AACpB,sBAAc,MAAM,mBAAmB;AACvC,YAAI,CAAC,GAAG,kBAAkB,WAAW,GAAG;AACtC,cAAI,KAAK;AAAA,YACP,OAAO,MAAM;AAAA,YACb,QAAQ;AAAA,UACV,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,QAAI,IAAI,SAAS,GAAG;AAClB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;AACA,gBAAgB,OAAO,SAAS,wBAAwB,GAAG;AACzD,EAAG,iBAAiB;AACtB;AACA,gBAAgB,QAA0B,mBAAmB;AAAA,EAC3D,OAAO;AAAA,EACP,SAAS,gBAAgB;AAC3B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,4BAAN,cAAwC,qBAAqB;AAAA;AAAA,EAE3D,YAAY,IAAI,OAAO,oBAAoB,oBAAoB;AAC7D,UAAM,oBAAoB,IAAI,OAAO,CAAC,GAAG,oBAAoB,kBAAkB;AAC/E,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,2BAA2B,IAAI,aAAa;AACjD,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,yBAAyB,IAAI,aAAa;AAC/C,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,0BAA0B,IAAI,aAAa;AAChD,SAAK,+BAA+B,IAAI,aAAa;AACrD,SAAK,8BAA8B,IAAI,aAAa;AACpD,SAAK,yBAAyB,IAAI,aAAa;AAC/C,SAAK,0BAA0B,IAAI,aAAa;AAChD,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,4BAA4B,IAAI,aAAa;AAAA,EACpD;AACF;AACA,0BAA0B,OAAO,SAAS,kCAAkC,GAAG;AAC7E,SAAO,KAAK,KAAK,2BAA8B,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,eAAe,CAAC;AACjN;AACA,0BAA0B,OAAyB,kBAAkB;AAAA,EACnE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,EACjC,QAAQ;AAAA,IACN,mBAAmB,CAAI,WAAa,MAAM,eAAe,mBAAmB;AAAA,IAC5E,sBAAsB,CAAI,WAAa,MAAM,kBAAkB,sBAAsB;AAAA,IACrF,oBAAoB,CAAI,WAAa,MAAM,gBAAgB,oBAAoB;AAAA,IAC/E,cAAc,CAAI,WAAa,MAAM,UAAU,cAAc;AAAA,IAC7D,oBAAoB,CAAI,WAAa,MAAM,gBAAgB,oBAAoB;AAAA,IAC/E,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,0BAA0B,CAAI,WAAa,MAAM,sBAAsB,0BAA0B;AAAA,IACjG,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,iBAAiB,CAAI,WAAa,MAAM,aAAa,iBAAiB;AAAA,IACtE,mBAAmB,CAAI,WAAa,MAAM,eAAe,mBAAmB;AAAA,IAC5E,sBAAsB,CAAI,WAAa,MAAM,kBAAkB,sBAAsB;AAAA,IACrF,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,wBAAwB,CAAI,WAAa,MAAM,oBAAoB,wBAAwB;AAAA,IAC3F,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,6BAA6B,CAAI,WAAa,MAAM,yBAAyB,6BAA6B;AAAA,IAC1G,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,+BAA+B,CAAI,WAAa,MAAM,2BAA2B,+BAA+B;AAAA,IAChH,sBAAsB,CAAI,WAAa,MAAM,kBAAkB,sBAAsB;AAAA,IACrF,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,wBAAwB,CAAI,WAAa,MAAM,oBAAoB,wBAAwB;AAAA,IAC3F,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,8BAA8B,CAAI,WAAa,MAAM,0BAA0B,8BAA8B;AAAA,IAC7G,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,0BAA0B,CAAI,WAAa,MAAM,sBAAsB,0BAA0B;AAAA,IACjG,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,uCAAuC,CAAI,WAAa,MAAM,mCAAmC,uCAAuC;AAAA,IACxI,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,uCAAuC,CAAI,WAAa,MAAM,mCAAmC,uCAAuC;AAAA,IACxI,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,2CAA2C,CAAI,WAAa,MAAM,uCAAuC,2CAA2C;AAAA,IACpJ,4CAA4C,CAAI,WAAa,MAAM,wCAAwC,4CAA4C;AAAA,IACvJ,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,2CAA2C,CAAI,WAAa,MAAM,uCAAuC,2CAA2C;AAAA,IACpJ,sCAAsC,CAAI,WAAa,MAAM,kCAAkC,sCAAsC;AAAA,IACrI,6CAA6C,CAAI,WAAa,MAAM,yCAAyC,6CAA6C;AAAA,IAC1J,6BAA6B,CAAI,WAAa,MAAM,yBAAyB,6BAA6B;AAAA,IAC1G,6BAA6B,CAAI,WAAa,MAAM,yBAAyB,6BAA6B;AAAA,IAC1G,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,EACjI;AAAA,EACA,SAAS;AAAA,IACP,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,6BAA6B;AAAA,IAC7B,wBAAwB;AAAA,IACxB,yBAAyB;AAAA,IACzB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,EAC7B;AAAA,EACA,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAAA,EAAC;AAAA,EAChE,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,2CAA2C,CAAC;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,IACD,4CAA4C,CAAC;AAAA,MAC3C,MAAM;AAAA,MACN,MAAM,CAAC,sCAAsC;AAAA,IAC/C,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,2CAA2C,CAAC;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,IACD,sCAAsC,CAAC;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,6CAA6C,CAAC;AAAA,MAC5C,MAAM;AAAA,MACN,MAAM,CAAC,uCAAuC;AAAA,IAChD,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAA6B;AAAC;AAC9B,uBAAuB,OAAO,SAAS,+BAA+B,GAAG;AACvE,SAAO,KAAK,KAAK,wBAAwB;AAC3C;AACA,uBAAuB,OAAyB,iBAAiB;AAAA,EAC/D,MAAM;AAAA,EACN,cAAc,CAAC,yBAAyB;AAAA,EACxC,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,yBAAyB;AACrC,CAAC;AACD,uBAAuB,OAAyB,iBAAiB;AAAA,EAC/D,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,yBAAyB;AAAA,MACxC,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,8BAAN,cAA0C,qBAAqB;AAAA;AAAA,EAE7D,YAAY,IAAI,OAAO,oBAAoB,oBAAoB;AAC7D,UAAM,sBAAsB,IAAI,OAAO,CAAC,GAAG,oBAAoB,kBAAkB;AACjF,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,yBAAyB,IAAI,aAAa;AAC/C,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,yBAAyB,IAAI,aAAa;AAC/C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,0BAA0B,IAAI,aAAa;AAChD,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,qBAAqB,IAAI,aAAa;AAAA,EAC7C;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,GAAG;AACjF,SAAO,KAAK,KAAK,6BAAgC,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,eAAe,CAAC;AACnN;AACA,4BAA4B,OAAyB,kBAAkB;AAAA,EACrE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,EACnC,QAAQ;AAAA,IACN,6BAA6B,CAAI,WAAa,MAAM,yBAAyB,6BAA6B;AAAA,IAC1G,oBAAoB,CAAI,WAAa,MAAM,gBAAgB,oBAAoB;AAAA,IAC/E,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,mBAAmB,CAAI,WAAa,MAAM,eAAe,mBAAmB;AAAA,IAC5E,wBAAwB,CAAI,WAAa,MAAM,oBAAoB,wBAAwB;AAAA,IAC3F,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,0BAA0B,CAAI,WAAa,MAAM,sBAAsB,0BAA0B;AAAA,IACjG,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,iBAAiB,CAAI,WAAa,MAAM,aAAa,iBAAiB;AAAA,IACtE,cAAc,CAAI,WAAa,MAAM,UAAU,cAAc;AAAA,IAC7D,0BAA0B,CAAI,WAAa,MAAM,sBAAsB,0BAA0B;AAAA,IACjG,sBAAsB,CAAI,WAAa,MAAM,kBAAkB,sBAAsB;AAAA,IACrF,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,qBAAqB,CAAI,WAAa,MAAM,iBAAiB,qBAAqB;AAAA,IAClF,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,kBAAkB,CAAI,WAAa,MAAM,cAAc,kBAAkB;AAAA,IACzE,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,sCAAsC,CAAI,WAAa,MAAM,kCAAkC,sCAAsC;AAAA,IACrI,wBAAwB,CAAI,WAAa,MAAM,oBAAoB,wBAAwB;AAAA,IAC3F,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,8BAA8B,CAAI,WAAa,MAAM,0BAA0B,8BAA8B;AAAA,IAC7G,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,0BAA0B,CAAI,WAAa,MAAM,sBAAsB,0BAA0B;AAAA,IACjG,4BAA4B,CAAI,WAAa,MAAM,wBAAwB,4BAA4B;AAAA,IACvG,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,uBAAuB,CAAI,WAAa,MAAM,mBAAmB,uBAAuB;AAAA,IACxF,2BAA2B,CAAI,WAAa,MAAM,uBAAuB,2BAA2B;AAAA,IACpG,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,yBAAyB,CAAI,WAAa,MAAM,qBAAqB,yBAAyB;AAAA,IAC9F,uCAAuC,CAAI,WAAa,MAAM,mCAAmC,uCAAuC;AAAA,IACxI,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,oCAAoC,CAAI,WAAa,MAAM,gCAAgC,oCAAoC;AAAA,IAC/H,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,4CAA4C,CAAI,WAAa,MAAM,wCAAwC,4CAA4C;AAAA,IACvJ,8CAA8C,CAAI,WAAa,MAAM,0CAA0C,8CAA8C;AAAA,IAC7J,mDAAmD,CAAI,WAAa,MAAM,+CAA+C,mDAAmD;AAAA,IAC5K,oDAAoD,CAAI,WAAa,MAAM,gDAAgD,oDAAoD;AAAA,IAC/K,2CAA2C,CAAI,WAAa,MAAM,uCAAuC,2CAA2C;AAAA,IACpJ,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,gCAAgC,CAAI,WAAa,MAAM,4BAA4B,gCAAgC;AAAA,IACnH,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,IACzH,0CAA0C,CAAI,WAAa,MAAM,sCAAsC,0CAA0C;AAAA,IACjJ,sCAAsC,CAAI,WAAa,MAAM,kCAAkC,sCAAsC;AAAA,IACrI,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,sCAAsC,CAAI,WAAa,MAAM,kCAAkC,sCAAsC;AAAA,IACrI,uCAAuC,CAAI,WAAa,MAAM,mCAAmC,uCAAuC;AAAA,IACxI,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,iCAAiC,CAAI,WAAa,MAAM,6BAA6B,iCAAiC;AAAA,IACtH,sCAAsC,CAAI,WAAa,MAAM,kCAAkC,sCAAsC;AAAA,IACrI,uCAAuC,CAAI,WAAa,MAAM,mCAAmC,uCAAuC;AAAA,IACxI,wCAAwC,CAAI,WAAa,MAAM,oCAAoC,wCAAwC;AAAA,IAC3I,qCAAqC,CAAI,WAAa,MAAM,iCAAiC,qCAAqC;AAAA,IAClI,8CAA8C,CAAI,WAAa,MAAM,0CAA0C,8CAA8C;AAAA,IAC7J,6BAA6B,CAAI,WAAa,MAAM,yBAAyB,6BAA6B;AAAA,IAC1G,mCAAmC,CAAI,WAAa,MAAM,+BAA+B,mCAAmC;AAAA,IAC5H,kCAAkC,CAAI,WAAa,MAAM,8BAA8B,kCAAkC;AAAA,EAC3H;AAAA,EACA,SAAS;AAAA,IACP,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,wBAAwB;AAAA,IACxB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACtB;AAAA,EACA,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAAA,EAAC;AAAA,EAClE,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,sCAAsC,CAAC;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oCAAoC,CAAC;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,4CAA4C,CAAC;AAAA,MAC3C,MAAM;AAAA,MACN,MAAM,CAAC,sCAAsC;AAAA,IAC/C,CAAC;AAAA,IACD,8CAA8C,CAAC;AAAA,MAC7C,MAAM;AAAA,MACN,MAAM,CAAC,wCAAwC;AAAA,IACjD,CAAC;AAAA,IACD,mDAAmD,CAAC;AAAA,MAClD,MAAM;AAAA,MACN,MAAM,CAAC,6CAA6C;AAAA,IACtD,CAAC;AAAA,IACD,oDAAoD,CAAC;AAAA,MACnD,MAAM;AAAA,MACN,MAAM,CAAC,8CAA8C;AAAA,IACvD,CAAC;AAAA,IACD,2CAA2C,CAAC;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM,CAAC,qCAAqC;AAAA,IAC9C,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,0CAA0C,CAAC;AAAA,MACzC,MAAM;AAAA,MACN,MAAM,CAAC,oCAAoC;AAAA,IAC7C,CAAC;AAAA,IACD,sCAAsC,CAAC;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,sCAAsC,CAAC;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,sCAAsC,CAAC;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,IACD,wCAAwC,CAAC;AAAA,MACvC,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,qCAAqC,CAAC;AAAA,MACpC,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,8CAA8C,CAAC;AAAA,MAC7C,MAAM;AAAA,MACN,MAAM,CAAC,wCAAwC;AAAA,IACjD,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAA+B;AAAC;AAChC,yBAAyB,OAAO,SAAS,iCAAiC,GAAG;AAC3E,SAAO,KAAK,KAAK,0BAA0B;AAC7C;AACA,yBAAyB,OAAyB,iBAAiB;AAAA,EACjE,MAAM;AAAA,EACN,cAAc,CAAC,2BAA2B;AAAA,EAC1C,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,2BAA2B;AACvC,CAAC;AACD,yBAAyB,OAAyB,iBAAiB;AAAA,EACjE,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,2BAA2B;AAAA,MAC1C,SAAS,CAAC,2BAA2B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAC;AACzB,kBAAkB,OAAO,SAAS,0BAA0B,GAAG;AAC7D,SAAO,KAAK,KAAK,mBAAmB;AACtC;AACA,kBAAkB,OAAyB,iBAAiB;AAAA,EAC1D,MAAM;AAAA,EACN,SAAS,CAAC,cAAc,wBAAwB,wBAAwB;AAAA,EACxE,SAAS,CAAC,wBAAwB,wBAAwB;AAC5D,CAAC;AACD,kBAAkB,OAAyB,iBAAiB;AAAA,EAC1D,SAAS,CAAC,CAAC,cAAc,wBAAwB,wBAAwB,GAAG,wBAAwB,wBAAwB;AAC9H,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,wBAAwB,wBAAwB;AAAA,MACxE,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,wBAAwB,wBAAwB;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}