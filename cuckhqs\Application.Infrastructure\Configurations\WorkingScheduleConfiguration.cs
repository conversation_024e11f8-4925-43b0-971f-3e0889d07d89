﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class WorkingScheduleConfiguration : IEntityTypeConfiguration<WorkingScheduleEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleEntity> builder)
        {
            builder.ToTable("WorkingSchedule");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.Classify).HasColumnName("Classify");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
            builder.Property(x => x.Register).HasColumnName("Register");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Week).HasColumnName("Week");
            builder.Property(x => x.Date).HasColumnName("Date");
            builder.Property(x => x.CoChair).HasColumnName("CoChair");
            builder.Property(x => x.Time).HasColumnName("Time");
            builder.Property(x => x.TimeFrom).HasColumnName("TimeFrom");
            builder.Property(x => x.TimeTo).HasColumnName("TimeTo");
            builder.Property(x => x.Place).HasColumnName("Place");
            builder.Property(x => x.Contents).HasColumnName("Contents");
            builder.Property(x => x.Member).HasColumnName("Member");
            builder.Property(x => x.Note).HasColumnName("Note");
            builder.Property(x => x.Message).HasColumnName("Message");
            builder.Property(x => x.Announced).HasColumnName("Announced");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
            builder.Property(x => x.OrganizationUnitId_Chair).HasColumnName("OrganizationUnitId_Chair");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");

            builder.HasMany(x => x.WorkingScheduleC)
                    .WithOne(ws => ws.WorkingSchedule)
                    .HasForeignKey(ws => ws.WorkingScheduleId)
                    .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(x => x.WorkingScheduleEP)
                   .WithOne(ws => ws.WorkingSchedule)
                   .HasForeignKey(ws => ws.WorkingScheduleId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(x => x.WorkingScheduleEPH)
                   .WithOne(ws => ws.WorkingSchedule)
                   .HasForeignKey(ws => ws.WorkingScheduleId)
                   .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(x => x.WorkingScheduleResult)
                .WithOne(ws => ws.WorkingSchedule)
                .HasForeignKey(ws => ws.WorkingScheduleId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
