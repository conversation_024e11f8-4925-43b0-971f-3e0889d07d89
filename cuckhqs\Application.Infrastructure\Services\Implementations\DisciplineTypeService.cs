﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.DisciplineType;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class DisciplineTypeService : IDisciplineTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public DisciplineTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<DisciplineTypeResponse>> SearchDisciplineTypeAsync(SearchDisciplineTypeRequest request)
        {
            try
            {
                IQueryable<DisciplineTypeResponse> query = _unitOfWork.DisciplineType.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.DisciplineTypeCode.Contains(request.keyword) ||
                                x.DisciplineTypeName.Contains(request.keyword)
                                ))
                    .Select(s => new DisciplineTypeResponse()
                    {
                        Id = s.Id,
                        DisciplineTypeCode = s.DisciplineTypeCode,
                        DisciplineTypeName = s.DisciplineTypeName,
                        Class = s.Class,
                        Year = s.Year,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<DisciplineTypeResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<DisciplineTypeResponse> CreateDisciplineTypeAsync(CreateDisciplineTypeRequest request)
        {
            try
            {
                var entity = CreateDisciplineTypeRequest.Create(request);

                await _unitOfWork.DisciplineType.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return DisciplineTypeResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteDisciplineTypeAsync(DeleteDisciplineTypeRequest request)
        {
            try
            {
                var record = await _unitOfWork.DisciplineType.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.DisciplineType.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateDisciplineTypeAsync(UpdateDisciplineTypeRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.DisciplineType.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateDisciplineTypeRequest.Create(request);

                await _unitOfWork.DisciplineType.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
