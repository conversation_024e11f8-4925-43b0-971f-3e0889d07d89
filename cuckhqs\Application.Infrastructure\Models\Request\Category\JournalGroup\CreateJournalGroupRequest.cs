﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.JournalGroup
{
    public class CreateJournalGroupRequest
    {
        public string? JournalGroupCode { get; set; }
        public string? JournalGroupName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateJournalGroupRequest, JournalGroupEntity>> Expression
        {
            get
            {
                return entity => new JournalGroupEntity
                {
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupName = entity.JournalGroupName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalGroupEntity Create(CreateJournalGroupRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
