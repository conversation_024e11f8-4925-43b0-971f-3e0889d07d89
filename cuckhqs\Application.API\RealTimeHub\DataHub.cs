﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using OpenIddict.Validation.AspNetCore;

namespace Application.API.RealTimeHub
{
    [Authorize]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    public class DataHub : Hub
    {
        public async Task NotifyDataUpdated(string dataType)
        {
            await Clients.All.SendAsync("dataUpdated", dataType);
        }
    }
}
