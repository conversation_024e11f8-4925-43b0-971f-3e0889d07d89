﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Application.Infrastructure.Repositories.Implementations.EmployeeRepository
{
    public class EmployeeCRepository : GenericRepository<EmployeeCEntity, Guid>, IEmployeeRepository
    {
        public AppDbContext _context { get; set; }

        //public EmployeeCRepository(AppDbContext context)
        //{
        //    _context = context;
        //}

        public EmployeeCRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }


        public async Task<List<EmployeeCEntity>> GetEmployeesByOrgUnitIdAsync(int orgUnitId)
        {
            return await _context.Employees
                .Where(e => e.OrganizationUnitId == orgUnitId)
                .ToListAsync();
        }
        public async Task<List<EmployeeCEntity>> GetAllEmployeesAsync()
        {
            return await _context.Employees.ToListAsync();
        }

        protected override void Update(EmployeeCEntity requestObject, EmployeeCEntity targetObject)
        {
            targetObject.FirstName = requestObject.FirstName;
            targetObject.LastName = requestObject.LastName;
            targetObject.OrganizationUnitId = requestObject.OrganizationUnitId;
            targetObject.Classify = requestObject.Classify;
            targetObject.EmployeeCode = requestObject.EmployeeCode;
            targetObject.FullName = requestObject.FullName;
            targetObject.RankId = requestObject.RankId;
            targetObject.Gender = requestObject.Gender;
            targetObject.AcademicRankId = requestObject.AcademicRankId;
            targetObject.YearOfAcademicRank = requestObject.YearOfAcademicRank;
            targetObject.DegreeId = requestObject.DegreeId;
            targetObject.YearOfDegree = requestObject.YearOfDegree;
            targetObject.PositionId = requestObject.PositionId;
            targetObject.PositionType = requestObject.PositionType;
            targetObject.PartyPositionId = requestObject.PartyPositionId;
            targetObject.BirthDay = requestObject.BirthDay;
            targetObject.Owned = requestObject.Owned;
            targetObject.Active = requestObject.Active;
            targetObject.ActiveAccount = requestObject.ActiveAccount;
            targetObject.IsAdministrator = requestObject.IsAdministrator;
            targetObject.LongFullName = requestObject.LongFullName;
            targetObject.BirthPlace = requestObject.BirthPlace;
            targetObject.HomeLand = requestObject.HomeLand;
            targetObject.NativeAddress = requestObject.NativeAddress;
            targetObject.Tel = requestObject.Tel;
            targetObject.HomeTel = requestObject.HomeTel;
            targetObject.Mobile = requestObject.Mobile;
            targetObject.Fax = requestObject.Fax;
            targetObject.Email = requestObject.Email;
            targetObject.OfficeAddress = requestObject.OfficeAddress;
            targetObject.HomeAddress = requestObject.HomeAddress;
            targetObject.Website = requestObject.Website;
            targetObject.Description = requestObject.Description;
            targetObject.IDNumber = requestObject.IDNumber;
            targetObject.IssuedBy = requestObject.IssuedBy;
            targetObject.DateBy = requestObject.DateBy;
            targetObject.AccountNumber = requestObject.AccountNumber;
            targetObject.Bank = requestObject.Bank;
            targetObject.Avatar = requestObject.Avatar;
            targetObject.SortOrder = requestObject.SortOrder;
        }
    }
}
