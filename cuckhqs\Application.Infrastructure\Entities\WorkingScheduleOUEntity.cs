﻿using Application.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleOUEntity : BaseEntity<int>
    {
        public int OrganizationUnitId { get; set; }
        public int WorkingScheduleId { get; set; }
        //[NotMapped]
        //public string OrganizationUnitName { get; set; }
        public WorkingScheduleEntity WorkingSchedule { get; set; }
    }
}
