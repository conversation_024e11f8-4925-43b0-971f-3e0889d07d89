﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.JournalGroup;
using Application.Infrastructure.Models.Request.Category.JournalType;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IJournalGroupService
    {
        Task<BaseSearchResponse<JournalGroupResponse>> SearchJournalGroupAsync(SearchJournalGroupRequest request);
        Task<JournalGroupResponse> CreateJournalGroupAsync(CreateJournalGroupRequest request);
        Task<bool> UpdateJournalGroupAsync(UpdateJournalGroupRequest request);
        Task<bool> DeleteJournalGroupAsync(DeleteJournalGroupRequest request);
    }
}
