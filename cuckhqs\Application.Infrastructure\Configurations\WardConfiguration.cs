﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class WardConfiguration : IEntityTypeConfiguration<WardEntity>
    {
        public void Configure(EntityTypeBuilder<WardEntity> builder)
        {
            builder.ToTable("Ward");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.ProvinceId).HasColumnName("ProvinceId");
            builder.Property(x => x.DistrictId).HasColumnName("DistrictId");
            builder.Property(x => x.WardCode).HasColumnName("WardCode");
            builder.Property(x => x.WardName).HasColumnName("WardName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
