﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response
{
    public class WorkingScheduleResultResponse
    {
        public bool Success { get; set; }
        public int ErrorType { get; set; }
        public List<ScheduleData1> Data { get; set; }
        public int Id { get; set; }
        public int Total { get; set; }
        public int Code { get; set; }
    }

    public class ScheduleData1
    {
        public int Id { get; set; }
        public int? WorkingScheduleId { get; set; }
        public DateTime? Date { get; set; }
        public string? Result { get; set; }
        public bool? Active { get; set; }
        public string? OldListFile { get; set; }
    }

    public class FileResponseget
    {
        public string? Contents { get; set; }
        public string? FileName { get; set; }
        public string? FileType { get; set; }
        public double? FileSize { get; set; }
    }

    public class WorkingScheduleResultResponse_Search
    {
        public int Id { get; set; }
        public int WorkingScheduleId { get; set; }
        public DateTime? Date { get; set; }
        public string? Result { get; set; }
        public bool? Active { get; set; }

        public List<FileResponseget>? Files { get; set; }
    }
}
