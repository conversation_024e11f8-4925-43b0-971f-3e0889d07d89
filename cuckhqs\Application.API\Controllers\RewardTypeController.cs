﻿using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.RewardType;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class RewardTypeController : ControllerBase
    {
        private readonly IRewardTypeService _rewardTypeService;

        public RewardTypeController(IRewardTypeService rewardTypeService)
        {
            _rewardTypeService = rewardTypeService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchRewardTypeAsync([FromBody] SearchRewardTypeRequest request)
        {
            try
            {
                var response = await _rewardTypeService.SearchRewardTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateRewardTypeAsync([FromBody] CreateRewardTypeRequest request)
        {
            var response = await _rewardTypeService.CreateRewardTypeAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateRewardTypeAsync([FromBody] UpdateRewardTypeRequest request)
        {
            try
            {
                var response = await _rewardTypeService.UpdateRewardTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteRewardTypeAsync([FromBody] DeleteRewardTypeRequest request)
        {
            try
            {
                var response = await _rewardTypeService.DeleteRewardTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
