﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.OrganizationUnit
{
    public class CreatePositionRequest
    {
        public int? ParentId{ get; set; }
        public string? PositionCode { get; set; }
        public string? PositionName { get; set; }
        public bool? IsRoot { get; set; }
        public short? Classify { get; set; }
        public string? ShortPositionName { get; set; }
        public string? FullPositionName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }

        public static Expression<Func<CreatePositionRequest, PositionEntity>> Expression
        {
            get
            {
                return entity => new PositionEntity
                {
                    ParentId = entity.ParentId,
                    PositionCode = entity.PositionCode,
                    PositionName = entity.PositionName,
                    IsRoot = entity.IsRoot,
                    Classify = entity.Classify,
                    ShortPositionName = entity.ShortPositionName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static PositionEntity Create(CreatePositionRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
