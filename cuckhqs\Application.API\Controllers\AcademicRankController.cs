﻿using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Azure.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class AcademicRankController : ControllerBase
    {
        private readonly IAcademicRankService _academicRankService;

        public AcademicRankController(IAcademicRankService academicRankervice)
        {
            _academicRankService = academicRankervice;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchAcademicRankAsync([FromBody] SearchAcademicRankRequest request)
        {
            try
            {
                var response = await _academicRankService.SearchAcademicRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateAcademicRankAsync([FromBody] CreateAcademicRankRequest request)
        {
            var response = await _academicRankService.CreateAcademicRankAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateAcademicRankAsync([FromBody] UpdateAcademicRankRequest request)
        {
            try
            {
                var response = await _academicRankService.UpdateAcademicRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteAcademicRankAsync([FromBody] DeleteAcademicRankRequest request)
        {
            try
            {
                var response = await _academicRankService.DeleteAcademicRankAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
