﻿using System.Linq.Expressions;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.WorkingResultSynthetic;

public class UpdateWorkingResultSyntheticRequest: CreateWorkingResultSyntheticRequest
{
    public int Id { get; set; }

    public static Expression<Func<UpdateWorkingResultSyntheticRequest, WorkingResultSyntheticEntity>> Expression
    {
        get
        {
            return entity => new WorkingResultSyntheticEntity
            {
                Id = entity.Id,
                OrganizationUnitId = entity.OrganizationUnitId,
                Year = entity.Year,
                Week = entity.Week,
                Contents = entity.Contents,
                Active = entity.Active,
                Announced = entity.Announced,
                CreatedDate = entity.CreatedDate,
                CreatedBy = entity.CreatedBy,
                SortOrder = entity.SortOrder,
                ModifiedDate = entity.ModifiedDate,
                ModifiedBy = entity.ModifiedBy,
                IPAddress = entity.IPAddress
            };
        }
    }
    public static WorkingResultSyntheticEntity Create(UpdateWorkingResultSyntheticRequest request)
    {
        return Expression.Compile().Invoke(request);
    }
}