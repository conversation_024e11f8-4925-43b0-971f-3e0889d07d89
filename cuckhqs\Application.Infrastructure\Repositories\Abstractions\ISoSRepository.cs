﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions
{
    public interface ISoSRepository : IRepository<SoSEntity, int>
    {
        // Task<OrganizationUnitEntity> GetBureauChiefUnitAsync();
        Task<List<SoSEntity>> GetAllSoSAsync(int type);
    }
}
