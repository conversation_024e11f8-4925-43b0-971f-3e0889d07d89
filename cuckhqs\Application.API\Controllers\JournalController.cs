﻿using Application.Infrastructure.Models.Request.Category.Journal;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class JournalController : ControllerBase
    {
        private readonly IJournalService _journalService;

        public JournalController(IJournalService journalService)
        {
            _journalService = journalService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchJournalAsync([FromBody] SearchJournalRequest request)
        {
            try
            {
                var response = await _journalService.SearchJournalAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateJournalAsync([FromBody] CreateJournalRequest request)
        {
            var response = await _journalService.CreateJournalAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateJournalAsync([FromBody] UpdateJournalRequest request)
        {
            try
            {
                var response = await _journalService.UpdateJournalAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteJournalAsync([FromBody] DeleteJournalRequest request)
        {
            try
            {
                var response = await _journalService.DeleteJournalAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
