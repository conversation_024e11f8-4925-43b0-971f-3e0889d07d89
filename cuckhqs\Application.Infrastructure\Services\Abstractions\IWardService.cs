﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.Ward;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IWardService
    {
        Task<BaseSearchResponse<WardResponse>> SearchWardAsync(SearchWardRequest request);
        Task<WardResponse> CreateWardAsync(CreateWardRequest request);
        Task<bool> UpdateWardAsync(UpdateWardRequest request);
        Task<bool> DeleteWardAsync(DeleteWardRequest request);
    }
}
