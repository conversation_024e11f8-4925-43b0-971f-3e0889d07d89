﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;

namespace Application.Infrastructure.Services.Implementations
{
    public class AcademicRankService : IAcademicRankService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public AcademicRankService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<AcademicRankResponse>> SearchAcademicRankAsync(SearchAcademicRankRequest request)
        {
            try
            {
                IQueryable<AcademicRankResponse> query = _unitOfWork.AcademicRank.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.AcademicRankCode.Contains(request.keyword) ||
                                x.AcademicRankName.Contains(request.keyword) ||
                                x.AcademicRankShortName.Contains(request.keyword) 
                                ))
                    .Select(s => new AcademicRankResponse()
                    {
                        Id = s.Id,
                        AcademicRankCode = s.AcademicRankCode,
                        AcademicRankName = s.AcademicRankName,
                        AcademicRankShortName = s.AcademicRankShortName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<AcademicRankResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<AcademicRankResponse> CreateAcademicRankAsync(CreateAcademicRankRequest request)
        {
            try
            {
                var entity = CreateAcademicRankRequest.Create(request);

                await _unitOfWork.AcademicRank.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return AcademicRankResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteAcademicRankAsync(DeleteAcademicRankRequest request)
        {
            try
            {
                var record = await _unitOfWork.AcademicRank.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.AcademicRank.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateAcademicRankAsync(UpdateAcademicRankRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.AcademicRank.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateAcademicRankRequest.Create(request);

                await _unitOfWork.AcademicRank.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
