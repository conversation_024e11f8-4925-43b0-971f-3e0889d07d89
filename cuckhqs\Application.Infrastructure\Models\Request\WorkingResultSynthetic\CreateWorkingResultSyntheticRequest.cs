﻿using System.Linq.Expressions;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.WorkingResultSynthetic;

public class CreateWorkingResultSyntheticRequest
{
    public int? OrganizationUnitId { get; set; }
    public int? Year { get; set; }
    public int? Week { get; set; }
    public string? Contents { get; set; }
    public int? Announced { get; set; }
    public bool? Active { get; set; }
    public short? SortOrder { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string? IPAddress { get; set; }
    public string? CreatedBy { get; set; }
    public string? ModifiedBy { get; set; }

    public static Expression<Func<CreateWorkingResultSyntheticRequest, WorkingResultSyntheticEntity>> Expression
    {
        get
        {
            return entity => new WorkingResultSyntheticEntity
            {
                OrganizationUnitId = entity.OrganizationUnitId,
                Year = entity.Year,
                Week = entity.Week,
                Contents = entity.Contents,
                Active = entity.Active,
                Announced = entity.Announced,
                CreatedDate = entity.CreatedDate,
                CreatedBy = entity.CreatedBy,
                SortOrder = entity.SortOrder,
                ModifiedDate = entity.ModifiedDate,
                ModifiedBy = entity.ModifiedBy,
                IPAddress = entity.IPAddress
            };
        }
    }
    public static WorkingResultSyntheticEntity Create(CreateWorkingResultSyntheticRequest request)
    {
        return Expression.Compile().Invoke(request);
    }
}