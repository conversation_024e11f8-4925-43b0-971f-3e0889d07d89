﻿[*.cs]

# CS8600: Converting null literal or possible null value to non-nullable type.
dotnet_diagnostic.CS8600.severity = silent

# IDE0008: Use explicit type
dotnet_diagnostic.IDE0008.severity = silent

# CS8604: Possible null reference argument.
dotnet_diagnostic.CS8604.severity = silent

# CS8603: Possible null reference return.
dotnet_diagnostic.CS8603.severity = silent

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
dotnet_diagnostic.CS8618.severity = silent

# CS8602: Dereference of a possibly null reference.
dotnet_diagnostic.CS8602.severity = silent

# CS8619: Nullability of reference types in value doesn't match target type.
dotnet_diagnostic.CS8619.severity = silent
