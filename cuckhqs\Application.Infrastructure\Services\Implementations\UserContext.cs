﻿using Application.Infrastructure.Services.Abstractions;

namespace Application.Infrastructure.Services.Implementations
{
    public class UserContext : IUserContext
    {
        private int _id;
        private string _name;
        private string _roleName;

        public int Id => _id;

        public string Name => _name;

        public string RoleName => _roleName;

        public IUserContext SetId(int id)
        {
            _id = id;
            return this;
        }

        public IUserContext SetName(string name)
        {
            _name = name;
            return this;
        }

        public IUserContext SetRoleName(string role)
        {
            _roleName = role;
            return this;
        }
    }
}
