﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.WorkingResultSynthetic;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;


namespace Application.Infrastructure.Services.Implementations;

public class WorkingResultSyntheticService : IWorkingResultSyntheticService
{
    private readonly IUnitOfWork _unitOfWork;
    private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

    public WorkingResultSyntheticService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<BaseSearchResponse<WorkingResultSyntheticResponse>> SearchWorkingResultSyntheticAsync(
        SearchWorkingResultSyntheticRequest request)
    {
        try
        {
            IQueryable<WorkingResultSyntheticResponse> query = _unitOfWork.WorkingResultSynthetic.AsQueryable()
                .AsNoTracking()
                .Where(x => (request.org == x.OrganizationUnitId.ToString()) && (request.year == x.Year.ToString()) &&
                            (request.week == x.Week.ToString())).Select(s => new WorkingResultSyntheticResponse
                {
                    Id = s.Id,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Year = s.Year,
                    Week = s.Week,
                    Contents = s.Contents,
                    Active = s.Active,
                    SortOrder = s.SortOrder,
                    CreatedBy = s.CreatedBy,
                    CreatedDate = s.CreatedDate,
                    ModifiedBy = s.ModifiedBy,
                    ModifiedDate = s.ModifiedDate,
                    IPAddress = s.IPAddress,
                });
            return await BaseSearchResponse<WorkingResultSyntheticResponse>.GetResponse(query, request);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại SearchWorkingResultSyntheticAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }
    public async Task<WorkingResultSyntheticResponse> CreateWorkingResultSyntheticAsync(
        CreateWorkingResultSyntheticRequest request)
    {
        try
        {
            var entity = CreateWorkingResultSyntheticRequest.Create(request);

            await _unitOfWork.WorkingResultSynthetic.AddAsync(entity);
            await _unitOfWork.CommitChangesAsync();
            return WorkingResultSyntheticResponse.Create(entity);
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại CreateWorkingResultSyntheticAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }
    public async Task<bool> UpdateWorkingResultSyntheticAsync(UpdateWorkingResultSyntheticRequest request)
    {
        try
        {
            var findRecord = await _unitOfWork.WorkingResultSynthetic.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

            if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

            var updateRecord = UpdateWorkingResultSyntheticRequest.Create(request);

            await _unitOfWork.WorkingResultSynthetic.UpdateAsync(request.Id, updateRecord);
            await _unitOfWork.CommitChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại UpdateWorkingResultSyntheticAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }

    public async Task<bool> DeleteWorkingResultSyntheticAsync(DeleteWorkingResultSyntheticRequest request)
    {
        try
        {
            var record = await _unitOfWork.WorkingResultSynthetic.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();
            _unitOfWork.WorkingResultSynthetic.RemoveRange(record);
            await _unitOfWork.CommitChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            log.Error("Lỗi tại DeleteWorkingResultSyntheticAsync: " + ex.ToString());
            throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
        }
    }
}