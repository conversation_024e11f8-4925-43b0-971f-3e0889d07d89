﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IDegreeService
    {
        Task<BaseSearchResponse<DegreeResponse>> SearchDegreeAsync(SearchDegreeRequest request);
        Task<DegreeResponse> CreateDegreeAsync(CreateDegreeRequest request);
        Task<bool> UpdateDegreeAsync(UpdateDegreeRequest request);
        Task<bool> DeleteDegreeAsync(DeleteDegreeRequest request);
    }
}
