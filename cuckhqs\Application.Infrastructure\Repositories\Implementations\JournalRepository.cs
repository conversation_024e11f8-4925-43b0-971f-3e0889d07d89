﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Vml.Office;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class JournalRepository : GenericRepository<JournalEntity, int>, IJournalRepository
    {
        public AppDbContext Context { get; set; }

        public JournalRepository(AppDbContext context) : base(context)
        {
            Context = context;
        }

        public async Task<List<JournalGroupEntity>> GetAllJournalGroupAsync()
        {
            return await Context.JournalGroup.ToListAsync();
        }

        public async Task<List<JournalTypeEntity>> GetAllJournalTypeAsync()
        {
            return await Context.JournalType.ToListAsync();
        }

        protected override void Update(JournalEntity requestObject, JournalEntity targetObject)
        {
            targetObject.JournalCode = requestObject.JournalCode;
            targetObject.JournalName = requestObject.JournalName;
            targetObject.ISSN = requestObject.ISSN;
            targetObject.JournalTypeId = requestObject.JournalTypeId;
            targetObject.JournalTypeCode = requestObject.JournalTypeCode;
            targetObject.JournalTypeId_AN = requestObject.JournalTypeId_AN;
            targetObject.JournalGroupId = requestObject.JournalGroupId;
            targetObject.JournalGroupCode = requestObject.JournalGroupCode;
            targetObject.JournalGroupId_AN = requestObject.JournalGroupId_AN;
            targetObject.PublishingAgency = requestObject.PublishingAgency;
            targetObject.PointFrom = requestObject.PointFrom;
            targetObject.PointTo = requestObject.PointTo;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}
