{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * Divider is used to separate contents.\n * @group Components\n */\nconst _c0 = [\"*\"];\nclass Divider {\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Specifies the orientation.\n   * @group Props\n   */\n  layout = 'horizontal';\n  /**\n   * Border style type.\n   * @group Props\n   */\n  type = 'solid';\n  /**\n   * Alignment of the content.\n   * @group Props\n   */\n  align;\n  containerClass() {\n    return {\n      'p-divider p-component': true,\n      'p-divider-horizontal': this.layout === 'horizontal',\n      'p-divider-vertical': this.layout === 'vertical',\n      'p-divider-solid': this.type === 'solid',\n      'p-divider-dashed': this.type === 'dashed',\n      'p-divider-dotted': this.type === 'dotted',\n      'p-divider-left': this.layout === 'horizontal' && (!this.align || this.align === 'left'),\n      'p-divider-center': this.layout === 'horizontal' && this.align === 'center' || this.layout === 'vertical' && (!this.align || this.align === 'center'),\n      'p-divider-right': this.layout === 'horizontal' && this.align === 'right',\n      'p-divider-top': this.layout === 'vertical' && this.align === 'top',\n      'p-divider-bottom': this.layout === 'vertical' && this.align === 'bottom'\n    };\n  }\n  static ɵfac = function Divider_Factory(t) {\n    return new (t || Divider)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Divider,\n    selectors: [[\"p-divider\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      layout: \"layout\",\n      type: \"type\",\n      align: \"align\"\n    },\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 6,\n    consts: [[\"role\", \"separator\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-divider-content\"]],\n    template: function Divider_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"aria-orientation\", ctx.layout)(\"data-pc-name\", \"divider\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-divider-horizontal{display:flex;width:100%;position:relative;align-items:center}.p-divider-horizontal:before{position:absolute;display:block;top:50%;left:0;width:100%;content:\\\"\\\"}.p-divider-horizontal.p-divider-left{justify-content:flex-start}.p-divider-horizontal.p-divider-right{justify-content:flex-end}.p-divider-horizontal.p-divider-center{justify-content:center}.p-divider-content{z-index:1}.p-divider-vertical{min-height:100%;margin:0 1rem;display:flex;position:relative;justify-content:center}.p-divider-vertical:before{position:absolute;display:block;top:0;left:50%;height:100%;content:\\\"\\\"}.p-divider-vertical.p-divider-top{align-items:flex-start}.p-divider-vertical.p-divider-center{align-items:center}.p-divider-vertical.p-divider-bottom{align-items:flex-end}.p-divider-solid.p-divider-horizontal:before{border-top-style:solid}.p-divider-solid.p-divider-vertical:before{border-left-style:solid}.p-divider-dashed.p-divider-horizontal:before{border-top-style:dashed}.p-divider-dashed.p-divider-vertical:before{border-left-style:dashed}.p-divider-dotted.p-divider-horizontal:before{border-top-style:dotted}.p-divider-dotted.p-divider-vertical:before{border-left-style:dotted}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Divider, [{\n    type: Component,\n    args: [{\n      selector: 'p-divider',\n      template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" role=\"separator\" [attr.aria-orientation]=\"layout\" [attr.data-pc-name]=\"'divider'\">\n            <div class=\"p-divider-content\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-divider-horizontal{display:flex;width:100%;position:relative;align-items:center}.p-divider-horizontal:before{position:absolute;display:block;top:50%;left:0;width:100%;content:\\\"\\\"}.p-divider-horizontal.p-divider-left{justify-content:flex-start}.p-divider-horizontal.p-divider-right{justify-content:flex-end}.p-divider-horizontal.p-divider-center{justify-content:center}.p-divider-content{z-index:1}.p-divider-vertical{min-height:100%;margin:0 1rem;display:flex;position:relative;justify-content:center}.p-divider-vertical:before{position:absolute;display:block;top:0;left:50%;height:100%;content:\\\"\\\"}.p-divider-vertical.p-divider-top{align-items:flex-start}.p-divider-vertical.p-divider-center{align-items:center}.p-divider-vertical.p-divider-bottom{align-items:flex-end}.p-divider-solid.p-divider-horizontal:before{border-top-style:solid}.p-divider-solid.p-divider-vertical:before{border-left-style:solid}.p-divider-dashed.p-divider-horizontal:before{border-top-style:dashed}.p-divider-dashed.p-divider-vertical:before{border-left-style:dashed}.p-divider-dotted.p-divider-horizontal:before{border-top-style:dotted}.p-divider-dotted.p-divider-vertical:before{border-left-style:dotted}}\\n\"]\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    align: [{\n      type: Input\n    }]\n  });\n})();\nclass DividerModule {\n  static ɵfac = function DividerModule_Factory(t) {\n    return new (t || DividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DividerModule,\n    declarations: [Divider],\n    imports: [CommonModule],\n    exports: [Divider]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Divider],\n      declarations: [Divider]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Divider, DividerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,MACL,yBAAyB;AAAA,MACzB,wBAAwB,KAAK,WAAW;AAAA,MACxC,sBAAsB,KAAK,WAAW;AAAA,MACtC,mBAAmB,KAAK,SAAS;AAAA,MACjC,oBAAoB,KAAK,SAAS;AAAA,MAClC,oBAAoB,KAAK,SAAS;AAAA,MAClC,kBAAkB,KAAK,WAAW,iBAAiB,CAAC,KAAK,SAAS,KAAK,UAAU;AAAA,MACjF,oBAAoB,KAAK,WAAW,gBAAgB,KAAK,UAAU,YAAY,KAAK,WAAW,eAAe,CAAC,KAAK,SAAS,KAAK,UAAU;AAAA,MAC5I,mBAAmB,KAAK,WAAW,gBAAgB,KAAK,UAAU;AAAA,MAClE,iBAAiB,KAAK,WAAW,cAAc,KAAK,UAAU;AAAA,MAC9D,oBAAoB,KAAK,WAAW,cAAc,KAAK,UAAU;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,GAAG;AACxC,WAAO,KAAK,KAAK,UAAS;AAAA,EAC5B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,IACzB,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,aAAa,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,mBAAmB,CAAC;AAAA,IACjF,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,eAAe,CAAC,EAAE,WAAW,IAAI,KAAK;AACnE,QAAG,YAAY,oBAAoB,IAAI,MAAM,EAAE,gBAAgB,SAAS;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,OAAO;AAAA,IACrC,QAAQ,CAAC,orCAAwrC;AAAA,IACjsC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,orCAAwrC;AAAA,IACnsC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAe;AAAA,EAClC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,OAAO;AAAA,IACtB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,OAAO;AAAA,EACnB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,OAAO;AAAA,MACjB,cAAc,CAAC,OAAO;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}