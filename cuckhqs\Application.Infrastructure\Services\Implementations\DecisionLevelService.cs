﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class DecisionLevelService : IDecisionLevelService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public DecisionLevelService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<DecisionLevelResponse>> SearchDecisionLevelAsync(SearchDecisionLevelRequest request)
        {
            try
            {
                IQueryable<DecisionLevelResponse> query = _unitOfWork.DecisionLevel.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.DecisionLevelCode.Contains(request.keyword) ||
                                x.DecisionLevelName.Contains(request.keyword) 
                                ))
                    .Select(s => new DecisionLevelResponse()
                    {
                        Id = s.Id,
                        DecisionLevelCode = s.DecisionLevelCode,
                        DecisionLevelName = s.DecisionLevelName,
                        Class = s.Class,
                        Year = s.Year,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<DecisionLevelResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<DecisionLevelResponse> CreateDecisionLevelAsync(CreateDecisionLevelRequest request)
        {
            try
            {
                var entity = CreateDecisionLevelRequest.Create(request);

                await _unitOfWork.DecisionLevel.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return DecisionLevelResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteDecisionLevelAsync(DeleteDecisionLevelRequest request)
        {
            try
            {
                var record = await _unitOfWork.DecisionLevel.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.DecisionLevel.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateDecisionLevelAsync(UpdateDecisionLevelRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.DecisionLevel.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateDecisionLevelRequest.Create(request);

                await _unitOfWork.DecisionLevel.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
