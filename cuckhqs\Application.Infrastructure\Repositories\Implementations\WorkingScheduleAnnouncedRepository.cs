﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class WorkingScheduleAnnouncedRepository : GenericRepository<WorkingScheduleIssueEntity, int>, IWorkingScheduleAnnouncedRepository
    {
        public AppDbContext Context { get; set; }

        public WorkingScheduleAnnouncedRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(WorkingScheduleIssueEntity requestObject, WorkingScheduleIssueEntity targetObject)
        {
        //    targetObject.Classify = requestObject.Classify;
        //    targetObject.OrganizationUnitId = requestObject.OrganizationUnitId;
        //    targetObject.Register = requestObject.Register;
        //    targetObject.Year = requestObject.Year;
        //    targetObject.Week = requestObject.Week;
        //    targetObject.Date = requestObject.Date;
        //    targetObject.CoChair = requestObject.CoChair;
        //    targetObject.Time = requestObject.Time;
        //    targetObject.TimeFrom = requestObject.TimeFrom;
        //    targetObject.TimeTo = requestObject.TimeTo;
        //    targetObject.Place = requestObject.Place;
        //    targetObject.Contents = requestObject.Contents;
        //    targetObject.Member = requestObject.Member;
        //    targetObject.Note = requestObject.Note;
        //    targetObject.Message = requestObject.Message;
        //    targetObject.Active = requestObject.Active;
        //    targetObject.OrganizationUnitId_Chair = requestObject.OrganizationUnitId_Chair;
        //    //targetObject.OrganizationUnitIdName = requestObject.OrganizationUnitIdName;
        //    targetObject.WorkingScheduleC = requestObject.WorkingScheduleC;
        //    //targetObject.WorkingScheduleCName = requestObject.WorkingScheduleCName;
        //    targetObject.WorkingScheduleEP = requestObject.WorkingScheduleEP;
        //    //targetObject.WorkingScheduleEPName = requestObject.WorkingScheduleEPName;
        //    targetObject.WorkingScheduleEPH = requestObject.WorkingScheduleEPH;
        //    //targetObject.WorkingScheduleEPHName = requestObject.WorkingScheduleEPHName;
        //    targetObject.WorkingScheduleOU = requestObject.WorkingScheduleOU;
        //    //targetObject.WorkingScheduleOUName = requestObject.WorkingScheduleOUName;
        //    targetObject.Announced = requestObject.Announced;
        //    //targetObject.WorkingScheduleResultId = requestObject.WorkingScheduleResultId;
        //    //targetObject.OrganizationUnitId_ChairName = requestObject.OrganizationUnitId_ChairName;
        //    targetObject.IPAddress = requestObject.IPAddress;
        //    //targetObject.MTEntityState = requestObject.MTEntityState;
        //    targetObject.CreatedDate = requestObject.CreatedDate;
        //    targetObject.ModifiedDate = requestObject.ModifiedDate;
        //    targetObject.ModifiedBy = requestObject.ModifiedBy;
        //    targetObject.CreatedBy = requestObject.CreatedBy;
        //    targetObject.SortOrder = requestObject.SortOrder;
        }

    }
}
