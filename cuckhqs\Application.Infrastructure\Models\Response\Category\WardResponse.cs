﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class WardResponse
    {
        public int? Id { get; set; }
        public int? ProvinceId { get; set; }
        public string? Province_Name { get; set; }
        public int? DistrictId { get; set; }
        public string? District_Name { get; set; }
        public string? WardCode { get; set; }
        public string? WardName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<WardEntity, WardResponse>> Expression
        {
            get
            {
                return entity => new WardResponse()
                {
                    Id = entity.Id,
                    ProvinceId = entity.ProvinceId,
                    DistrictId = entity.DistrictId,
                    WardCode= entity.WardCode,
                    WardName = entity.WardName,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static WardResponse Create(WardEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
