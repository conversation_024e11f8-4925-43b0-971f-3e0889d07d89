﻿
namespace ql_tb_vk_vt.Infrastructure.Constants
{
    public static class MessageErrorConstant
    {
        public const string NOT_FOUND = "Lỗi không tồn tại dữ liệu!";
        public const string UNKNOWN_ERROR = "Có lỗi sảy ra!";
        public const string AUTHORIZED = "Lỗi xác thực!";

        public const string DATA_INVALID = "Sai dữ liệu!";

        public const string QUANTITY_NOT_NULL = "Vui lòng nhập số lượng lớn hơn 0!";
        public const string CANNOT_UPDATE_APPROVED = "Không thể sửa phiếu đã duyệt.";

        #region "WareHouseProductItem"
        public const string WAREHOUSE_ID_NULL = "Vui lòng chọn kho hàng!";
        public const string WAREHOUSE_NOT_FOUND = "Kho hàng không tồn tại trong hệ thống!";
        public const string WAREHOUSE_PRODUCT_ID_NULL = "Vui lòng chọn kho tbvk!";
        public const string WAREHOUSE_PRODUCT_DETAIL_WAREHOUSE_PRODUCT_ID_NULL = "Vui lòng chọn kho tbvk!";
        public const string WAREHOUSE_PRODUCT_DETAIL_WAREHOUSE_PRODUCT_ID_NOT_FOUND = "Kho tbvk không tồn tại!";
        public const string WAREHOUSE_PRODUCT_DETAIL_WAREHOUSE_PRODUCT_QUANTITY_INVALID = "Không thể duyệt nhập kho số lượng nhỏ hơn hoặc bằng 0!";
        public const string WAREHOUSE_PRODUCT_DETAIL_WAREHOUSE_PRODUCT_APPROVED = "Không thể cập nhật với trạng thái đã duyệt!";
        #endregion

        #region "SuggestedRepair"
        public const string SUGGESTED_REPAIR_NOT_FOUND = "Phiếu đề nghị sữa chữa không tồn tại!";
        public const string SUGGESTED_REPAIR_EXPECT_NOT_FOUND = "Phương án dự trù không tồn tại!";
        public const string SUGGESTED_REPAIR_NOT_APPROVED = "Phiếu đề nghị sữa chữa chưa được duyệt!";
        public const string SUGGESTED_REPAIR_ERROR_TYPE = "Không thể tạo phương án dụ trừ với loại phiếu sữa chữa lớn, nhỏ!";
        public const string PRODUCT_NOT_FOUND = "tbvk không tồn tại!";
        public const string SUGGESTED_REPAIR_EXITED_PRODUCT = "tbvk đã đang chờ sửa!";
        public const string SUGGESTED_REPAIR_STATUS_WAITTING_APPROVAL = "Không thể duyệt - từ chối với phiếu đề nghị sữa chữa khác chờ duyệt!";
        #endregion

        #region "Purpose"
        public const string PURPOSE_NOT_FOUND = "Mục đích sử dụng không tồn tại!";
        #endregion

        #region "Department"
        public const string DEPARTMENT_ASSIGN_NOT_FOUND = "Đơn vị chỉ định không tồn tại!";
        public const string DEPARTMENT_MANAGEMENT_NOT_FOUND = "Đơn vị quản lí không tồn tại!";
        public const string DEPARTMENT_SENT_NOT_FOUND = "Đơn vị giao không tồn tại!";
        public const string DEPARTMENT_APPROVAL_NOT_FOUND = "Đơn vị duyệt không tồn tại!";
        public const string DEPARTMENT_RECEIVER_NOT_FOUND = "Đơn vị nhận không tồn tại!";
        #endregion

        #region "AllocationPlanProduct"
        public const string ALLOCATION_PLAN_NOT_FOUND = "Kế hoạch cấp phát không tồn tại!";
        public const string SUGGESTED_ALLOCATION_STATUS_WAITTING_APPROVAL = "Không thể duyệt - từ chối với phiếu đề nghị khác chờ duyệt!";
        public const string NOT_ALLOW_DELETE_BY_STATUS = "Không thể xóa - không thể xóa phiếu đã xác nhận, đã duyệt!";
        public const string QUANTITY_NOT_ENOUGH = "Tồn kho không đủ";


        #endregion

        #region "ProviceProduct"
        public const string PROVICEPRODUCT_NOT_FOUND = "Phiếu biên chế không tồn tại!";
        public const string PROVICEPRODUCT_STATUS_WAITTING_APPROVAL = "Không thể duyệt - từ chối với phiếu biên chế khác chờ duyệt!";
        #endregion
        public const string COACHING_PRODUCT_NOT_FOUND = "Huấn luyện không tồn tại!";

        #region "DemandDefensiveProduct"
        public const string DEMANDEFENSIVE_PLAN_NOT_FOUND = "Kế hoạch cấp phát không tồn tại!";
        #endregion


        #region "PlanRemoveProviderProduct"
        public const string REMOVE_PLAN_PROVIDER_NOT_FOUND = "Kế hoạch loại biên chế không tồn tại!";
        #endregion

        #region Lập lý lịch
        public const string ProductBackgroundHasExisted = "Lý lịch trang bị đã tồn tại!";
        #endregion
        #region Sổ theo dõi
        public const string ProductTrackingHasExisted = "Sổ theo dõi trang bị đã tồn tại!";
        #endregion
    }
}