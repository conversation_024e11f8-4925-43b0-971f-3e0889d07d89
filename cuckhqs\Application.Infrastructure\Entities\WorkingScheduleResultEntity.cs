﻿using Application.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleResultEntity : BaseEntity<int>
    {
        public int? WorkingScheduleId { get; set; }
        public DateTime? Date { get; set; }
        public string? Result { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public virtual ICollection<WorkingScheduleResult_AttachDetailEntity> WorkingScheduleResult_AttachDetail { get; set; }
        [ForeignKey("WorkingScheduleId")]
        public virtual WorkingScheduleEntity WorkingSchedule { get; set; }

    }
}
