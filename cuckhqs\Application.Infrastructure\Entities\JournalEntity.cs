﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class JournalEntity : BaseEntity<int>
    {
        public string? JournalCode { get; set; }
        public string? JournalName { get; set; }
        public string? ISSN { get; set; }
        public int? JournalTypeId { get; set; }
        public string? JournalTypeCode { get; set; }
        public string? JournalTypeId_AN { get; set; }
        public int? JournalGroupId { get; set; }
        public string? JournalGroupCode { get; set; }
        public string? JournalGroupId_AN { get; set; }
        public string? JournalSpecialCode { get; set; }
        public string? PublishingAgency { get; set; }
        public Decimal? PointFrom { get; set; }
        public Decimal? PointTo { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
    }
}
