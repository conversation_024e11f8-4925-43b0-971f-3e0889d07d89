﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities.Employee
{
    public class EmployeeCEntity : BaseEntity<Guid>
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? FullName { get; set; }
        public string? ShortName { get; set; }
        public int? OrganizationUnitId { get; set; }
        public int? Classify { get; set; }
       // public string? OrganizationUnitName { get; set; }
        public string? EmployeeCode { get; set; }
        public int? RankId { get; set; }
        public int? Gender { get; set; }
        public int? AcademicRankId { get; set; }
        public int? YearOfAcademicRank { get; set; }
        public int? DegreeId { get; set; }
        public int? YearOfDegree { get; set; }
        public int? PositionId { get; set; }
     //   public string? PositionName { get; set; }
        public short? PositionType { get; set; }
        public int? PartyPositionId { get; set; }
        public DateTime? BirthDay { get; set; }
        public bool? Owned { get; set; }
        public bool? Active { get; set; }
        public bool? ActiveAccount { get; set; }
        public bool? IsAdministrator { get; set; }
       // public string? RankName { get; set; }
        #region thêm sau cho dễ nhớ
        public string? LongFullName { get; set; }
        public string? BirthPlace { get; set; }
        public string? HomeLand {  get; set; }
        public string? NativeAddress { get; set; }
        public string? Tel {  get; set; }
        public string? HomeTel { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? OfficeAddress { get; set; }
        public string? HomeAddress { get; set; }
        public string? Website { get; set; }
        public string? Description { get; set; }
        public string? IDNumber { get; set; }
        public string? IssuedBy { get; set; }
        public DateTime? DateBy { get; set; }
        public string? AccountNumber { get; set; }
        public string? Bank { get; set; }
        public string? Avatar { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        #endregion
        public virtual ICollection<OnDutyCommandEntity>? OnDutyCommands { get; set; }
    }
}
