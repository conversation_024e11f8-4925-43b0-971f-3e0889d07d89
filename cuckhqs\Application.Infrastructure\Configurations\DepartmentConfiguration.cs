﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class DepartmentConfiguration : IEntityTypeConfiguration<DepartmentEntity>
    {
        public void Configure(EntityTypeBuilder<DepartmentEntity> builder)
        {
            builder.ToTable("dm_don_vi_btm");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.Name).HasColumnName("ten_don_vi");
            builder.Property(x => x.NameShort).HasColumnName("viet_tat");
            builder.Property(x => x.Code).HasColumnName("ma_don_vi");
            builder.Property(x => x.SortOrder).HasColumnName("sap_xep");
            builder.Property(x => x.Address).HasColumnName("dia_chi");
            builder.Property(x => x.PhoneNumber).HasColumnName("dien_thoai");
            builder.Property(x => x.IsActive).HasColumnName("hieu_luc");
            builder.Property(x => x.ParentId).HasColumnName("id_don_vi_cha");
            builder.Property(x => x.ParentIdCode).HasColumnName("ma_don_vi_cha");
            builder.Property(x => x.DepartmentType).HasColumnName("loai_don_vi");
            builder.Property(x => x.Description).HasColumnName("mo_ta");
            builder.Property(x => x.Status).HasColumnName("trang_thai");
            builder.Property(x => x.CreatedBy).HasColumnName("id_nguoi_tao");
            builder.Property(x => x.CreatedDate).HasColumnName("ngay_tao");
            builder.Property(x => x.UpdatedBy).HasColumnName("id_nguoi_cap_nhap");
            builder.Property(x => x.UpdatedDate).HasColumnName("lan_cap_nhap_cuoi");
        }
    }
}
