﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class PositionEntity : BaseEntity<int>
    {
        public int? ParentId { get; set; }
        public bool? IsRoot { get; set; }
        public string? PositionCode { get; set; }
        public string? ParentCode { get; set; }
        public short? Classify { get; set; }
        public string? PositionName { get; set; }
        public string? ShortPositionName { get; set; }
        public string? FullPositionName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
    }
}
