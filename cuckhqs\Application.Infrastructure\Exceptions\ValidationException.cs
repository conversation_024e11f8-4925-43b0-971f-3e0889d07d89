﻿
using Application.Infrastructure.Constants;

namespace Application.Infrastructure.Exceptions
{
    [Serializable]
    public class ValidationException : BaseException
    {
        public ValidationException(string message, System.Exception? innerException = null) : base(message, ErrorCodeConstant.VALIDATION, innerException) { }
        public ValidationException(string message, IDictionary<string, object> payloads, System.Exception? innerException = null) 
            : base(message, ErrorCodeConstant.VALIDATION, payloads, innerException) { }
    }
}
