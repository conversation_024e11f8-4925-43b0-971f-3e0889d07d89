﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models.Request.Category.Degree;

namespace Application.Infrastructure.Services.Implementations
{
    public class DegreeService : IDegreeService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public DegreeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<DegreeResponse>> SearchDegreeAsync(SearchDegreeRequest request)
        {
            try
            {
                IQueryable<DegreeResponse> query = _unitOfWork.Degree.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.DegreeCode.Contains(request.keyword) ||
                                x.DegreeName.Contains(request.keyword) ||
                                x.DegreeShortName.Contains(request.keyword)
                                ))
                    .Select(s => new DegreeResponse()
                    {
                        Id = s.Id,
                        DegreeCode = s.DegreeCode,
                        DegreeName = s.DegreeName,
                        DegreeShortName = s.DegreeShortName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                return await BaseSearchResponse<DegreeResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<DegreeResponse> CreateDegreeAsync(CreateDegreeRequest request)
        {
            try
            {
                var entity = CreateDegreeRequest.Create(request);

                await _unitOfWork.Degree.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return DegreeResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteDegreeAsync(DeleteDegreeRequest request)
        {
            try
            {
                var record = await _unitOfWork.Degree.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Degree.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateDegreeAsync(UpdateDegreeRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Degree.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateDegreeRequest.Create(request);

                await _unitOfWork.Degree.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
