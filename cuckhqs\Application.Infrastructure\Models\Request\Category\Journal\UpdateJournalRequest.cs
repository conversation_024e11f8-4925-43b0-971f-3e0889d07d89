﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.JournalGroup;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Journal
{
    public class UpdateJournalRequest : CreateJournalRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateJournalRequest, JournalEntity>> Expression
        {
            get
            {
                return entity => new JournalEntity
                {
                    Id = entity.Id,
                    JournalCode = entity.JournalCode,
                    JournalName = entity.JournalName,
                    ISSN = entity.ISSN,
                    JournalTypeId = entity.JournalTypeId,
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeId_AN = entity.JournalTypeId_AN,
                    JournalGroupId = entity.JournalGroupId,
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupId_AN = entity.JournalGroupId_AN,
                    PublishingAgency = entity.PublishingAgency,
                    PointFrom = entity.PointFrom,
                    PointTo = entity.PointTo,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalEntity Create(UpdateJournalRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
