﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Azure.Core;

namespace Application.Infrastructure.Models.Response
{
    public class OnDutyCommandResponse
    {
        public int Id { get; set; }
        public int? Year { set; get; }
        public int? Week { set; get; }
        public DateTime? Date { set; get; }
        public Guid? EmployeeId_H { set; get; }
        public string? Description { set; get; }
        public bool? Active { set; get; }
        public int? SortOrder { set; get; }
        public DateTime? CreatedDate { set; get; }
        public DateTime? ModifiedDate { set; get; }
        public string? IPAddress { set; get; }
        public string? ModifiedBy { set; get; }
        public string? CreatedBy { set; get; }
        public virtual EmployeeCResponse? Employee { get; set; }

        public static Expression<Func<OnDutyCommandEntity, OnDutyCommandResponse>> Expression
        {
            get
            {
                return entity => new OnDutyCommandResponse()
                {
                    Id = entity.Id,
                    Year = entity.Year,
                    Week = entity.Week,
                    Date = entity.Date,
                    EmployeeId_H = entity.EmployeeId_H,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                    Employee = EmployeeCResponse.Create(entity.Employee)
                };
            }
        }

        public static OnDutyCommandResponse Create(OnDutyCommandEntity response)
        {
            if (response == null) return null;

            return Expression.Compile().Invoke(response);
        }
    }
}
