﻿using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.DisciplineType;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class DisciplineTypeController : ControllerBase
    {
        private readonly IDisciplineTypeService _disciplineTypeService;

        public DisciplineTypeController(IDisciplineTypeService disciplineTypeService)
        {
            _disciplineTypeService = disciplineTypeService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchDisciplineTypeAsync([FromBody] SearchDisciplineTypeRequest request)
        {
            try
            {
                var response = await _disciplineTypeService.SearchDisciplineTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateDisciplineTypeAsync([FromBody] CreateDisciplineTypeRequest request)
        {
            var response = await _disciplineTypeService.CreateDisciplineTypeAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateDisciplineTypeAsync([FromBody] UpdateDisciplineTypeRequest request)
        {
            try
            {
                var response = await _disciplineTypeService.UpdateDisciplineTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteDisciplineTypeAsync([FromBody] DeleteDisciplineTypeRequest request)
        {
            try
            {
                var response = await _disciplineTypeService.DeleteDisciplineTypeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
