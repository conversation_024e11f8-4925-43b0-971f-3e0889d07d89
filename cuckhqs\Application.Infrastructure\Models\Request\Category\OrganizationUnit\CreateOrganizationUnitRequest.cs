﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.OrganizationUnit
{
    public class CreateOrganizationUnitRequest
    {
        public string? OrganizationUnitCode { get; set; }
        public string? OrganizationUnitName { get; set; }
        public string? ShortOrganizationUnitName { get; set; }
        public int? ParentId { get; set; }
        public int? ClassifyGroup { get; set; }
        public string? TrainingMaterialCode { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }

        public static Expression<Func<CreateOrganizationUnitRequest, OrganizationUnitEntity>> Expression
        {
            get
            {
                return entity => new OrganizationUnitEntity
                {
                    OrganizationUnitCode = entity.OrganizationUnitCode,
                    Name = entity.OrganizationUnitName,
                    ShortOrganizationUnitName = entity.ShortOrganizationUnitName,
                    ParentId = entity.ParentId,
                    ClassifyGroup = entity.ClassifyGroup,
                    TrainingMaterialCode = entity.TrainingMaterialCode,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static OrganizationUnitEntity Create(CreateOrganizationUnitRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
