﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class DegreeConfiguration : IEntityTypeConfiguration<DegreeEntity>
    {
        public void Configure(EntityTypeBuilder<DegreeEntity> builder)
        {
            builder.ToTable("Degree");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.DegreeCode).HasColumnName("DegreeCode");
            builder.Property(x => x.DegreeName).HasColumnName("DegreeName");
            builder.Property(x => x.DegreeShortName).HasColumnName("DegreeShortName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}