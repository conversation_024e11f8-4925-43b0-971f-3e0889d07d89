﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Schedule;
using Application.Infrastructure.Models.Response;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IScheduleService
    {
        Task<BaseSearchResponse<ScheduleResponse>> SearchScheduleAsync(SearchScheduleRequest request);
        Task<ScheduleResponse> CreateScheduleAsync(CreateScheduleRequest request);
        Task<bool> DeleteScheduleAsync(DeleteScheduleRequest request);
        Task<bool> UpdateScheduleAsync(UpdateScheduleRequest request);
    }
}
