﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.Advertisement
{
    public class UpdateAdvertisementRequest : CreateAdvertisementRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateAdvertisementRequest, AdvertisementEntity>> Expression
        {
            get
            {
                return entity => new AdvertisementEntity
                {
                    Id = entity.Id,
                    AdvertisementCode = entity.AdvertisementCode,
                    AdvertisementName = entity.AdvertisementName,
                    Start = entity.Start,
                    StartHour = entity.StartHour,
                    StartMinute = entity.StartMinute,
                    Ends = entity.Ends,
                    EndsHour = entity.EndsHour,
                    EndsMinute = entity.EndsMinute,
                    Year = entity.Year,
                    Active = entity.Active
                };
            }
        }

        public static AdvertisementEntity Create(UpdateAdvertisementRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
