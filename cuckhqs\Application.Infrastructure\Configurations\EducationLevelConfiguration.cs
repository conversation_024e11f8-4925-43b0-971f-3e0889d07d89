﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class EducationLevelConfiguration : IEntityTypeConfiguration<EducationLevelEntity>
    {
        public void Configure(EntityTypeBuilder<EducationLevelEntity> builder)
        {
            builder.ToTable("EducationLevel");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.EducationLevelCode).HasColumnName("EducationLevelCode");
            builder.Property(x => x.EducationLevelName).HasColumnName("EducationLevelName");
            builder.Property(x => x.Class).HasColumnName("Class");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
