﻿using Application.Infrastructure.Models.Request.Category.Country;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class CountryController : ControllerBase
    {
        private readonly ICountryService _countryService;

        public CountryController(ICountryService countryRankervice)
        {
            _countryService = countryRankervice;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchCountryAsync([FromBody] SearchCountryRequest request)
        {
            try
            {
                var response = await _countryService.SearchCountryAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateCountryAsync([FromBody] CreateCountryRequest request)
        {
            var response = await _countryService.CreateCountryAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateCountryAsync([FromBody] UpdateCountryRequest request)
        {
            try
            {
                var response = await _countryService.UpdateCountryAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteCountryAsync([FromBody] DeleteCountryRequest request)
        {
            try
            {
                var response = await _countryService.DeleteCountryAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
