﻿using Application.API.RealTimeHub;
using Application.Infrastructure.Email;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OpenIddict.Validation.AspNetCore;

namespace Application.API.Controllers
{
    [AllowAnonymous]
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly IEmailSender _emailSender;
        private readonly IHubContext<DataHub> _hubContext;
        public TestController(IEmailSender emailSender, IHubContext<DataHub> hubContext)
        {
            _emailSender = emailSender;
            _hubContext = hubContext;
        }

        [HttpPost("SendTestMail")]
        public async Task<IActionResult> TestSendMail([FromBody] string text)
        {
            try
            {
                await _emailSender.SendEmailAsync("<EMAIL>", "Test Mail", text);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("TestNotification")]
        public async Task<IActionResult> TestNotification([FromBody] string text, CancellationToken cancellationToken)
        {
            try
            {
                await _hubContext.Clients.All.SendAsync("dataUpdated", text, cancellationToken);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
