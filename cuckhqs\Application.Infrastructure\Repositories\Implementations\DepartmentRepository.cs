﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Models;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Implementations;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class DepartmentRepository : GenericRepository<DepartmentEntity, int>, IDepartmentRepository
    {
        public AppDbContext Context { get; set; }

        public DepartmentRepository(AppDbContext Context) : base(Context)
        { }

        protected override void Update(DepartmentEntity requestObject, DepartmentEntity targetObject)
        {
            targetObject.Name = requestObject.Name;
            targetObject.NameShort = requestObject.NameShort;
            targetObject.Code = requestObject.Code;
            targetObject.SortOrder = requestObject.SortOrder;
            targetObject.Address = requestObject.Address;
            targetObject.IsActive = requestObject.IsActive;
            targetObject.ParentId = requestObject.ParentId;
            targetObject.ParentIdCode = requestObject.ParentIdCode;
            targetObject.PhoneNumber = requestObject.PhoneNumber;
            targetObject.DepartmentType = requestObject.DepartmentType;
            targetObject.Description = requestObject.Description;
        }
    }
}
