﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations.Category
{
    public class EmployeeRepository : GenericRepository<EmployeeEntity, Guid>, IViewEmployeeRepository
    {
        public AppDbContext Context { get; set; }

        public EmployeeRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(EmployeeEntity requestObject, EmployeeEntity targetObject)
        {
            targetObject.OrganizationUnitId = requestObject.OrganizationUnitId;
            //targetObject.DateFrom = requestObject.DateFrom;
            //targetObject.DateTo = requestObject.DateTo;
            //targetObject.Class = requestObject.Class;
            //targetObject.Contents = requestObject.Contents;
            //targetObject.Status = requestObject.Status;
            //targetObject.Active = requestObject.Active;
            //targetObject.Year = requestObject.Year;
            //targetObject.Week = requestObject.Week;
            //targetObject.Active = requestObject.Active;
            //targetObject.SortOrder = requestObject.SortOrder;
        }
    }
}
