﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class RewardTypeRepository : GenericRepository<RewardTypeEntity, int>, IRewardTypeRepository
    {
        public AppDbContext Context { get; set; }

        public RewardTypeRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(RewardTypeEntity requestObject, RewardTypeEntity targetObject)
        {
            targetObject.RewardTypeCode = requestObject.RewardTypeCode;
            targetObject.RewardTypeName = requestObject.RewardTypeName;
            targetObject.Active = requestObject.Active;
            targetObject.Class = requestObject.Class;
        }
    }
}
