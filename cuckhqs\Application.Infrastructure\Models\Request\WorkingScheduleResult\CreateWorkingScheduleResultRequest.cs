﻿using Application.Infrastructure.Models.Response;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.WorkingScheduleResult
{
    public class CreateWorkingScheduleResultRequest
    {
        public int? WorkingScheduleId { get; set; }
        public List<FileDataRequest>? Files { get; set; }
        public string? OldListFile { get; set; }
        public string? Date {  get; set; }
        public string? Result { get; set; }
    }

    public class FileDataRequest
    {
        public string? Contents { get; set; }
        public string? FileName { get; set; }
        public string? FileType { get; set; }
        public float? FileSize { get; set; }
    }
}
