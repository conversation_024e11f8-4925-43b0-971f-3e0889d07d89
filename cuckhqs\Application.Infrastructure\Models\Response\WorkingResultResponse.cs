﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Response
{
    public class WorkingResultResponse
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public int? OrganizationUnitId { set; get; }
        public int? Year { set; get; }
        public int? Week {  set; get; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public int? Class { get; set; }
        public string?  Contents { get; set; }  
        public string?  Contents1 { get; set; }  
        public string?  Contents2 { get; set; }  
        public string?  Contents3 { get; set; }
        public string?  Note { get; set; }
        public int? Status { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { set; get; }
        public string? CreatedBy { set; get; }

        public static Expression<Func<WorkingResultEntity, WorkingResultResponse>> Expression
        {
            get
            {
                return entity => new WorkingResultResponse()
                {
                    Id = entity.Id,
                    OrganizationUnitId = entity.OrganizationUnitId,
                    Year = entity.Year,
                    Week = entity.Week,
                    DateFrom = entity.DateFrom,
                    DateTo = entity.DateTo,
                    Class = entity.Class,
                    Contents = entity.Contents,
                    Status = entity.Status,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                };
            }
        }

        public static WorkingResultResponse Create(WorkingResultEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
