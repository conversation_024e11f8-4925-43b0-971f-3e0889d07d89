﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class DegreeRepository : GenericRepository<DegreeEntity, int>, IDegreeRepository
    {
        public AppDbContext Context { get; set; }

        public DegreeRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(DegreeEntity requestObject, DegreeEntity targetObject)
        {
            targetObject.DegreeCode = requestObject.DegreeCode;
            targetObject.DegreeName = requestObject.DegreeName;
            targetObject.DegreeShortName = requestObject.DegreeShortName;
            targetObject.Active = requestObject.Active;
            targetObject.Description = requestObject.Description;
        }
    }
}
