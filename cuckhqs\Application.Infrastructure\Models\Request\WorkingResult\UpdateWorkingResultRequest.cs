﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.Advertisement
{
    public class UpdateWorkingResultRequest : CreateWorkingResultRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateWorkingResultRequest, WorkingResultEntity>> Expression
        {
            get
            {
                return entity => new WorkingResultEntity
                {
                    Id = entity.Id,
                    Name = Convert.ToString(entity.Name),
                    OrganizationUnitId = Convert.ToInt32(entity.OrganizationUnitId),
                    Year = Convert.ToInt32(entity.Year),
                    Week = Convert.ToInt32(entity.Week),
                    Class = Convert.ToInt32(entity.Class),
                    Contents = Convert.ToString(entity.Contents),
                    DateFrom = Convert.ToDateTime(entity.DateFrom),
                    DateTo = Convert.ToDateTime(entity.DateTo),
                    Status = Convert.ToInt32(entity.Status),
                    Contents1 = Convert.ToString(entity.Contents1),
                    Contents2 = Convert.ToString(entity.Contents2),
                    Contents3 = Convert.ToString(entity.Contents3),
                    Note = Convert.ToString(entity.Note),
                };
            }
        }

        public static WorkingResultEntity Create(UpdateWorkingResultRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
