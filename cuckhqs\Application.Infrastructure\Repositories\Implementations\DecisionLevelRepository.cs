﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class DecisionLevelRepository : GenericRepository<DecisionLevelEntity, int>, IDecisionLevelRepository
    {
        public AppDbContext Context { get; set; }

        public DecisionLevelRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(DecisionLevelEntity requestObject, DecisionLevelEntity targetObject)
        {
            targetObject.DecisionLevelCode = requestObject.DecisionLevelCode;
            targetObject.DecisionLevelName = requestObject.DecisionLevelName;
            targetObject.Active = requestObject.Active;
            targetObject.Class = requestObject.Class;
        }
    }
}
