﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Rank;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Position
{
    public class UpdatePositionRequest : CreatePositionRequest
    {
        public int Id {  get; set; }

        public static Expression<Func<UpdatePositionRequest, PositionEntity>> Expression
        {
            get
            {
                return entity => new PositionEntity
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    PositionCode = entity.PositionCode,
                    PositionName = entity.PositionName,
                    IsRoot = entity.IsRoot,
                    Classify = entity.Classify,
                    ShortPositionName = entity.ShortPositionName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static PositionEntity Create(UpdatePositionRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
