﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Journal;
using Application.Infrastructure.Models.Request.Category.JournalGroup;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class JournalService : IJournalService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public JournalService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<JournalResponse>> SearchJournalAsync(SearchJournalRequest request)
        {
            try
            {
                var JournalGroup = await _unitOfWork.Journal.GetAllJournalGroupAsync();
                var JournalGroup_Name = JournalGroup?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.JournalGroupName)
                    ?? new Dictionary<string, string>();

                var JournalType = await _unitOfWork.Journal.GetAllJournalTypeAsync();
                var JournalType_Name = JournalType?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.JournalTypeName)
                    ?? new Dictionary<string, string>();

                IQueryable<JournalResponse> query = _unitOfWork.Journal.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.JournalName.Contains(request.keyword) ||
                                x.JournalCode.Contains(request.keyword)))
                    .Select(s => new JournalResponse()
                    {
                        Id = s.Id,
                        JournalCode = s.JournalCode,
                        JournalName = s.JournalName,
                        ISSN = s.ISSN,
                        JournalTypeId = s.JournalTypeId,
                        JournalTypeCode = s.JournalTypeCode,
                        JournalTypeName = null,
                        JournalTypeId_AN = s.JournalTypeId_AN,
                        JournalGroupId = s.JournalGroupId,
                        JournalGroupCode = s.JournalGroupCode,
                        JournalGroupName = null,
                        JournalGroupId_AN = s.JournalGroupId_AN,
                        JournalSpecialCode = s.JournalSpecialCode,
                        PublishingAgency = s.PublishingAgency,
                        PointFrom = s.PointFrom,
                        PointTo = s.PointTo,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                var resultQuery = query.AsEnumerable()
                 .Select(emp =>
                 {
                     emp.JournalGroupName = JournalGroup_Name.TryGetValue(emp.JournalGroupId?.ToString().Trim() ?? "", out var name) ? name : "";
                     emp.JournalTypeName = JournalType_Name.TryGetValue(emp.JournalTypeId?.ToString().Trim() ?? "", out var dname) ? dname : "";
                     return emp;
                 })
                 .AsQueryable();

                return await BaseSearchResponse<JournalResponse>.GetResponse(resultQuery, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<JournalResponse> CreateJournalAsync(CreateJournalRequest request)
        {
            try
            {
                var entity = CreateJournalRequest.Create(request);

                await _unitOfWork.Journal.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return JournalResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteJournalAsync(DeleteJournalRequest request)
        {
            try
            {
                var record = await _unitOfWork.Journal.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Journal.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateJournalAsync(UpdateJournalRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Journal.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateJournalRequest.Create(request);

                await _unitOfWork.Journal.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
