﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.AcademicRank
{
    public class UpdateAcademicRankRequest : CreateAcademicRankRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateAcademicRankRequest, AcademicRankEntity>> Expression
        {
            get
            {
                return entity => new AcademicRankEntity
                {
                    Id = entity.Id,
                    AcademicRankCode = entity.AcademicRankCode,
                    AcademicRankName = entity.AcademicRankName,
                    AcademicRankShortName = entity.AcademicRankShortName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static AcademicRankEntity Create(UpdateAcademicRankRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
