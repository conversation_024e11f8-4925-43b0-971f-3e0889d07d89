﻿using BoldReports.Web.ReportViewer;
using BoldReports.Web;
using BoldReports.Writer;
using BoldReports.RDL.DOM;
using BoldReports.RDL.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Models.Request.BaoCao;
using Application.Infrastructure.Models.Response;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using OpenIddict.Validation.AspNetCore;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordBody = DocumentFormat.OpenXml.Wordprocessing.Body;
using WordParagraph = DocumentFormat.OpenXml.Wordprocessing.Paragraph;
using WordRun = DocumentFormat.OpenXml.Wordprocessing.Run;
using WordText = DocumentFormat.OpenXml.Wordprocessing.Text;
using SkiaSharp;

namespace ql_tb_vk_vt.API.Controllers.BaoCao
{
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]/[action]")]
    [EnableCors("AllowAngularApp")]
    public class BcMauBaoCaoTuanController : ControllerBase, IReportController
    {
        private readonly IMemoryCache _cache;
        private readonly IWebHostEnvironment _hostingEnvironment;
        private readonly IReportProductService _reportProductService;
        private readonly ILogger<BcMauBaoCaoTuanController> _logger;
        private const string REPORT_FILE_NAME = "Mau_bao_cao_tuan.rdlc";
        private const string FONT_FILE_NAME = "times.ttf";
        private const string CACHE_KEY = "MauBaoCaoTuanRequest";

        public BcMauBaoCaoTuanController(
            IMemoryCache cache,
            IWebHostEnvironment hostingEnvironment,
            IReportProductService reportProductService,
            ILogger<BcMauBaoCaoTuanController> logger)
        {
            _cache = cache;
            _hostingEnvironment = hostingEnvironment;
            _reportProductService = reportProductService;
            _logger = logger;
        }

        [HttpPost]
        [EnableCors("AllowAngularApp")]
        public object PostReportAction([FromBody] Dictionary<string, object> jsonResult)
        {
            try
            {
                MauBaoCaoTuanRequest request = null;
                if (jsonResult?.ContainsKey("customData") == true)
                {
                    var jsonObject = JObject.Parse(jsonResult["customData"].ToString());
                    request = new MauBaoCaoTuanRequest
                    {
                        nam = int.Parse(jsonObject["nam"]?.ToString() ?? "0"),
                        tuan = int.Parse(jsonObject["tuan"]?.ToString() ?? "0"),
                        don_vi = int.Parse(jsonObject["don_vi_ql"]?.ToString() ?? "0")
                    };

                    var validationResult = ValidateRequest(request);
                    if (!validationResult.IsValid)
                    {
                        _logger.LogWarning("Dữ liệu đầu vào không hợp lệ: {@Request}, {@Errors}",
                            request, validationResult.Errors);
                        return new { errors = validationResult.Errors };
                    }

                    // Store the request in cache for use in OnInitReportOptions
                    _cache.Set(CACHE_KEY, request, TimeSpan.FromMinutes(5));
                }

                var result = ReportHelper.ProcessReport(jsonResult, this, _cache);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi xử lý báo cáo");
                return new { error = "Lỗi xử lý báo cáo", message = ex.Message };
            }
        }

        [ActionName("GetResource")]
        [AcceptVerbs("GET")]
        public object GetResource(ReportResource resource)
        {
            try
            {
                var result = ReportHelper.GetResource(resource, this, _cache);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi lấy tài nguyên báo cáo");
                return new { error = "Lỗi lấy tài nguyên", message = ex.Message };
            }
        }

        [HttpPost]
        public object PostFormReportAction()
        {
            try
            {
                var result = ReportHelper.ProcessReport(null, this, _cache);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi xử lý form báo cáo");
                return new { error = "Lỗi xử lý form", message = ex.Message };
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public void OnInitReportOptions(ReportViewerOptions reportOption)
        {
            try
            {
                var basePath = _hostingEnvironment.WebRootPath;
                var reportPath = Path.Combine(basePath, "Resources", REPORT_FILE_NAME);
                var fontPath = Path.Combine(basePath, "App_Data", FONT_FILE_NAME);

                if (!System.IO.File.Exists(reportPath))
                {
                    _logger.LogError("Không tìm thấy file báo cáo tại đường dẫn: {ReportPath}", reportPath);
                    throw new FileNotFoundException("Không tìm thấy file báo cáo");
                }

                if (!System.IO.File.Exists(fontPath))
                {
                    _logger.LogError("Không tìm thấy file font tại đường dẫn: {FontPath}", fontPath);
                    throw new FileNotFoundException("Không tìm thấy file font");
                }

                using var inputStream = new FileStream(reportPath, FileMode.Open, FileAccess.Read);
                var reportStream = new MemoryStream();
                inputStream.CopyTo(reportStream);
                reportStream.Position = 0;

                reportOption.ReportModel.Stream = reportStream;
                reportOption.ReportModel.PDFOptions = new PDFOptions
                {
                    Fonts = new Dictionary<string, Stream>
                    {
                        { "Times New Roman", new FileStream(fontPath, FileMode.Open, FileAccess.Read) }
                    }
                };

                // Retrieve the request from cache instead of Request.Query
                if (!_cache.TryGetValue(CACHE_KEY, out MauBaoCaoTuanRequest request))
                {
                    // Fallback to Request.Query if cache is unavailable
                    _logger.LogWarning("Request data not found in cache, falling back to query parameters.");
                    request = new MauBaoCaoTuanRequest
                    {
                        nam = GetQueryParamAsInt(Request.Query["nam"], "nam"),
                        tuan = GetQueryParamAsInt(Request.Query["tuan"], "tuan"),
                        don_vi = GetQueryParamAsInt(Request.Query["don_vi_ql"], "don_vi_ql")
                    };
                }

                var validationResult = ValidateRequest(request);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Dữ liệu đầu vào không hợp lệ: {@Request}, {@Errors}",
                        request, validationResult.Errors);
                    reportOption.ReportModel.DataSources.Add(new ReportDataSource
                    {
                        Name = "DataSet1",
                        Value = new List<BCMauBaoCaoTuanResponse>()
                    });
                    return;
                }

                var response = _reportProductService.Bc2MauBaoCaoTuan(request).GetAwaiter().GetResult();
                reportOption.ReportModel.DataSources.Add(new ReportDataSource
                {
                    Name = "DataSet1",
                    Value = response
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khởi tạo báo cáo");
                reportOption.ReportModel.DataSources.Add(new ReportDataSource
                {
                    Name = "DataSet1",
                    Value = new List<BCMauBaoCaoTuanResponse>()
                });
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [NonAction]
        public void OnReportLoaded(ReportViewerOptions reportOption)
        {
            // Xử lý các tham số báo cáo nếu cần
        }

        private ValidationResult ValidateRequest(MauBaoCaoTuanRequest request)
        {
            var errors = new List<string>();

            if (request.nam <= 0)
            {
                errors.Add("Năm phải lớn hơn 0");
            }
            else if (request.nam < 2000 || request.nam > 2100)
            {
                errors.Add("Năm phải nằm trong khoảng 2000-2100");
            }

            if (request.tuan <= 0)
            {
                errors.Add("Tuần phải lớn hơn 0");
            }
            else if (request.tuan > 53)
            {
                errors.Add("Tuần không được lớn hơn 53");
            }

            //if (request.don_vi <= 0)
            //{
            //    errors.Add("Đơn vị phải lớn hơn 0");
            //}

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }

        // Helper method to safely parse query parameters
        private int GetQueryParamAsInt(StringValues queryValue, string paramName)
        {
            if (string.IsNullOrWhiteSpace(queryValue))
            {
                _logger.LogWarning("Query parameter '{ParamName}' is missing or empty.", paramName);
                return 0; // Default value
            }

            if (int.TryParse(queryValue, out int result))
            {
                return result;
            }

            _logger.LogWarning("Query parameter '{ParamName}' with value '{Value}' is not a valid integer.", paramName, queryValue);
            return 0; // Default value
        }

        ////////////  test
        [HttpPost]
        [EnableCors("AllowAngularApp")]
        public async Task<IActionResult> ExportToWord([FromBody] Dictionary<string, object> jsonResult)
        {
            try
            {
                MauBaoCaoTuanRequest request = null;
                if (jsonResult?.ContainsKey("customData") == true)
                {
                    var jsonObject = JObject.Parse(jsonResult["customData"].ToString());
                    request = new MauBaoCaoTuanRequest
                    {
                        nam = int.Parse(jsonObject["nam"]?.ToString() ?? "0"),
                        tuan = int.Parse(jsonObject["tuan"]?.ToString() ?? "0"),
                        don_vi = int.Parse(jsonObject["don_vi_ql"]?.ToString() ?? "0")
                    };

                    var validationResult = ValidateRequest(request);
                    if (!validationResult.IsValid)
                    {
                        _logger.LogWarning("Dữ liệu đầu vào không hợp lệ: {@Request}, {@Errors}", request, validationResult.Errors);
                        return BadRequest(new { errors = validationResult.Errors });
                    }

                    _cache.Set(CACHE_KEY, request, TimeSpan.FromMinutes(5));
                }

                // Initialize ReportWriter for Word export
                var writer = new ReportWriter
                {
                    ReportProcessingMode = BoldReports.Writer.ProcessingMode.Local // Explicitly use BoldReports.Writer.ProcessingMode
                };

                // Set the RDLC report file path
                var basePath = _hostingEnvironment.WebRootPath;
                var reportPath = Path.Combine(basePath, "Resources", REPORT_FILE_NAME);
                if (!System.IO.File.Exists(reportPath))
                {
                    _logger.LogError("Không tìm thấy file báo cáo tại đường dẫn: {ReportPath}", reportPath);
                    return NotFound(new { error = "Không tìm thấy file báo cáo" });
                }

                // Load the RDLC report
                using var inputStream = new FileStream(reportPath, FileMode.Open, FileAccess.Read);
                var reportStream = new MemoryStream();
                inputStream.CopyTo(reportStream);
                reportStream.Position = 0;
                writer.LoadReport(reportStream);

                // Set font options (similar to OnInitReportOptions)
                var fontPath = Path.Combine(basePath, "App_Data", FONT_FILE_NAME);
                if (!System.IO.File.Exists(fontPath))
                {
                    _logger.LogError("Không tìm thấy file font tại đường dẫn: {FontPath}", fontPath);
                    return NotFound(new { error = "Không tìm thấy file font" });
                }

                writer.PDFOptions = new PDFOptions
                {
                    Fonts = new Dictionary<string, Stream>
                    {
                        { "Times New Roman", new FileStream(fontPath, FileMode.Open, FileAccess.Read) }
                    }
                };

                // Fetch report data
                var response = await _reportProductService.Bc2MauBaoCaoTuan(request);
                writer.DataSources.Add(new ReportDataSource
                {
                    Name = "DataSet1",
                    Value = response
                });

                // Export to Word
                using var memoryStream = new MemoryStream();
                writer.Save(memoryStream, WriterFormat.Word);
                memoryStream.Position = 0;

                var fileName = $"BaoCaoTuan_{request.nam}_{request.tuan}.docx";
                return File(memoryStream.ToArray(), "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi xuất báo cáo Word");
                return StatusCode(500, new { error = "Lỗi xuất báo cáo Word", message = ex.Message });
            }
        }
    }

    public class ValidationResult
    {   
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
}