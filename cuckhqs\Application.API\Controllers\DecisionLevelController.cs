﻿using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class DecisionLevelController : ControllerBase
    {
        private readonly IDecisionLevelService _decisionLevelService;

        public DecisionLevelController(IDecisionLevelService DecisionLevelService)
        {
            _decisionLevelService = DecisionLevelService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchDecisionLevelAsync([FromBody] SearchDecisionLevelRequest request)
        {
            try
            {
                var response = await _decisionLevelService.SearchDecisionLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateDecisionLevelAsync([FromBody] CreateDecisionLevelRequest request)
        {
            var response = await _decisionLevelService.CreateDecisionLevelAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateDecisionLevelAsync([FromBody] UpdateDecisionLevelRequest request)
        {
            try
            {
                var response = await _decisionLevelService.UpdateDecisionLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteDecisionLevelAsync([FromBody] DeleteDecisionLevelRequest request)
        {
            try
            {
                var response = await _decisionLevelService.DeleteDecisionLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
