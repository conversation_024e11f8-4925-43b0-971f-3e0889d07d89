﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response
{
    public class WorkingScheduleAnnouncedResponse
    {
        public int Id { get; set; }
        public int? OrganizationUnitId { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }
        public int? Number { get; set; }
        public string? Sign {  get; set; }
        public DateTime? Date {  get; set; }
        public int? Announced {  get; set; }
        public string? Place {  get; set; }
        public string? Command { get; set; }
        public string? PersonSigningOther { get; set; }
        public Guid? PersonSigning{ get; set; }
        public string? PersonSigning_Name { get; set; }
        public string? UnitPositonSigning { get; set; }
        public List<string>? Receive { get; set; }
        public string? Receive_Name {  get; set; }
    }

}
