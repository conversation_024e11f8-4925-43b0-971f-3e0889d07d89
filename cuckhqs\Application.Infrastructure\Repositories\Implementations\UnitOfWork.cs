﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using System.Dynamic;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Services.Implementations;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.Category;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class UnitOfWork : IUnitOfWork
    {
        //public AppDbContext Context { set; private get; }
        public IDepartmentRepository Department { get; private set; }
        public IWorkingScheduleRepository WorkingSchedule { get; private set; }
        public IAdvertisementRepository Advertisement { get; private set; }
        public IOnDutyCommandRepository OnDutyCommand { get; private set; }
        public IWorkingResultRepository WorkingResult { get; private set; }
        public IWorkingResultSyntheticRepository WorkingResultSynthetic { get; private set; }
        public IEmployeeRepository Employees { get; private set; }
        public IOrganizationUnitRepository Organizations { get; private set; }
        public IWorkingScheduleAnnouncedRepository WorkingScheduleAnnounced { get; private set; }
        public IViewEmployeeRepository ViewEmployee { get; private set; }
        public IRankRepository Rank { get; private set; }
        public IPositionRepository Position { get; private set; }
        public IAcademicRankRepository AcademicRank { get; private set; }
        public IDegreeRepository Degree { get; private set; }
        public ICountryRepository Country { get; private set; }
        public IProvinceRepository Province { get; private set; }
        public IDistrictRepository District { get; private set; }
        public IWardRepository Ward { get; private set; }
        public IDecisionLevelRepository DecisionLevel { get; private set; }
        public IRewardTypeRepository RewardType { get; private set; }
        public IDisciplineTypeRepository DisciplineType { get; private set; }
        public IEducationLevelRepository EducationLevel { get; private set; }
        public IJournalTypeRepository JournalType { get; private set; }
        public IJournalGroupRepository JournalGroup { get; private set; }
        public IJournalRepository Journal { get; private set; }
        public ISoSRepository SoS{ get; private set; }
        public IWorkingScheduleResultRepository WorkingScheduleResult { get; private set; }
        public IScheduleRepository Schedule { get; private set; }


        public UnitOfWork
        (
            AppDbContext context,
            IUserContext userContext,
            IDepartmentRepository department,
            IWorkingScheduleRepository workingSchedule,
            IAdvertisementRepository advertisement,
            IOnDutyCommandRepository onDutyCommand,
            IWorkingResultRepository workingresult,
            IWorkingResultSyntheticRepository workingResultSynthetic,
            IEmployeeRepository employee,
            IOrganizationUnitRepository organizations,
            IWorkingScheduleAnnouncedRepository workingScheduleAnnouced,
            IViewEmployeeRepository viewEmployee,
            IRankRepository rank,
            IPositionRepository position,
            IAcademicRankRepository academicRank,
            IDegreeRepository degree,
            ICountryRepository country,
            IProvinceRepository province,
            IDistrictRepository district,
            IWardRepository ward,
            IDecisionLevelRepository decisionLevel,
            IRewardTypeRepository rewardType,
            IDisciplineTypeRepository disciplineType,
            IEducationLevelRepository educationLevel,
            IJournalTypeRepository journalType,
            IJournalGroupRepository journalGroup,
            IJournalRepository journal,
            ISoSRepository sos,
            IWorkingScheduleResultRepository workingScheduleResult,
            IScheduleRepository schedule

         )
        {
            Context = context;
            _userContext = userContext;
            Department = department;
            WorkingSchedule = workingSchedule;
            Advertisement = advertisement;
            WorkingResult = workingresult;
            OnDutyCommand = onDutyCommand;
            WorkingResultSynthetic = workingResultSynthetic;
            Employees = employee;
            Organizations = organizations;
            WorkingScheduleAnnounced = workingScheduleAnnouced;
            ViewEmployee = viewEmployee;
            Rank = rank;
            Position = position;
            AcademicRank = academicRank;
            Degree = degree;
            Country = country;
            Province = province;
            District = district;
            Ward = ward;
            DecisionLevel = decisionLevel;
            RewardType = rewardType;
            DisciplineType = disciplineType;
            EducationLevel = educationLevel;
            JournalType = journalType;
            JournalGroup = journalGroup;
            Journal = journal;
            SoS = sos;
            WorkingScheduleResult = workingScheduleResult;
            Schedule = schedule;
        }

        #region Ít sửa
        protected AppDbContext Context { get; private set; }
        private IDbContextTransaction _transaction;
        private readonly IUserContext _userContext;

        public void BeginTransaction()
        => _transaction = Context.Database.BeginTransaction();

        public async Task CommitChangesAsync()
        {
            Context.ChangeTracker.DetectChanges();

            foreach (var entry in Context.ChangeTracker.Entries())
            {
                dynamic track = entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    SetCreationProperties(track);

                    SetUpdateProperties(track);
                }

                if (entry.State == EntityState.Deleted || entry.State == EntityState.Modified)
                {
                    SetUpdateProperties(track);
                }
            }

            Context.SaveChanges();
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
            }
        }

        public void RollbackTransaction()
        {
            _transaction.Rollback();
            _transaction.Dispose();
        }

        private static object HasProperty(dynamic item, string propertyName)
        {
            if (item is ExpandoObject eo)
            {
                return (eo as IDictionary<string, object>).ContainsKey(propertyName);
            }
            else
            {
                return item.GetType().GetProperty(propertyName) ?? null;
            }
        }

        private void SetCreationProperties(dynamic track)
        {
            if (HasProperty(track, "CreatedBy") != null) track.CreatedBy = _userContext.Id.ToString();
            if (HasProperty(track, "CreatedDate") != null) track.CreatedDate = DateTime.Now;
        }

        private void SetUpdateProperties(dynamic track)
        {
            if (HasProperty(track, "UpdatedBy") != null) track.UpdatedBy = _userContext.Id.ToString();
            if (HasProperty(track, "UpdatedDate") != null) track.UpdatedDate = DateTime.Now;
            if (HasProperty(track, "ModifiedBy") != null) track.ModifiedBy = _userContext.Id.ToString();
            if (HasProperty(track, "ModifiedDate") != null) track.ModifiedDate = DateTime.Now;
        }


        public async Task<int> SaveChangesAsync()
        {
            Context.ChangeTracker.DetectChanges();

            foreach (var entry in Context.ChangeTracker.Entries())
            {
                dynamic track = entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    SetCreationProperties(track);

                    SetUpdateProperties(track);
                }

                if (entry.State == EntityState.Deleted || entry.State == EntityState.Modified)
                {
                    SetUpdateProperties(track);
                }
            }

            return await Context.SaveChangesAsync();
        }
        #endregion
    }
}