﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions
{
    public interface IProvinceRepository : IRepository<ProvinceEntity, int>
    {
        Task<List<DistrictEntity>> GetAllDistrictAsync();
        Task<List<WardEntity>> GetAllWardAsync();
    }
}
