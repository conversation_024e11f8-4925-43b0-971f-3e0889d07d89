﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class AcademicRankConfiguration : IEntityTypeConfiguration<AcademicRankEntity>
    {
        public void Configure(EntityTypeBuilder<AcademicRankEntity> builder)
        {
            builder.ToTable("AcademicRank");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.AcademicRankCode).HasColumnName("AcademicRankCode");
            builder.Property(x => x.AcademicRankName).HasColumnName("AcademicRankName");
            builder.Property(x => x.AcademicRankShortName).HasColumnName("AcademicRankShortName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}