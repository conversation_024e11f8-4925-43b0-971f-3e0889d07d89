﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Application.Infrastructure.Exceptions;
using ql_tb_vk_vt.Infrastructure.Constants;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Models.Request.Category.Employee;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Services.Abstractions.Category;

namespace Application.Infrastructure.Services.Implementations
{
    public class EmployeeService : IViewEmployeeService
    {
        private readonly IUnitOfWork _unitOfWork;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public EmployeeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }


        public async Task<BaseSearchResponse<EmployeeResponse>> SearchEmployeeAsync(SearchEmployeeRequest request)
        {
            try
            {
                IQueryable<EmployeeResponse> query = _unitOfWork.ViewEmployee
                                                               .AsQueryable()
                                                               .AsNoTracking()
                                                               .Where(x => (string.IsNullOrEmpty(request.keyword)
                                                                       || x.RankName.Contains(request.keyword)
                                                                       || x.FullName.Contains(request.keyword)
                                                                       || x.OrganizationUnitName.Contains(request.keyword)
                                                               ))
                                                               .Select(s => new EmployeeResponse()
                                                               {
                                                                   Id = s.Id,
                                                                   Classify = s.Classify,
                                                                   OrganizationUnitId = s.OrganizationUnitId,
                                                                   OrganizationUnitName = s.OrganizationUnitName,
                                                                   EmployeeCode = s.EmployeeCode,
                                                                   Fullname = s.FullName,
                                                                   RankId = s.RankId,
                                                                   Gender = s.Gender,
                                                                   Gender_Name = s.Gender == 1 ? "Nam" : "Nữ",
                                                                   AcademicRankId = s.AcademicRankId,
                                                                   YearOfAcademicRank = s.YearOfAcademicRank,
                                                                   DegreeId = s.DegreeId,
                                                                   YearOfDegree = s.YearOfDegree,
                                                                   PositionId = s.PositionId,
                                                                   PositionName = s.PositionName,
                                                                   PositionType = s.PositionType,
                                                                   PartyPositionId = s.PartyPositionId,
                                                                   BirthDay = s.BirthDay,
                                                                   Owned = s.Owned,
                                                                   Active = s.Active,
                                                                   ActiveAccount = s.ActiveAccount,
                                                               //    IsAdministrator = s.IsAdministrator,
                                                                   RankName = s.RankName,
                                                               }); 

                return await BaseSearchResponse<EmployeeResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }
    }
}
