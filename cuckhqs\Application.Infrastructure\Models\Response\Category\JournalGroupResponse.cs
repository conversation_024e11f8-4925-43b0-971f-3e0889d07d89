﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class JournalGroupResponse
    {
        public int? Id { get; set; }
        public string? JournalGroupCode { get; set; }
        public string? JournalGroupName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<JournalGroupEntity, JournalGroupResponse>> Expression
        {
            get
            {
                return entity => new JournalGroupResponse()
                {
                    Id = entity.Id,
                    JournalGroupCode = entity.JournalGroupCode,
                    JournalGroupName = entity.JournalGroupName,
                    Description = entity.Description,
                    Active = true,
                };
            }
        }

        public static JournalGroupResponse Create(JournalGroupEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
