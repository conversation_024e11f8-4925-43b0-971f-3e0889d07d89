﻿using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Position;
using Application.Infrastructure.Models.Request.Category.SoS;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class SoSController : ControllerBase
    {
        private readonly ISoSService _sosService;

        public SoSController(ISoSService sosService)
        {
            _sosService = sosService;
        }

        [HttpGet("get-all")]
        public async Task<IActionResult> GetAllSoSAsync()
        {
            try
            {
                var response = await _sosService.GetAllSoSAsync();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("get-tree")]
        public async Task<IActionResult> GetSoSBuildTreeAsync()
        {
            try
            {
                var response = await _sosService.GetAllSoSBuildTreeAsync();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Search")]
        public async Task<IActionResult> SearchSoSAsync([FromBody] SearchSoSRequest request)
        {
            try
            {
                var response = await _sosService.SearchSoSAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetPositionById")]
        public async Task<IActionResult> GetSoSById([FromQuery] int Id)
        {
            try
            {
                var response = await _sosService.GetSoSById(Id);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateSoSAsync([FromBody] CreateSoSRequest request)
        {
            var response = await _sosService.CreateSoSAsync(request);
            return Ok(response);
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateSoSAsync([FromBody] UpdateSoSRequest request)
        {
            try
            {
                var response = await _sosService.UpdateSoSAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]

        public async Task<IActionResult> DeleteSoSAsync([FromBody] DeleteSoSRequest request)
        {
            try
            {
                var response = await _sosService.DeleteSoSAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
