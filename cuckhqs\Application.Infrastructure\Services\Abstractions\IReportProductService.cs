﻿using Application.Infrastructure.Models.Request.BaoCao;
using Application.Infrastructure.Models.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IReportProductService
    {
        Task<List<BCMauBaoCaoTuanResponse>> Bc2MauBaoCaoTuan(MauBaoCaoTuanRequest request);
    }
}
