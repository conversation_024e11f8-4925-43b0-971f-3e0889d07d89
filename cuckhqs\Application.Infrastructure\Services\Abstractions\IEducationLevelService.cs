﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.EducationLevel;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IEducationLevelService
    {
        Task<BaseSearchResponse<EducationLevelResponse>> SearchEducationLevelAsync(SearchEducationLevelRequest request);
        Task<EducationLevelResponse> CreateEducationLevelAsync(CreateEducationLevelRequest request);
        Task<bool> UpdateEducationLevelAsync(UpdateEducationLevelRequest request);
        Task<bool> DeleteEducationLevelAsync(DeleteEducationLevelRequest request);
    }
}
