﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.DisciplineType
{
    public class CreateDisciplineTypeRequest
    {
        public string? DisciplineTypeCode { get; set; }
        public string? DisciplineTypeName { get; set; }
        public int? Class { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateDisciplineTypeRequest, DisciplineTypeEntity>> Expression
        {
            get
            {
                return entity => new DisciplineTypeEntity
                {
                    DisciplineTypeCode = entity.DisciplineTypeCode,
                    DisciplineTypeName = entity.DisciplineTypeName,
                    Class = entity.Class,
                    Active = entity.Active,
                };
            }
        }

        public static DisciplineTypeEntity Create(CreateDisciplineTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
