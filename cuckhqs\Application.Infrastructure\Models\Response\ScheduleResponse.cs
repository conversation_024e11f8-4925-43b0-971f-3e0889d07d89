﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Response
{
    public class ScheduleResponse
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public DateTime? Date { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public string? Rank { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }

        public static Expression<Func<ScheduleEntity, ScheduleResponse>> Expression
        {
            get
            {
                return entity => new ScheduleResponse()
                {
                    Id = entity.Id,
                    Name = entity.Name,
                    Date = entity.Date,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                    Rank = entity.Rank,
                    Year = entity.Year,
                    Week = entity.Week
                };
            }
        }

        public static ScheduleResponse Create(ScheduleEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
