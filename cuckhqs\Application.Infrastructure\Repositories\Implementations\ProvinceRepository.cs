﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class ProvinceRepository : GenericRepository<ProvinceEntity, int>, IProvinceRepository
    {
        public AppDbContext Context { get; set; }

        public ProvinceRepository(AppDbContext context) : base(context)
        {
            Context = context;
        }

        public async Task<List<DistrictEntity>> GetAllDistrictAsync()
        {
            return await Context.District.ToListAsync();
        }

        public async Task<List<WardEntity>> GetAllWardAsync()
        {
            return await Context.Ward.ToListAsync();
        }

        protected override void Update(ProvinceEntity requestObject, ProvinceEntity targetObject)
        {
            targetObject.ProvinceCode = requestObject.ProvinceCode;
            targetObject.ProvinceName = requestObject.ProvinceName;
            targetObject.Description = requestObject.Description;
            targetObject.Active = requestObject.Active;
        }
    }
}
