﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class WorkingScheduleResult_AttachDetailConfiguration : IEntityTypeConfiguration<WorkingScheduleResult_AttachDetailEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleResult_AttachDetailEntity> builder)
        {
            builder.ToTable("WorkingScheduleResult_AttachDetail");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.WorkingScheduleResultId).HasColumnName("WorkingScheduleResultId");
            builder.Property(x => x.Contents).HasColumnName("Contents");
            builder.Property(x => x.FileName).HasColumnName("FileName");
            builder.Property(x => x.FileType).HasColumnName("FileType");
            builder.Property(x => x.FileSize).HasColumnName("FileSize");
            builder.Property(x => x.Inactive).HasColumnName("Inactive");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");

            builder.HasOne(x => x.WorkingScheduleResult)
               .WithMany(ws => ws.WorkingScheduleResult_AttachDetail)
               .HasForeignKey(x => x.WorkingScheduleResultId)
               .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
