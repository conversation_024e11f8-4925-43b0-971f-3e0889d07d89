﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.SoS
{
    public class CreateSoSRequest
    {
        public int? ParentId { get; set; }
        public string? SoSCode { get; set; }
        public string? SoSName { get; set; }
        public int? Year{ get; set; }
        public bool? Active { get; set; }

        public static Expression<Func<CreateSoSRequest, SoSEntity>> Expression
        {
            get
            {
                return entity => new SoSEntity
                {
                    ParentId = entity.ParentId,
                    SoSCode = entity.SoSCode,
                    SoSName = entity.SoSName,
                    Year = entity.Year,
                    Active = entity.Active,
                    IsRoot = false

                };
            }
        }

        public static SoSEntity Create(CreateSoSRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
