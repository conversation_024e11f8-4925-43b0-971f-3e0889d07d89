﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Advertisement;

namespace Application.Infrastructure.Models.Request.OnDutyCommand
{
    public class UpdateOnDutyCommandRequest : CreateOnDutyCommandRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateOnDutyCommandRequest, OnDutyCommandEntity>> Expression
        {
            get
            {
                return entity => new OnDutyCommandEntity
                {
                    Id = entity.Id,
                    Year = entity.Year,
                    Week = entity.Week,
                    Date = entity.Date,
                    EmployeeId_H = entity.EmployeeId_H,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                };
            }
        }

        public static OnDutyCommandEntity Create(UpdateOnDutyCommandRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
