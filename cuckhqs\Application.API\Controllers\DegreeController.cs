﻿using Application.Infrastructure.Models.Request.Category.AcademicRank;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class DegreeController : ControllerBase
    {
        private readonly IDegreeService _degreeService;

        public DegreeController(IDegreeService degreeRankervice)
        {
            _degreeService = degreeRankervice;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchDegreeAsync([FromBody] SearchDegreeRequest request)
        {
            try
            {
                var response = await _degreeService.SearchDegreeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateDegreeAsync([FromBody] CreateDegreeRequest request)
        {
            var response = await _degreeService.CreateDegreeAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateDegreeAsync([FromBody] UpdateDegreeRequest request)
        {
            try
            {
                var response = await _degreeService.UpdateDegreeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteDegreeAsync([FromBody] DeleteDegreeRequest request)
        {
            try
            {
                var response = await _degreeService.DeleteDegreeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
