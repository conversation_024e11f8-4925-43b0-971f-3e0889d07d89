﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities.Employee;

namespace Application.Infrastructure.Entities
{
    public class OnDutyCommandEntity : BaseEntity<int>
    {
        public int? Year { set; get; }
        public int? Week { set; get; }
        public DateTime? Date { set; get; }
        public Guid? EmployeeId_H { set; get; }
        public string? Description { set; get; }
        public bool? Active { set; get; }
        public int? SortOrder { set; get; }
        public DateTime? CreatedDate { set; get; }
        public DateTime? ModifiedDate { set; get; }
        public string? IPAddress { set; get; }
        public string? ModifiedBy { set; get; }
        public string? CreatedBy { set; get; }
        public virtual EmployeeCEntity? Employee { get; set; }
    }
}
