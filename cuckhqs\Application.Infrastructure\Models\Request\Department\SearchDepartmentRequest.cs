﻿using Application.Infrastructure.Commons;
using System.Runtime.CompilerServices;

namespace Application.Infrastructure.Models.Request
{
    public class SearchDepartmentRequest : BaseCriteria
    {
        public string? keyword { get; set; }
        public string? IsActive { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedDateTu { get; set; }
        public DateTime? CreatedDateDen { get; set; }
        public string? Status { get; set; }
    }
}