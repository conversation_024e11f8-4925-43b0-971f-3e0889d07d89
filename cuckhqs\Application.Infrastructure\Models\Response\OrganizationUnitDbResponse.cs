namespace Application.Infrastructure.Models.Response;

public class OrganizationUnitDbResponse
{
    public int Id { get; set; }
    public int? ParentId { get; set; }
    public bool IsRoot { get; set; }
    public string? OrganizationUnitCode { get; set; }
    public string? OrganizationUnitName { get; set; }
    public string? ShortOrganizationUnitName { get; set; }
    public string? FullOrganizationUnitName { get; set; }
    public string? ParentCode { get; set; }
    public int? Classify { get; set; }
    public int? ClassifyGroup { get; set; }
}


public class Employee_Receiced_Response
{
    public Guid Id { get; set; }
    public string? Fullname { get; set; }
    public int? OrganizationUnitId { get; set; }
    public string? OrganizationUnitName{ get; set; }
}

public class Organization_Unit_Tree_Response
{
    public int? OrganizationUnitId { get; set; }
    public string? OrganizationUnitName { get; set; }
    public List<Employee_Receiced_Response> Employees { get; set; }
}


