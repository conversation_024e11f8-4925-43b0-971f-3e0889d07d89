﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Z.EntityFramework.Extensions;

namespace Application.Infrastructure.Models.Request.Category.JournalType
{
    public class CreateJournalTypeRequest
    {
        public string? JournalTypeCode { get; set; }
        public string? JournalTypeName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateJournalTypeRequest, JournalTypeEntity>> Expression
        {
            get
            {
                return entity => new JournalTypeEntity
                {
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeName = entity.JournalTypeName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalTypeEntity Create(CreateJournalTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
