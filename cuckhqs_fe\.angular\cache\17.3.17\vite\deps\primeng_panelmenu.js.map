{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-panelmenu.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, numberAttribute, booleanAttribute, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, computed, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nconst _c0 = [\"list\"];\nconst _c1 = a0 => ({\n  \"p-submenu-list\": true,\n  \"p-panelmenu-root-list\": a0\n});\nconst _c2 = a0 => ({\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction PanelMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 7);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 20)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemActive(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isItemActive(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_Template, 1, 0, null, 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.panelMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.panelMenu.submenuIconTemplate);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.icon)(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"label\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getItemProp(processedItem_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_2_Template, 1, 2, \"span\", 16)(3, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_3_Template, 2, 1, \"span\", 17)(4, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_6_Template, 2, 2, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(5);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, ctx_r3.getItemProp(processedItem_r3, \"disabled\")))(\"target\", ctx_r3.getItemProp(processedItem_r3, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r3.getItemProp(processedItem_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"data-pc-section\", \"action\")(\"tabindex\", !!ctx_r3.parentExpanded ? \"0\" : \"-1\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemGroup(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (processedItem_r3.item == null ? null : processedItem_r3.item.escape) !== false)(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 20)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemActive(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isItemActive(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_Template, 1, 0, null, 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.panelMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.panelMenu.submenuIconTemplate);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.icon)(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"label\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getItemProp(processedItem_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getItemProp(processedItem_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_2_Template, 1, 2, \"span\", 16)(3, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_3_Template, 2, 1, \"span\", 17)(4, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_6_Template, 2, 2, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r6 = i0.ɵɵreference(5);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r3.getItemProp(processedItem_r3, \"routerLink\"))(\"queryParams\", ctx_r3.getItemProp(processedItem_r3, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r3.getItemProp(processedItem_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r3.getItemProp(processedItem_r3, \"disabled\")))(\"target\", ctx_r3.getItemProp(processedItem_r3, \"target\"))(\"fragment\", ctx_r3.getItemProp(processedItem_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r3.getItemProp(processedItem_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r3.getItemProp(processedItem_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r3.getItemProp(processedItem_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r3.getItemProp(processedItem_r3, \"replaceUrl\"))(\"state\", ctx_r3.getItemProp(processedItem_r3, \"state\"));\n    i0.ɵɵattribute(\"title\", ctx_r3.getItemProp(processedItem_r3, \"title\"))(\"data-pc-section\", \"action\")(\"tabindex\", !!ctx_r3.parentExpanded ? \"0\" : \"-1\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemGroup(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getItemProp(processedItem_r3, \"escape\") !== false)(\"ngIfElse\", htmlRouteLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_Template, 7, 12, \"a\", 13)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_Template, 7, 23, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getItemProp(processedItem_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getItemProp(processedItem_r3, \"routerLink\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_1_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_3_1_Template, 1, 0, null, 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, processedItem_r3.item));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-panelMenuSub\", 28);\n    i0.ɵɵlistener(\"itemToggle\", function PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template_p_panelMenuSub_itemToggle_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onItemToggle($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r3.getItemId(processedItem_r3) + \"_list\")(\"panelId\", ctx_r3.panelId)(\"items\", processedItem_r3 == null ? null : processedItem_r3.items)(\"itemTemplate\", ctx_r3.itemTemplate)(\"transitionOptions\", ctx_r3.transitionOptions)(\"focusedItemId\", ctx_r3.focusedItemId)(\"activeItemPath\", ctx_r3.activeItemPath)(\"level\", ctx_r3.level + 1)(\"parentExpanded\", !!ctx_r3.parentExpanded && ctx_r3.isItemExpanded(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function PanelMenuSub_ng_template_2_li_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onItemClick($event, processedItem_r3));\n    });\n    i0.ɵɵtemplate(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_Template, 3, 2, \"ng-container\", 10)(3, PanelMenuSub_ng_template_2_li_1_ng_container_3_Template, 2, 4, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11);\n    i0.ɵɵlistener(\"@submenu.done\", function PanelMenuSub_ng_template_2_li_1_Template_div_animation_submenu_done_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onToggleDone());\n    });\n    i0.ɵɵtemplate(5, PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template, 1, 9, \"p-panelMenuSub\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const processedItem_r3 = ctx_r7.$implicit;\n    const index_r9 = ctx_r7.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getItemProp(processedItem_r3, \"styleClass\"));\n    i0.ɵɵclassProp(\"p-hidden\", processedItem_r3.visible === false)(\"p-focus\", ctx_r3.isItemFocused(processedItem_r3) && !ctx_r3.isItemDisabled(processedItem_r3));\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getItemClass(processedItem_r3))(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"style\"))(\"pTooltip\", ctx_r3.getItemProp(processedItem_r3, \"tooltip\"))(\"tooltipOptions\", ctx_r3.getItemProp(processedItem_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r3.getItemId(processedItem_r3))(\"aria-label\", ctx_r3.getItemProp(processedItem_r3, \"label\"))(\"aria-expanded\", ctx_r3.isItemGroup(processedItem_r3) ? ctx_r3.isItemActive(processedItem_r3) : undefined)(\"aria-level\", ctx_r3.level + 1)(\"aria-setsize\", ctx_r3.getAriaSetSize())(\"aria-posinset\", ctx_r3.getAriaPosInset(index_r9))(\"data-p-disabled\", ctx_r3.isItemDisabled(processedItem_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@submenu\", ctx_r3.getAnimation(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemVisible(processedItem_r3) && ctx_r3.isItemGroup(processedItem_r3) && (ctx_r3.isItemExpanded(processedItem_r3) || ctx_r3.animating));\n  }\n}\nfunction PanelMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_0_Template, 1, 0, \"li\", 5)(1, PanelMenuSub_ng_template_2_li_1_Template, 6, 21, \"li\", 6);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !processedItem_r3.separator && ctx_r3.isItemVisible(processedItem_r3));\n  }\n}\nconst _c5 = [\"submenu\"];\nconst _c6 = [\"container\"];\nconst _c7 = (a0, a1) => ({\n  \"p-component p-panelmenu-header\": true,\n  \"p-highlight\": a0,\n  \"p-disabled\": a1\n});\nconst _c8 = a0 => ({\n  \"p-panelmenu-expanded\": a0\n});\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronDownIcon_1_Template, 1, 1, \"ChevronDownIcon\", 19)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemActive(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_Template, 1, 0, null, 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.submenuIconTemplate);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r3.icon)(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"label\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r4.getItemProp(item_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_2_Template, 1, 2, \"span\", 15)(3, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_3_Template, 2, 1, \"span\", 16)(4, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_6_Template, 2, 2, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(5);\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r4.getItemProp(item_r3, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r4.getItemProp(item_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"tabindex\", -1)(\"title\", ctx_r4.getItemProp(item_r3, \"title\"))(\"data-pc-section\", \"headeraction\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"escape\") !== false)(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_Template, 7, 10, \"a\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.getItemProp(item_r3, \"routerLink\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronDownIcon_1_Template, 1, 1, \"ChevronDownIcon\", 19)(2, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemActive(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_Template, 1, 0, null, 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.submenuIconTemplate);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r3.icon)(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"label\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r4.getItemProp(item_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 25);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_a_5_span_2_Template, 1, 2, \"span\", 15)(3, PanelMenu_ng_container_2_div_1_a_5_span_3_Template, 2, 1, \"span\", 16)(4, PanelMenu_ng_container_2_div_1_a_5_ng_template_4_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, PanelMenu_ng_container_2_div_1_a_5_span_6_Template, 2, 2, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r7 = i0.ɵɵreference(5);\n    const item_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.getItemProp(item_r3, \"routerLink\"))(\"queryParams\", ctx_r4.getItemProp(item_r3, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r4.getItemProp(item_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(18, _c3))(\"target\", ctx_r4.getItemProp(item_r3, \"target\"))(\"fragment\", ctx_r4.getItemProp(item_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r4.getItemProp(item_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r4.getItemProp(item_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r4.getItemProp(item_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r4.getItemProp(item_r3, \"replaceUrl\"))(\"state\", ctx_r4.getItemProp(item_r3, \"state\"));\n    i0.ɵɵattribute(\"tabindex\", -1)(\"data-pc-section\", \"headeraction\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"escape\") !== false)(\"ngIfElse\", htmlRouteLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"@rootItem.done\", function PanelMenu_ng_container_2_div_1_div_6_Template_div_animation_rootItem_done_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onToggleDone());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"p-panelMenuList\", 28);\n    i0.ɵɵlistener(\"headerFocus\", function PanelMenu_ng_container_2_div_1_div_6_Template_p_panelMenuList_headerFocus_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.updateFocusedHeader($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const item_r3 = ctx_r1.$implicit;\n    const i_r4 = ctx_r1.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c8, ctx_r4.isItemActive(item_r3)))(\"@rootItem\", ctx_r4.getAnimation(item_r3));\n    i0.ɵɵattribute(\"id\", ctx_r4.getContentId(item_r3, i_r4))(\"aria-labelledby\", ctx_r4.getHeaderId(item_r3, i_r4))(\"data-pc-section\", \"toggleablecontent\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"menucontent\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"panelId\", ctx_r4.getPanelId(i_r4, item_r3))(\"items\", ctx_r4.getItemProp(item_r3, \"items\"))(\"itemTemplate\", ctx_r4.itemTemplate)(\"transitionOptions\", ctx_r4.transitionOptions)(\"root\", true)(\"activeItem\", ctx_r4.activeItem())(\"tabindex\", ctx_r4.tabindex)(\"parentExpanded\", ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function PanelMenu_ng_container_2_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const item_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onHeaderClick($event, item_r3, i_r4));\n    })(\"keydown\", function PanelMenu_ng_container_2_div_1_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const item_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onHeaderKeyDown($event, item_r3, i_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 8);\n    i0.ɵɵtemplate(3, PanelMenu_ng_container_2_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 9)(4, PanelMenu_ng_container_2_div_1_ng_container_4_Template, 1, 0, \"ng-container\", 10)(5, PanelMenu_ng_container_2_div_1_a_5_Template, 7, 19, \"a\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, PanelMenu_ng_container_2_div_1_div_6_Template, 3, 16, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const item_r3 = ctx_r1.$implicit;\n    const i_r4 = ctx_r1.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"headerClass\"))(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"style\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"panel\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getItemProp(item_r3, \"styleClass\"));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(23, _c7, ctx_r4.isItemActive(item_r3), ctx_r4.isItemDisabled(item_r3)))(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"style\"))(\"pTooltip\", ctx_r4.getItemProp(item_r3, \"tooltip\"))(\"tabindex\", 0)(\"tooltipOptions\", ctx_r4.getItemProp(item_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r4.getHeaderId(item_r3, i_r4))(\"aria-expanded\", ctx_r4.isItemActive(item_r3))(\"aria-label\", ctx_r4.getItemProp(item_r3, \"label\"))(\"aria-controls\", ctx_r4.getContentId(item_r3, i_r4))(\"aria-disabled\", ctx_r4.isItemDisabled(item_r3))(\"data-p-highlight\", ctx_r4.isItemActive(item_r3))(\"data-p-disabled\", ctx_r4.isItemDisabled(item_r3))(\"data-pc-section\", \"header\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(26, _c4, item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_Template, 7, 28, \"div\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemVisible(item_r3));\n  }\n}\nconst _c9 = \"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\";\nclass PanelMenuSub {\n  panelMenu;\n  el;\n  panelId;\n  focusedItemId;\n  items;\n  itemTemplate;\n  level = 0;\n  activeItemPath;\n  root;\n  tabindex;\n  transitionOptions;\n  parentExpanded;\n  itemToggle = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeyDown = new EventEmitter();\n  listViewChild;\n  animating;\n  constructor(panelMenu, el) {\n    this.panelMenu = panelMenu;\n    this.el = el;\n  }\n  getItemId(processedItem) {\n    return processedItem.item?.id ?? `${this.panelId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemClass(processedItem) {\n    return {\n      'p-menuitem': true,\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemProp(processedItem, name, params) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  isItemExpanded(processedItem) {\n    return processedItem.expanded;\n  }\n  isItemActive(processedItem) {\n    return this.isItemExpanded(processedItem) || this.activeItemPath.some(path => path && path.key === processedItem.key);\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  getAnimation(processedItem) {\n    return this.isItemActive(processedItem) ? {\n      value: 'visible',\n      params: {\n        transitionParams: this.transitionOptions,\n        height: '*'\n      }\n    } : {\n      value: 'hidden',\n      params: {\n        transitionParams: this.transitionOptions,\n        height: '0'\n      }\n    };\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemClick(event, processedItem) {\n    if (!this.isItemDisabled(processedItem)) {\n      this.animating = true;\n      this.getItemProp(processedItem, 'command', {\n        originalEvent: event,\n        item: processedItem.item\n      });\n      this.itemToggle.emit({\n        processedItem,\n        expanded: !this.isItemActive(processedItem)\n      });\n    }\n  }\n  onItemToggle(event) {\n    this.itemToggle.emit(event);\n  }\n  onToggleDone() {\n    this.animating = false;\n  }\n  static ɵfac = function PanelMenuSub_Factory(t) {\n    return new (t || PanelMenuSub)(i0.ɵɵdirectiveInject(forwardRef(() => PanelMenu)), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PanelMenuSub,\n    selectors: [[\"p-panelMenuSub\"]],\n    viewQuery: function PanelMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      panelId: \"panelId\",\n      focusedItemId: \"focusedItemId\",\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"level\", \"level\", numberAttribute],\n      activeItemPath: \"activeItemPath\",\n      root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n      transitionOptions: \"transitionOptions\",\n      parentExpanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"parentExpanded\", \"parentExpanded\", booleanAttribute]\n    },\n    outputs: {\n      itemToggle: \"itemToggle\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeyDown: \"menuKeyDown\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 3,\n    vars: 8,\n    consts: [[\"list\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"tree\", 3, \"focusin\", \"focusout\", \"keydown\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menuitem-separator\", \"role\", \"separator\", 4, \"ngIf\"], [\"role\", \"treeitem\", 3, \"ngClass\", \"class\", \"p-hidden\", \"p-focus\", \"ngStyle\", \"pTooltip\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menuitem-separator\"], [\"role\", \"treeitem\", 3, \"ngClass\", \"ngStyle\", \"pTooltip\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\"], [4, \"ngIf\"], [1, \"p-toggleable-content\"], [3, \"id\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"focusedItemId\", \"activeItemPath\", \"level\", \"parentExpanded\", \"itemToggle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"ngClass\", \"target\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"ngClass\", \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"styleClass\", \"ngStyle\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemToggle\", \"id\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"focusedItemId\", \"activeItemPath\", \"level\", \"parentExpanded\"]],\n    template: function PanelMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ul\", 3, 0);\n        i0.ɵɵlistener(\"focusin\", function PanelMenuSub_Template_ul_focusin_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n        })(\"focusout\", function PanelMenuSub_Template_ul_focusout_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n        })(\"keydown\", function PanelMenuSub_Template_ul_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuKeyDown.emit($event));\n        });\n        i0.ɵɵtemplate(2, PanelMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx.root))(\"tabindex\", -1);\n        i0.ɵɵattribute(\"aria-activedescendant\", ctx.focusedItemId)(\"data-pc-section\", \"menu\")(\"aria-hidden\", !ctx.parentExpanded);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Tooltip, AngleDownIcon, AngleRightIcon, PanelMenuSub],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('submenu', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-panelMenuSub',\n      template: `\n        <ul\n            #list\n            [ngClass]=\"{ 'p-submenu-list': true, 'p-panelmenu-root-list': root }\"\n            role=\"tree\"\n            [tabindex]=\"-1\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.data-pc-section]=\"'menu'\"\n            [attr.aria-hidden]=\"!parentExpanded\"\n            (focusin)=\"menuFocus.emit($event)\"\n            (focusout)=\"menuBlur.emit($event)\"\n            (keydown)=\"menuKeyDown.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem let-index=\"index\" [ngForOf]=\"items\">\n                <li *ngIf=\"processedItem.separator\" class=\"p-menuitem-separator\" role=\"separator\"></li>\n                <li\n                    *ngIf=\"!processedItem.separator && isItemVisible(processedItem)\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    role=\"treeitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.aria-label]=\"getItemProp(processedItem, 'label')\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    [class.p-hidden]=\"processedItem.visible === false\"\n                    [class.p-focus]=\"isItemFocused(processedItem) && !isItemDisabled(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [pTooltip]=\"getItemProp(processedItem, 'tooltip')\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                class=\"p-menuitem-link\"\n                                [ngClass]=\"{ 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [attr.tabindex]=\"!!parentExpanded ? '0' : '-1'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!panelMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(processedItem)\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(processedItem)\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"panelMenu.submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"processedItem.icon\" *ngIf=\"processedItem.icon\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"processedItem.item?.escape !== false; else htmlLabel\">{{ getItemProp(processedItem, 'label') }}</span>\n                                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(processedItem, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"processedItem.badge\" [ngClass]=\"processedItem.badgeStyleClass\">{{ processedItem.badge }}</span>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                class=\"p-menuitem-link\"\n                                [ngClass]=\"{ 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [attr.title]=\"getItemProp(processedItem, 'title')\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [attr.tabindex]=\"!!parentExpanded ? '0' : '-1'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!panelMenu.submenuIconTemplate\">\n                                        <AngleDownIcon *ngIf=\"isItemActive(processedItem)\" [styleClass]=\"'p-submenu-icon'\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                        <AngleRightIcon *ngIf=\"!isItemActive(processedItem)\" [styleClass]=\"'p-submenu-icon'\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"panelMenu.submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"processedItem.icon\" *ngIf=\"processedItem.icon\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape') !== false; else htmlRouteLabel\">{{ getItemProp(processedItem, 'label') }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(processedItem, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"processedItem.badge\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <div class=\"p-toggleable-content\" [@submenu]=\"getAnimation(processedItem)\" (@submenu.done)=\"onToggleDone()\">\n                        <p-panelMenuSub\n                            *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem) && (isItemExpanded(processedItem) || animating)\"\n                            [id]=\"getItemId(processedItem) + '_list'\"\n                            [panelId]=\"panelId\"\n                            [items]=\"processedItem?.items\"\n                            [itemTemplate]=\"itemTemplate\"\n                            [transitionOptions]=\"transitionOptions\"\n                            [focusedItemId]=\"focusedItemId\"\n                            [activeItemPath]=\"activeItemPath\"\n                            [level]=\"level + 1\"\n                            [parentExpanded]=\"!!parentExpanded && isItemExpanded(processedItem)\"\n                            (itemToggle)=\"onItemToggle($event)\"\n                        ></p-panelMenuSub>\n                    </div>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      animations: [trigger('submenu', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: PanelMenu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => PanelMenu)]\n    }]\n  }, {\n    type: i0.ElementRef\n  }], {\n    panelId: [{\n      type: Input\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    level: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    parentExpanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    itemToggle: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeyDown: [{\n      type: Output\n    }],\n    listViewChild: [{\n      type: ViewChild,\n      args: ['list']\n    }]\n  });\n})();\nclass PanelMenuList {\n  el;\n  panelId;\n  id;\n  items;\n  itemTemplate;\n  parentExpanded;\n  expanded;\n  transitionOptions;\n  root;\n  tabindex;\n  activeItem;\n  itemToggle = new EventEmitter();\n  headerFocus = new EventEmitter();\n  subMenuViewChild;\n  searchTimeout;\n  searchValue;\n  focused;\n  focusedItem = signal(null);\n  activeItemPath = signal([]);\n  processedItems = signal([]);\n  visibleItems = computed(() => {\n    const processedItems = this.processedItems();\n    return this.flatItems(processedItems);\n  });\n  get focusedItemId() {\n    const focusedItem = this.focusedItem();\n    return focusedItem && focusedItem.item?.id ? focusedItem.item.id : ObjectUtils.isNotEmpty(this.focusedItem()) ? `${this.panelId}_${this.focusedItem().key}` : undefined;\n  }\n  constructor(el) {\n    this.el = el;\n  }\n  ngOnChanges(changes) {\n    const hasItems = !!changes?.items?.currentValue;\n    if (hasItems) {\n      this.processedItems.set(this.createProcessedItems(changes?.items?.currentValue || this.items || []));\n      return;\n    }\n    // Update and keep `expanded` property from previous data\n    else {\n      this.processedItems.update(prev => prev.map(i => ({\n        ...i,\n        expanded: i.expanded\n      })));\n    }\n  }\n  getItemProp(processedItem, name) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name]) : undefined;\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemActive(processedItem) {\n    return this.activeItemPath().some(path => path.key === processedItem.parentKey);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isElementInPanel(event, element) {\n    const panel = event.currentTarget.closest('[data-pc-section=\"panel\"]');\n    return panel && panel.contains(element);\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isVisibleItem(processedItem) {\n    return !!processedItem && (processedItem.level === 0 || this.isItemActive(processedItem)) && this.isItemVisible(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem) && !processedItem.separator;\n  }\n  findFirstItem() {\n    return this.visibleItems().find(processedItem => this.isValidItem(processedItem));\n  }\n  findLastItem() {\n    return ObjectUtils.findLast(this.visibleItems(), processedItem => this.isValidItem(processedItem));\n  }\n  findItemByEventTarget(target) {\n    let parentNode = target;\n    while (parentNode && parentNode.tagName?.toLowerCase() !== 'li') {\n      parentNode = parentNode?.parentNode;\n    }\n    return parentNode?.id && this.visibleItems().find(processedItem => this.isValidItem(processedItem) && `${this.panelId}_${processedItem.key}` === parentNode.id);\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        icon: item.icon,\n        expanded: item.expanded,\n        separator: item.separator,\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  findProcessedItemByItemKey(key, processedItems, level = 0) {\n    processedItems = processedItems || this.processedItems();\n    if (processedItems && processedItems.length) {\n      for (let i = 0; i < processedItems.length; i++) {\n        const processedItem = processedItems[i];\n        if (this.getItemProp(processedItem, 'key') === key) return processedItem;\n        const matchedItem = this.findProcessedItemByItemKey(key, processedItem.items, level + 1);\n        if (matchedItem) return matchedItem;\n      }\n    }\n  }\n  flatItems(processedItems, processedFlattenItems = []) {\n    processedItems && processedItems.forEach(processedItem => {\n      if (this.isVisibleItem(processedItem)) {\n        processedFlattenItems.push(processedItem);\n        this.flatItems(processedItem.items, processedFlattenItems);\n      }\n    });\n    return processedFlattenItems;\n  }\n  changeFocusedItem(event) {\n    const {\n      originalEvent,\n      processedItem,\n      focusOnNext,\n      selfCheck,\n      allowHeaderFocus = true\n    } = event;\n    if (ObjectUtils.isNotEmpty(this.focusedItem()) && this.focusedItem().key !== processedItem.key) {\n      this.focusedItem.set(processedItem);\n      this.scrollInView();\n    } else if (allowHeaderFocus) {\n      this.headerFocus.emit({\n        originalEvent,\n        focusOnNext,\n        selfCheck\n      });\n    }\n  }\n  scrollInView() {\n    const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  onFocus(event) {\n    if (!this.focused) {\n      this.focused = true;\n      const focusedItem = this.focusedItem() || (this.isElementInPanel(event, event.relatedTarget) ? this.findItemByEventTarget(event.target) || this.findFirstItem() : this.findLastItem());\n      if (event.relatedTarget !== null) this.focusedItem.set(focusedItem);\n    }\n  }\n  onBlur(event) {\n    const target = event.relatedTarget;\n    if (this.focused && !this.el.nativeElement.contains(target)) {\n      this.focused = false;\n      this.focusedItem.set(null);\n      this.searchValue = '';\n    }\n  }\n  onItemToggle(event) {\n    const {\n      processedItem,\n      expanded\n    } = event;\n    processedItem.expanded = !processedItem.expanded;\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== processedItem.parentKey);\n    expanded && activeItemPath.push(processedItem);\n    this.activeItemPath.set(activeItemPath);\n    this.processedItems.update(value => value.map(i => i === processedItem ? processedItem : i));\n    this.focusedItem.set(processedItem);\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n      case 'Tab':\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findNextItem(this.focusedItem()) : this.findFirstItem();\n    this.changeFocusedItem({\n      originalEvent: event,\n      processedItem,\n      focusOnNext: true\n    });\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findPrevItem(this.focusedItem()) : this.findLastItem();\n    this.changeFocusedItem({\n      originalEvent: event,\n      processedItem,\n      selfCheck: true\n    });\n    event.preventDefault();\n  }\n  onArrowLeftKey(event) {\n    if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n      const matched = this.activeItemPath().some(p => p.key === this.focusedItem().key);\n      if (matched) {\n        const activeItemPath = this.activeItemPath().filter(p => p.key !== this.focusedItem().key);\n        this.activeItemPath.set(activeItemPath);\n      } else {\n        const focusedItem = ObjectUtils.isNotEmpty(this.focusedItem().parent) ? this.focusedItem().parent : this.focusedItem();\n        this.focusedItem.set(focusedItem);\n      }\n      event.preventDefault();\n    }\n  }\n  onArrowRightKey(event) {\n    if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n      const grouped = this.isItemGroup(this.focusedItem());\n      if (grouped) {\n        const matched = this.activeItemPath().some(p => p.key === this.focusedItem().key);\n        if (matched) {\n          this.onArrowDownKey(event);\n        } else {\n          const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItem().parentKey);\n          activeItemPath.push(this.focusedItem());\n          this.activeItemPath.set(activeItemPath);\n        }\n      }\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedItem({\n      originalEvent: event,\n      processedItem: this.findFirstItem(),\n      allowHeaderFocus: false\n    });\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItem({\n      originalEvent: event,\n      processedItem: this.findLastItem(),\n      focusOnNext: true,\n      allowHeaderFocus: false\n    });\n    event.preventDefault();\n  }\n  onEnterKey(event) {\n    if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n      const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && (DomHandler.findSingle(element, '[data-pc-section=\"action\"]') || DomHandler.findSingle(element, 'a,button'));\n      anchorElement ? anchorElement.click() : element && element.click();\n    }\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  findNextItem(processedItem) {\n    const index = this.visibleItems().findIndex(item => item.key === processedItem.key);\n    const matchedItem = index < this.visibleItems().length - 1 ? this.visibleItems().slice(index + 1).find(pItem => this.isValidItem(pItem)) : undefined;\n    return matchedItem || processedItem;\n  }\n  findPrevItem(processedItem) {\n    const index = this.visibleItems().findIndex(item => item.key === processedItem.key);\n    const matchedItem = index > 0 ? ObjectUtils.findLast(this.visibleItems().slice(0, index), pItem => this.isValidItem(pItem)) : undefined;\n    return matchedItem || processedItem;\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let matchedItem = null;\n    let matched = false;\n    if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n      const focusedItemIndex = this.visibleItems().findIndex(processedItem => processedItem.key === this.focusedItem().key);\n      matchedItem = this.visibleItems().slice(focusedItemIndex).find(processedItem => this.isItemMatched(processedItem));\n      matchedItem = ObjectUtils.isEmpty(matchedItem) ? this.visibleItems().slice(0, focusedItemIndex).find(processedItem => this.isItemMatched(processedItem)) : matchedItem;\n    } else {\n      matchedItem = this.visibleItems().find(processedItem => this.isItemMatched(processedItem));\n    }\n    if (ObjectUtils.isNotEmpty(matchedItem)) {\n      matched = true;\n    }\n    if (ObjectUtils.isEmpty(matchedItem) && ObjectUtils.isEmpty(this.focusedItem())) {\n      matchedItem = this.findFirstItem();\n    }\n    if (ObjectUtils.isNotEmpty(matchedItem)) {\n      this.changeFocusedItem({\n        originalEvent: event,\n        processedItem: matchedItem,\n        allowHeaderFocus: false\n      });\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  static ɵfac = function PanelMenuList_Factory(t) {\n    return new (t || PanelMenuList)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PanelMenuList,\n    selectors: [[\"p-panelMenuList\"]],\n    viewQuery: function PanelMenuList_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.subMenuViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      panelId: \"panelId\",\n      id: \"id\",\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      parentExpanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"parentExpanded\", \"parentExpanded\", booleanAttribute],\n      expanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"expanded\", \"expanded\", booleanAttribute],\n      transitionOptions: \"transitionOptions\",\n      root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n      activeItem: \"activeItem\"\n    },\n    outputs: {\n      itemToggle: \"itemToggle\",\n      headerFocus: \"headerFocus\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 10,\n    consts: [[\"submenu\", \"\"], [3, \"itemToggle\", \"keydown\", \"menuFocus\", \"menuBlur\", \"root\", \"id\", \"panelId\", \"tabindex\", \"itemTemplate\", \"focusedItemId\", \"activeItemPath\", \"transitionOptions\", \"items\", \"parentExpanded\"]],\n    template: function PanelMenuList_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"p-panelMenuSub\", 1, 0);\n        i0.ɵɵlistener(\"itemToggle\", function PanelMenuList_Template_p_panelMenuSub_itemToggle_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemToggle($event));\n        })(\"keydown\", function PanelMenuList_Template_p_panelMenuSub_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"menuFocus\", function PanelMenuList_Template_p_panelMenuSub_menuFocus_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus($event));\n        })(\"menuBlur\", function PanelMenuList_Template_p_panelMenuSub_menuBlur_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur($event));\n        });\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"root\", true)(\"id\", ctx.panelId + \"_list\")(\"panelId\", ctx.panelId)(\"tabindex\", ctx.tabindex)(\"itemTemplate\", ctx.itemTemplate)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"activeItemPath\", ctx.activeItemPath())(\"transitionOptions\", ctx.transitionOptions)(\"items\", ctx.processedItems())(\"parentExpanded\", ctx.parentExpanded);\n      }\n    },\n    dependencies: [PanelMenuSub],\n    styles: [\"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenuList, [{\n    type: Component,\n    args: [{\n      selector: 'p-panelMenuList',\n      template: `\n        <p-panelMenuSub\n            #submenu\n            [root]=\"true\"\n            [id]=\"panelId + '_list'\"\n            [panelId]=\"panelId\"\n            [tabindex]=\"tabindex\"\n            [itemTemplate]=\"itemTemplate\"\n            [focusedItemId]=\"focused ? focusedItemId : undefined\"\n            [activeItemPath]=\"activeItemPath()\"\n            [transitionOptions]=\"transitionOptions\"\n            [items]=\"processedItems()\"\n            [parentExpanded]=\"parentExpanded\"\n            (itemToggle)=\"onItemToggle($event)\"\n            (keydown)=\"onKeyDown($event)\"\n            (menuFocus)=\"onFocus($event)\"\n            (menuBlur)=\"onBlur($event)\"\n        ></p-panelMenuSub>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    panelId: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    parentExpanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    expanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    root: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    itemToggle: [{\n      type: Output\n    }],\n    headerFocus: [{\n      type: Output\n    }],\n    subMenuViewChild: [{\n      type: ViewChild,\n      args: ['submenu']\n    }]\n  });\n})();\n/**\n * PanelMenu is a hybrid of Accordion and Tree components.\n * @group Components\n */\nclass PanelMenu {\n  cd;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether multiple tabs can be activated at the same time or not.\n   * @group Props\n   */\n  multiple = false;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  templates;\n  containerViewChild;\n  submenuIconTemplate;\n  itemTemplate;\n  animating;\n  activeItem = signal(null);\n  ngOnInit() {\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  constructor(cd) {\n    this.cd = cd;\n  }\n  /**\n   * Collapses open panels.\n   * @group Method\n   */\n  collapseAll() {\n    for (let item of this.model) {\n      if (item.expanded) {\n        item.expanded = false;\n      }\n    }\n    this.cd.detectChanges();\n  }\n  onToggleDone() {\n    this.animating = false;\n    this.cd.markForCheck();\n  }\n  changeActiveItem(event, item, index, selfActive = false) {\n    if (!this.isItemDisabled(item)) {\n      const activeItem = selfActive ? item : this.activeItem && ObjectUtils.equals(item, this.activeItem) ? null : item;\n      this.activeItem.set(activeItem);\n    }\n  }\n  getAnimation(item) {\n    return item.expanded ? {\n      value: 'visible',\n      params: {\n        transitionParams: this.animating ? this.transitionOptions : '0ms',\n        height: '*'\n      }\n    } : {\n      value: 'hidden',\n      params: {\n        transitionParams: this.transitionOptions,\n        height: '0'\n      }\n    };\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  isItemActive(item) {\n    return item.expanded;\n  }\n  isItemVisible(item) {\n    return this.getItemProp(item, 'visible') !== false;\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemGroup(item) {\n    return ObjectUtils.isNotEmpty(item.items);\n  }\n  getPanelId(index, item) {\n    return item && item.id ? item.id : `${this.id}_${index}`;\n  }\n  getHeaderId(item, index) {\n    return item.id ? item.id + '_header' : `${this.getPanelId(index)}_header`;\n  }\n  getContentId(item, index) {\n    return item.id ? item.id + '_content' : `${this.getPanelId(index)}_content`;\n  }\n  updateFocusedHeader(event) {\n    const {\n      originalEvent,\n      focusOnNext,\n      selfCheck\n    } = event;\n    const panelElement = originalEvent.currentTarget.closest('[data-pc-section=\"panel\"]');\n    const header = selfCheck ? DomHandler.findSingle(panelElement, '[data-pc-section=\"header\"]') : focusOnNext ? this.findNextHeader(panelElement) : this.findPrevHeader(panelElement);\n    header ? this.changeFocusedHeader(originalEvent, header) : focusOnNext ? this.onHeaderHomeKey(originalEvent) : this.onHeaderEndKey(originalEvent);\n  }\n  changeFocusedHeader(event, element) {\n    element && DomHandler.focus(element);\n  }\n  findNextHeader(panelElement, selfCheck = false) {\n    const nextPanelElement = selfCheck ? panelElement : panelElement.nextElementSibling;\n    const headerElement = DomHandler.findSingle(nextPanelElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeader(headerElement.parentElement) : headerElement : null;\n  }\n  findPrevHeader(panelElement, selfCheck = false) {\n    const prevPanelElement = selfCheck ? panelElement : panelElement.previousElementSibling;\n    const headerElement = DomHandler.findSingle(prevPanelElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeader(headerElement.parentElement) : headerElement : null;\n  }\n  findFirstHeader() {\n    return this.findNextHeader(this.containerViewChild.nativeElement.firstElementChild, true);\n  }\n  findLastHeader() {\n    return this.findPrevHeader(this.containerViewChild.nativeElement.lastElementChild, true);\n  }\n  onHeaderClick(event, item, index) {\n    if (this.isItemDisabled(item)) {\n      event.preventDefault();\n      return;\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item\n      });\n    }\n    if (!this.multiple) {\n      for (let modelItem of this.model) {\n        if (item !== modelItem && modelItem.expanded) {\n          modelItem.expanded = false;\n        }\n      }\n    }\n    item.expanded = !item.expanded;\n    this.changeActiveItem(event, item, index);\n    this.animating = true;\n    DomHandler.focus(event.currentTarget);\n  }\n  onHeaderKeyDown(event, item, index) {\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onHeaderArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onHeaderArrowUpKey(event);\n        break;\n      case 'Home':\n        this.onHeaderHomeKey(event);\n        break;\n      case 'End':\n        this.onHeaderEndKey(event);\n        break;\n      case 'Enter':\n      case 'Space':\n        this.onHeaderEnterKey(event, item, index);\n        break;\n      default:\n        break;\n    }\n  }\n  onHeaderArrowDownKey(event) {\n    const rootList = DomHandler.getAttribute(event.currentTarget, 'data-p-highlight') === true ? DomHandler.findSingle(event.currentTarget.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n    rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({\n      originalEvent: event,\n      focusOnNext: true\n    });\n    event.preventDefault();\n  }\n  onHeaderArrowUpKey(event) {\n    const prevHeader = this.findPrevHeader(event.currentTarget.parentElement) || this.findLastHeader();\n    const rootList = DomHandler.getAttribute(prevHeader, 'data-p-highlight') === true ? DomHandler.findSingle(prevHeader.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n    rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({\n      originalEvent: event,\n      focusOnNext: false\n    });\n    event.preventDefault();\n  }\n  onHeaderHomeKey(event) {\n    this.changeFocusedHeader(event, this.findFirstHeader());\n    event.preventDefault();\n  }\n  onHeaderEndKey(event) {\n    this.changeFocusedHeader(event, this.findLastHeader());\n    event.preventDefault();\n  }\n  onHeaderEnterKey(event, item, index) {\n    const headerAction = DomHandler.findSingle(event.currentTarget, '[data-pc-section=\"headeraction\"]');\n    headerAction ? headerAction.click() : this.onHeaderClick(event, item, index);\n    event.preventDefault();\n  }\n  static ɵfac = function PanelMenu_Factory(t) {\n    return new (t || PanelMenu)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: PanelMenu,\n    selectors: [[\"p-panelMenu\"]],\n    contentQueries: function PanelMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function PanelMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n      transitionOptions: \"transitionOptions\",\n      id: \"id\",\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    decls: 3,\n    vars: 5,\n    consts: [[\"container\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngStyle\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-panelmenu-panel\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [1, \"p-panelmenu-panel\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"button\", 3, \"click\", \"keydown\", \"ngClass\", \"ngStyle\", \"pTooltip\", \"tabindex\", \"tooltipOptions\"], [1, \"p-panelmenu-header-content\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-panelmenu-header-action\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"class\", \"p-toggleable-content\", \"role\", \"region\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-panelmenu-header-action\", 3, \"target\", 4, \"ngIf\"], [1, \"p-panelmenu-header-action\", 3, \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-panelmenu-header-action\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"role\", \"region\", 1, \"p-toggleable-content\", 3, \"ngClass\"], [1, \"p-panelmenu-content\"], [3, \"headerFocus\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"root\", \"activeItem\", \"tabindex\", \"parentExpanded\"]],\n    template: function PanelMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 3, 0);\n        i0.ɵɵtemplate(2, PanelMenu_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-panelmenu p-component\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Tooltip, ChevronDownIcon, ChevronRightIcon, PanelMenuList],\n    styles: [_c9],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('rootItem', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-panelMenu',\n      template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-panelmenu p-component'\" #container>\n            <ng-container *ngFor=\"let item of model; let f = first; let l = last; let i = index\">\n                <div *ngIf=\"isItemVisible(item)\" class=\"p-panelmenu-panel\" [ngClass]=\"getItemProp(item, 'headerClass')\" [ngStyle]=\"getItemProp(item, 'style')\" [attr.data-pc-section]=\"'panel'\">\n                    <div\n                        [ngClass]=\"{ 'p-component p-panelmenu-header': true, 'p-highlight': isItemActive(item), 'p-disabled': isItemDisabled(item) }\"\n                        [class]=\"getItemProp(item, 'styleClass')\"\n                        [ngStyle]=\"getItemProp(item, 'style')\"\n                        [pTooltip]=\"getItemProp(item, 'tooltip')\"\n                        [attr.id]=\"getHeaderId(item, i)\"\n                        [tabindex]=\"0\"\n                        role=\"button\"\n                        [tooltipOptions]=\"getItemProp(item, 'tooltipOptions')\"\n                        [attr.aria-expanded]=\"isItemActive(item)\"\n                        [attr.aria-label]=\"getItemProp(item, 'label')\"\n                        [attr.aria-controls]=\"getContentId(item, i)\"\n                        [attr.aria-disabled]=\"isItemDisabled(item)\"\n                        [attr.data-p-highlight]=\"isItemActive(item)\"\n                        [attr.data-p-disabled]=\"isItemDisabled(item)\"\n                        [attr.data-pc-section]=\"'header'\"\n                        (click)=\"onHeaderClick($event, item, i)\"\n                        (keydown)=\"onHeaderKeyDown($event, item, i)\"\n                    >\n                        <div class=\"p-panelmenu-header-content\">\n                            <ng-container *ngIf=\"!itemTemplate\">\n                                <a\n                                    *ngIf=\"!getItemProp(item, 'routerLink')\"\n                                    [attr.href]=\"getItemProp(item, 'url')\"\n                                    [attr.tabindex]=\"-1\"\n                                    [target]=\"getItemProp(item, 'target')\"\n                                    [attr.title]=\"getItemProp(item, 'title')\"\n                                    class=\"p-panelmenu-header-action\"\n                                    [attr.data-pc-section]=\"'headeraction'\"\n                                >\n                                    <ng-container *ngIf=\"isItemGroup(item)\">\n                                        <ng-container *ngIf=\"!submenuIconTemplate\">\n                                            <ChevronDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(item)\" />\n                                            <ChevronRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(item)\" />\n                                        </ng-container>\n                                        <ng-template *ngTemplateOutlet=\"submenuIconTemplate\"></ng-template>\n                                    </ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"getItemProp(item, 'iconStyle')\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(item, 'escape') !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(item, 'badge')\" [ngClass]=\"getItemProp(item, 'badgeStyleClass')\">{{ getItemProp(item, 'badge') }}</span>\n                                </a>\n                            </ng-container>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            <a\n                                *ngIf=\"getItemProp(item, 'routerLink')\"\n                                [routerLink]=\"getItemProp(item, 'routerLink')\"\n                                [queryParams]=\"getItemProp(item, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(item, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                class=\"p-panelmenu-header-action\"\n                                [attr.tabindex]=\"-1\"\n                                [fragment]=\"getItemProp(item, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(item, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(item, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(item, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(item, 'replaceUrl')\"\n                                [state]=\"getItemProp(item, 'state')\"\n                                [attr.data-pc-section]=\"'headeraction'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(item)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <ChevronDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(item)\" />\n                                        <ChevronRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(item)\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"getItemProp(item, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(item, 'escape') !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(item, 'badge')\" [ngClass]=\"getItemProp(item, 'badgeStyleClass')\">{{ getItemProp(item, 'badge') }}</span>\n                            </a>\n                        </div>\n                    </div>\n                    <div\n                        *ngIf=\"isItemGroup(item)\"\n                        class=\"p-toggleable-content\"\n                        [ngClass]=\"{ 'p-panelmenu-expanded': isItemActive(item) }\"\n                        [@rootItem]=\"getAnimation(item)\"\n                        (@rootItem.done)=\"onToggleDone()\"\n                        role=\"region\"\n                        [attr.id]=\"getContentId(item, i)\"\n                        [attr.aria-labelledby]=\"getHeaderId(item, i)\"\n                        [attr.data-pc-section]=\"'toggleablecontent'\"\n                    >\n                        <div class=\"p-panelmenu-content\" [attr.data-pc-section]=\"'menucontent'\">\n                            <p-panelMenuList\n                                [panelId]=\"getPanelId(i, item)\"\n                                [items]=\"getItemProp(item, 'items')\"\n                                [itemTemplate]=\"itemTemplate\"\n                                [transitionOptions]=\"transitionOptions\"\n                                [root]=\"true\"\n                                [activeItem]=\"activeItem()\"\n                                [tabindex]=\"tabindex\"\n                                [parentExpanded]=\"isItemActive(item)\"\n                                (headerFocus)=\"updateFocusedHeader($event)\"\n                            ></p-panelMenuList>\n                        </div>\n                    </div>\n                </div>\n            </ng-container>\n        </div>\n    `,\n      animations: [trigger('rootItem', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }]\n  });\n})();\nclass PanelMenuModule {\n  static ɵfac = function PanelMenuModule_Factory(t) {\n    return new (t || PanelMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PanelMenuModule,\n    declarations: [PanelMenu, PanelMenuSub, PanelMenuList],\n    imports: [CommonModule, RouterModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, ChevronDownIcon, ChevronRightIcon],\n    exports: [PanelMenu, RouterModule, TooltipModule, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, ChevronDownIcon, ChevronRightIcon, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, ChevronDownIcon, ChevronRightIcon],\n      exports: [PanelMenu, RouterModule, TooltipModule, SharedModule],\n      declarations: [PanelMenu, PanelMenuSub, PanelMenuList]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PanelMenu, PanelMenuList, PanelMenuModule, PanelMenuSub };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,SAAO;AAAA,EACjB,kBAAkB;AAAA,EAClB,yBAAyB;AAC3B;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,cAAc;AAChB;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0GAA0G,IAAI,KAAK;AAC1H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,gBAAgB,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC5G;AACF;AACA,SAAS,2GAA2G,IAAI,KAAK;AAC3H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,gBAAgB,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC5G;AACF;AACA,SAAS,0FAA0F,IAAI,KAAK;AAC1G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2GAA2G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,4GAA4G,GAAG,GAAG,kBAAkB,EAAE;AAChS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa,gBAAgB,CAAC;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa,gBAAgB,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,2FAA2F,IAAI,KAAK;AAAC;AAC9G,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4FAA4F,GAAG,GAAG,aAAa;AAAA,EAClI;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2FAA2F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,8EAA8E,GAAG,GAAG,MAAM,EAAE;AACrO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU,mBAAmB;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,UAAU,mBAAmB;AAAA,EACxE;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,iBAAiB,IAAI,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC9G;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EACpE;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,YAAY,kBAAkB,OAAO,GAAM,cAAc;AAAA,EAC7F;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,IAAG,WAAW,WAAW,iBAAiB,eAAe;AACzD,IAAG,UAAU;AACb,IAAG,kBAAkB,iBAAiB,KAAK;AAAA,EAC7C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,4EAA4E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2EAA2E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE;AACvgB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC;AAChK,IAAG,YAAY,QAAQ,OAAO,YAAY,kBAAkB,KAAK,GAAM,aAAa,EAAE,mBAAmB,QAAQ,EAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI;AACnK,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAC1D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,iBAAiB,IAAI;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,iBAAiB,QAAQ,OAAO,OAAO,iBAAiB,KAAK,YAAY,KAAK,EAAE,YAAY,YAAY;AAC/H,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,iBAAiB,KAAK;AAAA,EAC9C;AACF;AACA,SAAS,0GAA0G,IAAI,KAAK;AAC1H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,iBAAiB,EAAE;AAAA,EACrC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,gBAAgB,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC5G;AACF;AACA,SAAS,2GAA2G,IAAI,KAAK;AAC3H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,kBAAkB,EAAE;AAAA,EACtC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,gBAAgB,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC5G;AACF;AACA,SAAS,0FAA0F,IAAI,KAAK;AAC1G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2GAA2G,GAAG,GAAG,iBAAiB,EAAE,EAAE,GAAG,4GAA4G,GAAG,GAAG,kBAAkB,EAAE;AAChS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa,gBAAgB,CAAC;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa,gBAAgB,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,2FAA2F,IAAI,KAAK;AAAC;AAC9G,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4FAA4F,GAAG,GAAG,aAAa;AAAA,EAClI;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2FAA2F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,8EAA8E,GAAG,GAAG,MAAM,EAAE;AACrO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU,mBAAmB;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,UAAU,mBAAmB;AAAA,EACxE;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,iBAAiB,IAAI,EAAE,WAAW,OAAO,YAAY,kBAAkB,WAAW,CAAC;AAAA,EAC9G;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EACpE;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,YAAY,kBAAkB,OAAO,GAAM,cAAc;AAAA,EAC7F;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,kBAAkB,iBAAiB,CAAC;AAChF,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAAA,EACpE;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,4EAA4E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2EAA2E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE;AACvgB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,kBAAkB,aAAa,CAAC,EAAE,oBAAoB,wBAAwB,EAAE,2BAA2B,OAAO,YAAY,kBAAkB,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,YAAY,kBAAkB,UAAU,CAAC,CAAC,EAAE,UAAU,OAAO,YAAY,kBAAkB,QAAQ,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,kBAAkB,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,kBAAkB,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,kBAAkB,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC;AAC33B,IAAG,YAAY,SAAS,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,mBAAmB,QAAQ,EAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI;AACpJ,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,gBAAgB,CAAC;AAC1D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,iBAAiB,IAAI;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,QAAQ,MAAM,KAAK,EAAE,YAAY,iBAAiB;AAC7G,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,iBAAiB,KAAK;AAAA,EAC9C;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6DAA6D,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,6DAA6D,GAAG,IAAI,KAAK,EAAE;AAC5K,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,kBAAkB,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AAAC;AAC3F,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,aAAa;AAAA,EAC/G;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,MAAM,EAAE;AAC1F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,iBAAiB,IAAI,CAAC;AAAA,EACrI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,EAAE;AACzC,IAAG,WAAW,cAAc,SAAS,+FAA+F,QAAQ;AAC1I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc,CAAC,EAAE;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,UAAU,gBAAgB,IAAI,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,SAAS,oBAAoB,OAAO,OAAO,iBAAiB,KAAK,EAAE,gBAAgB,OAAO,YAAY,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,iBAAiB,OAAO,aAAa,EAAE,kBAAkB,OAAO,cAAc,EAAE,SAAS,OAAO,QAAQ,CAAC,EAAE,kBAAkB,CAAC,CAAC,OAAO,kBAAkB,OAAO,eAAe,gBAAgB,CAAC;AAAA,EACrb;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,IAAG,WAAW,SAAS,SAAS,8DAA8D,QAAQ;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,mBAAsB,cAAc,EAAE;AAC5C,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,QAAQ,gBAAgB,CAAC;AAAA,IACpE,CAAC;AACD,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AACxL,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,iBAAiB,SAAS,iFAAiF;AACvH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,kBAAkB,EAAE;AACtG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,mBAAmB,OAAO;AAChC,UAAM,WAAW,OAAO;AACxB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,YAAY,kBAAkB,YAAY,CAAC;AAChE,IAAG,YAAY,YAAY,iBAAiB,YAAY,KAAK,EAAE,WAAW,OAAO,cAAc,gBAAgB,KAAK,CAAC,OAAO,eAAe,gBAAgB,CAAC;AAC5J,IAAG,WAAW,WAAW,OAAO,aAAa,gBAAgB,CAAC,EAAE,WAAW,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,YAAY,OAAO,YAAY,kBAAkB,SAAS,CAAC,EAAE,kBAAkB,OAAO,YAAY,kBAAkB,gBAAgB,CAAC;AAC/P,IAAG,YAAY,MAAM,OAAO,UAAU,gBAAgB,CAAC,EAAE,cAAc,OAAO,YAAY,kBAAkB,OAAO,CAAC,EAAE,iBAAiB,OAAO,YAAY,gBAAgB,IAAI,OAAO,aAAa,gBAAgB,IAAI,MAAS,EAAE,cAAc,OAAO,QAAQ,CAAC,EAAE,gBAAgB,OAAO,eAAe,CAAC,EAAE,iBAAiB,OAAO,gBAAgB,QAAQ,CAAC,EAAE,mBAAmB,OAAO,eAAe,gBAAgB,CAAC;AACvZ,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,aAAa,gBAAgB,CAAC;AAC/D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,gBAAgB,KAAK,OAAO,YAAY,gBAAgB,MAAM,OAAO,eAAe,gBAAgB,KAAK,OAAO,UAAU;AAAA,EACvK;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,0CAA0C,GAAG,IAAI,MAAM,CAAC;AAAA,EACvI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,iBAAiB,SAAS;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,iBAAiB,aAAa,OAAO,cAAc,gBAAgB,CAAC;AAAA,EAC7F;AACF;AACA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,kCAAkC;AAAA,EAClC,eAAe;AAAA,EACf,cAAc;AAChB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,wBAAwB;AAC1B;AACA,SAAS,2GAA2G,IAAI,KAAK;AAC3H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAAA,EAC9C;AACF;AACA,SAAS,4GAA4G,IAAI,KAAK;AAC5H,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,EAAE;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAAA,EAC9C;AACF;AACA,SAAS,yFAAyF,IAAI,KAAK;AACzG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,4GAA4G,GAAG,GAAG,mBAAmB,EAAE,EAAE,GAAG,6GAA6G,GAAG,GAAG,oBAAoB,EAAE;AACtS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa,OAAO,CAAC;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa,OAAO,CAAC;AAAA,EACrD;AACF;AACA,SAAS,0FAA0F,IAAI,KAAK;AAAC;AAC7G,SAAS,4EAA4E,IAAI,KAAK;AAC5F,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2FAA2F,GAAG,GAAG,aAAa;AAAA,EACjI;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,0FAA0F,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6EAA6E,GAAG,GAAG,MAAM,EAAE;AAClO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,mBAAmB;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB;AAAA,EAC9D;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,QAAQ,IAAI,EAAE,WAAW,OAAO,YAAY,SAAS,WAAW,CAAC;AAAA,EAC5F;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,YAAY,SAAS,OAAO,GAAM,cAAc;AAAA,EACpF;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,SAAS,iBAAiB,CAAC;AACvE,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,mEAAmE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,0EAA0E,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mEAAmE,GAAG,GAAG,QAAQ,EAAE;AACjgB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,YAAY,SAAS,QAAQ,CAAC;AAC7D,IAAG,YAAY,QAAQ,OAAO,YAAY,SAAS,KAAK,GAAM,aAAa,EAAE,YAAY,EAAE,EAAE,SAAS,OAAO,YAAY,SAAS,OAAO,CAAC,EAAE,mBAAmB,cAAc;AAC7K,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,OAAO,CAAC;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,IAAI;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,SAAS,QAAQ,MAAM,KAAK,EAAE,YAAY,YAAY;AAC/F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,4DAA4D,GAAG,IAAI,KAAK,EAAE;AAC3F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY,SAAS,YAAY,CAAC;AAAA,EAClE;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4FAA4F,IAAI,KAAK;AAC5G,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,mBAAmB,EAAE;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAAA,EAC9C;AACF;AACA,SAAS,6FAA6F,IAAI,KAAK;AAC7G,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,EAAE;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAc,gBAAgB;AAAA,EAC9C;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6FAA6F,GAAG,GAAG,mBAAmB,EAAE,EAAE,GAAG,8FAA8F,GAAG,GAAG,oBAAoB,EAAE;AACxQ,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,aAAa,OAAO,CAAC;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa,OAAO,CAAC;AAAA,EACrD;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAAC;AAC9F,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4EAA4E,GAAG,GAAG,aAAa;AAAA,EAClH;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,8DAA8D,GAAG,GAAG,MAAM,EAAE;AACpM,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,mBAAmB;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,mBAAmB;AAAA,EAC9D;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,QAAQ,IAAI,EAAE,WAAW,OAAO,YAAY,SAAS,WAAW,CAAC;AAAA,EAC5F;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,OAAO,YAAY,SAAS,OAAO,GAAM,cAAc;AAAA,EACpF;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,SAAS,iBAAiB,CAAC;AACvE,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC3D;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,QAAQ,EAAE;AACtb,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,YAAY,SAAS,YAAY,CAAC,EAAE,eAAe,OAAO,YAAY,SAAS,aAAa,CAAC,EAAE,oBAAoB,wBAAwB,EAAE,2BAA2B,OAAO,YAAY,SAAS,yBAAyB,KAAQ,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,YAAY,SAAS,QAAQ,CAAC,EAAE,YAAY,OAAO,YAAY,SAAS,UAAU,CAAC,EAAE,uBAAuB,OAAO,YAAY,SAAS,qBAAqB,CAAC,EAAE,oBAAoB,OAAO,YAAY,SAAS,kBAAkB,CAAC,EAAE,sBAAsB,OAAO,YAAY,SAAS,oBAAoB,CAAC,EAAE,cAAc,OAAO,YAAY,SAAS,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,SAAS,OAAO,CAAC;AACvsB,IAAG,YAAY,YAAY,EAAE,EAAE,mBAAmB,cAAc;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,OAAO,CAAC;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,QAAQ,IAAI;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,SAAS,QAAQ,MAAM,KAAK,EAAE,YAAY,iBAAiB;AACpG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,kBAAkB,SAAS,uFAAuF;AAC9H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,mBAAmB,EAAE;AACxD,IAAG,WAAW,eAAe,SAAS,qFAAqF,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,UAAU,OAAO;AACvB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,OAAO,aAAa,OAAO,CAAC,CAAC,EAAE,aAAa,OAAO,aAAa,OAAO,CAAC;AAC7H,IAAG,YAAY,MAAM,OAAO,aAAa,SAAS,IAAI,CAAC,EAAE,mBAAmB,OAAO,YAAY,SAAS,IAAI,CAAC,EAAE,mBAAmB,mBAAmB;AACrJ,IAAG,UAAU;AACb,IAAG,YAAY,mBAAmB,aAAa;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW,MAAM,OAAO,CAAC,EAAE,SAAS,OAAO,YAAY,SAAS,OAAO,CAAC,EAAE,gBAAgB,OAAO,YAAY,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,QAAQ,IAAI,EAAE,cAAc,OAAO,WAAW,CAAC,EAAE,YAAY,OAAO,QAAQ,EAAE,kBAAkB,OAAO,aAAa,OAAO,CAAC;AAAA,EAC5T;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,SAAS,SAAS,6DAA6D,QAAQ;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,YAAM,UAAU,OAAO;AACvB,YAAM,OAAO,OAAO;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,QAAQ,SAAS,IAAI,CAAC;AAAA,IACnE,CAAC,EAAE,WAAW,SAAS,+DAA+D,QAAQ;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,YAAM,UAAU,OAAO;AACvB,YAAM,OAAO,OAAO;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,SAAS,IAAI,CAAC;AAAA,IACrE,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6CAA6C,GAAG,IAAI,KAAK,EAAE;AACrP,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,+CAA+C,GAAG,IAAI,OAAO,EAAE;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,UAAU,OAAO;AACvB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY,SAAS,aAAa,CAAC,EAAE,WAAW,OAAO,YAAY,SAAS,OAAO,CAAC;AACpH,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,YAAY,SAAS,YAAY,CAAC;AACvD,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,OAAO,aAAa,OAAO,GAAG,OAAO,eAAe,OAAO,CAAC,CAAC,EAAE,WAAW,OAAO,YAAY,SAAS,OAAO,CAAC,EAAE,YAAY,OAAO,YAAY,SAAS,SAAS,CAAC,EAAE,YAAY,CAAC,EAAE,kBAAkB,OAAO,YAAY,SAAS,gBAAgB,CAAC;AACvS,IAAG,YAAY,MAAM,OAAO,YAAY,SAAS,IAAI,CAAC,EAAE,iBAAiB,OAAO,aAAa,OAAO,CAAC,EAAE,cAAc,OAAO,YAAY,SAAS,OAAO,CAAC,EAAE,iBAAiB,OAAO,aAAa,SAAS,IAAI,CAAC,EAAE,iBAAiB,OAAO,eAAe,OAAO,CAAC,EAAE,oBAAoB,OAAO,aAAa,OAAO,CAAC,EAAE,mBAAmB,OAAO,eAAe,OAAO,CAAC,EAAE,mBAAmB,QAAQ;AACjY,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,CAAC;AACtH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,SAAS,YAAY,CAAC;AAC/D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY,OAAO,CAAC;AAAA,EACnD;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yCAAyC,GAAG,IAAI,OAAO,CAAC;AACzE,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,OAAO,CAAC;AAAA,EACrD;AACF;AACA,IAAM,MAAM;AACZ,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,IAAI,aAAa;AAAA,EAC9B,YAAY,IAAI,aAAa;AAAA,EAC7B,WAAW,IAAI,aAAa;AAAA,EAC5B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,YAAY,WAAW,IAAI;AACzB,SAAK,YAAY;AACjB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,UAAU,eAAe;AACvB,WAAO,cAAc,MAAM,MAAM,GAAG,KAAK,OAAO,IAAI,cAAc,GAAG;AAAA,EACvE;AAAA,EACA,WAAW,eAAe;AACxB,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO;AAAA,MACL,cAAc;AAAA,MACd,cAAc,KAAK,eAAe,aAAa;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY,eAAe,MAAM,QAAQ;AACvC,WAAO,iBAAiB,cAAc,OAAO,YAAY,aAAa,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,EAC5G;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,cAAc;AAAA,EACvB;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,eAAe,aAAa,KAAK,KAAK,eAAe,KAAK,UAAQ,QAAQ,KAAK,QAAQ,cAAc,GAAG;AAAA,EACtH;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,kBAAkB,KAAK,UAAU,aAAa;AAAA,EAC5D;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,YAAY,WAAW,cAAc,KAAK;AAAA,EACnD;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,aAAa,aAAa,IAAI;AAAA,MACxC,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,kBAAkB,KAAK;AAAA,QACvB,QAAQ;AAAA,MACV;AAAA,IACF,IAAI;AAAA,MACF,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,kBAAkB,KAAK;AAAA,QACvB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,CAAC,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE;AAAA,EAChI;AAAA,EACA,gBAAgB,OAAO;AACrB,WAAO,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,mBAAiB,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY,eAAe,WAAW,CAAC,EAAE,SAAS;AAAA,EAChK;AAAA,EACA,YAAY,OAAO,eAAe;AAChC,QAAI,CAAC,KAAK,eAAe,aAAa,GAAG;AACvC,WAAK,YAAY;AACjB,WAAK,YAAY,eAAe,WAAW;AAAA,QACzC,eAAe;AAAA,QACf,MAAM,cAAc;AAAA,MACtB,CAAC;AACD,WAAK,WAAW,KAAK;AAAA,QACnB;AAAA,QACA,UAAU,CAAC,KAAK,aAAa,aAAa;AAAA,MAC5C,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,WAAW,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,GAAG;AAC7C,WAAO,KAAK,KAAK,eAAiB,kBAAkB,WAAW,MAAM,SAAS,CAAC,GAAM,kBAAqB,UAAU,CAAC;AAAA,EACvH;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,eAAe;AAAA,MACf,OAAO;AAAA,MACP,cAAc;AAAA,MACd,OAAO,CAAI,WAAa,4BAA4B,SAAS,SAAS,eAAe;AAAA,MACrF,gBAAgB;AAAA,MAChB,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,gBAAgB;AAAA,MACnF,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,MAC9F,mBAAmB;AAAA,MACnB,gBAAgB,CAAI,WAAa,4BAA4B,kBAAkB,kBAAkB,gBAAgB;AAAA,IACnH;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,QAAQ,QAAQ,GAAG,WAAW,YAAY,WAAW,WAAW,UAAU,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,SAAS,wBAAwB,QAAQ,aAAa,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,GAAG,WAAW,SAAS,YAAY,WAAW,WAAW,YAAY,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,aAAa,GAAG,sBAAsB,GAAG,CAAC,QAAQ,YAAY,GAAG,WAAW,WAAW,YAAY,gBAAgB,GAAG,CAAC,GAAG,sBAAsB,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,MAAM,WAAW,SAAS,gBAAgB,qBAAqB,iBAAiB,kBAAkB,SAAS,kBAAkB,cAAc,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,UAAU,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,WAAW,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,QAAQ,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,oBAAoB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,WAAW,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,cAAc,MAAM,WAAW,SAAS,gBAAgB,qBAAqB,iBAAiB,kBAAkB,SAAS,gBAAgB,CAAC;AAAA,IAC93D,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,QAAG,WAAW,WAAW,SAAS,4CAA4C,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,KAAK,MAAM,CAAC;AAAA,QAClD,CAAC,EAAE,YAAY,SAAS,6CAA6C,QAAQ;AAC3E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,SAAS,KAAK,MAAM,CAAC;AAAA,QACjD,CAAC,EAAE,WAAW,SAAS,4CAA4C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,KAAK,MAAM,CAAC;AAAA,QACpD,CAAC;AACD,QAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,CAAC;AAC5E,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,YAAY,EAAE;AAC7E,QAAG,YAAY,yBAAyB,IAAI,aAAa,EAAE,mBAAmB,MAAM,EAAE,eAAe,CAAC,IAAI,cAAc;AACxH,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,YAAe,kBAAqB,SAAS,eAAe,gBAAgB,aAAY;AAAA,IAClL,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,WAAW,CAAC,MAAM,UAAU,MAAM;AAAA,QACpD,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACjH;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+GV,YAAY,CAAC,QAAQ,WAAW,CAAC,MAAM,UAAU,MAAM;AAAA,QACrD,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAC/G,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,SAAS,CAAC;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,IAAI,aAAa;AAAA,EAC9B,cAAc,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc,OAAO,IAAI;AAAA,EACzB,iBAAiB,OAAO,CAAC,CAAC;AAAA,EAC1B,iBAAiB,OAAO,CAAC,CAAC;AAAA,EAC1B,eAAe,SAAS,MAAM;AAC5B,UAAM,iBAAiB,KAAK,eAAe;AAC3C,WAAO,KAAK,UAAU,cAAc;AAAA,EACtC,CAAC;AAAA,EACD,IAAI,gBAAgB;AAClB,UAAM,cAAc,KAAK,YAAY;AACrC,WAAO,eAAe,YAAY,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,WAAW,KAAK,YAAY,CAAC,IAAI,GAAG,KAAK,OAAO,IAAI,KAAK,YAAY,EAAE,GAAG,KAAK;AAAA,EAChK;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,WAAW,CAAC,CAAC,SAAS,OAAO;AACnC,QAAI,UAAU;AACZ,WAAK,eAAe,IAAI,KAAK,qBAAqB,SAAS,OAAO,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC;AACnG;AAAA,IACF,OAEK;AACH,WAAK,eAAe,OAAO,UAAQ,KAAK,IAAI,OAAM,iCAC7C,IAD6C;AAAA,QAEhD,UAAU,EAAE;AAAA,MACd,EAAE,CAAC;AAAA,IACL;AAAA,EACF;AAAA,EACA,YAAY,eAAe,MAAM;AAC/B,WAAO,iBAAiB,cAAc,OAAO,YAAY,aAAa,cAAc,KAAK,IAAI,CAAC,IAAI;AAAA,EACpG;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,YAAY,eAAe,OAAO;AAAA,EAChD;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,eAAe,SAAS,MAAM;AAAA,EACxD;AAAA,EACA,eAAe,eAAe;AAC5B,WAAO,KAAK,YAAY,eAAe,UAAU;AAAA,EACnD;AAAA,EACA,aAAa,eAAe;AAC1B,WAAO,KAAK,eAAe,EAAE,KAAK,UAAQ,KAAK,QAAQ,cAAc,SAAS;AAAA,EAChF;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,YAAY,WAAW,cAAc,KAAK;AAAA,EACnD;AAAA,EACA,iBAAiB,OAAO,SAAS;AAC/B,UAAM,QAAQ,MAAM,cAAc,QAAQ,2BAA2B;AACrE,WAAO,SAAS,MAAM,SAAS,OAAO;AAAA,EACxC;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,KAAK,YAAY,aAAa,KAAK,KAAK,aAAa,aAAa,EAAE,kBAAkB,EAAE,WAAW,KAAK,YAAY,kBAAkB,CAAC;AAAA,EAChJ;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,CAAC,CAAC,kBAAkB,cAAc,UAAU,KAAK,KAAK,aAAa,aAAa,MAAM,KAAK,cAAc,aAAa;AAAA,EAC/H;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,CAAC,CAAC,iBAAiB,CAAC,KAAK,eAAe,aAAa,KAAK,CAAC,cAAc;AAAA,EAClF;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,aAAa,EAAE,KAAK,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EAClF;AAAA,EACA,eAAe;AACb,WAAO,YAAY,SAAS,KAAK,aAAa,GAAG,mBAAiB,KAAK,YAAY,aAAa,CAAC;AAAA,EACnG;AAAA,EACA,sBAAsB,QAAQ;AAC5B,QAAI,aAAa;AACjB,WAAO,cAAc,WAAW,SAAS,YAAY,MAAM,MAAM;AAC/D,mBAAa,YAAY;AAAA,IAC3B;AACA,WAAO,YAAY,MAAM,KAAK,aAAa,EAAE,KAAK,mBAAiB,KAAK,YAAY,aAAa,KAAK,GAAG,KAAK,OAAO,IAAI,cAAc,GAAG,OAAO,WAAW,EAAE;AAAA,EAChK;AAAA,EACA,qBAAqB,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,YAAY,IAAI;AAClE,UAAM,iBAAiB,CAAC;AACxB,aAAS,MAAM,QAAQ,CAAC,MAAM,UAAU;AACtC,YAAM,OAAO,cAAc,KAAK,YAAY,MAAM,MAAM;AACxD,YAAM,UAAU;AAAA,QACd,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,cAAQ,OAAO,IAAI,KAAK,qBAAqB,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAChF,qBAAe,KAAK,OAAO;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,KAAK,gBAAgB,QAAQ,GAAG;AACzD,qBAAiB,kBAAkB,KAAK,eAAe;AACvD,QAAI,kBAAkB,eAAe,QAAQ;AAC3C,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,gBAAgB,eAAe,CAAC;AACtC,YAAI,KAAK,YAAY,eAAe,KAAK,MAAM,IAAK,QAAO;AAC3D,cAAM,cAAc,KAAK,2BAA2B,KAAK,cAAc,OAAO,QAAQ,CAAC;AACvF,YAAI,YAAa,QAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,gBAAgB,wBAAwB,CAAC,GAAG;AACpD,sBAAkB,eAAe,QAAQ,mBAAiB;AACxD,UAAI,KAAK,cAAc,aAAa,GAAG;AACrC,8BAAsB,KAAK,aAAa;AACxC,aAAK,UAAU,cAAc,OAAO,qBAAqB;AAAA,MAC3D;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,IACrB,IAAI;AACJ,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,KAAK,KAAK,YAAY,EAAE,QAAQ,cAAc,KAAK;AAC9F,WAAK,YAAY,IAAI,aAAa;AAClC,WAAK,aAAa;AAAA,IACpB,WAAW,kBAAkB;AAC3B,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,UAAU,WAAW,WAAW,KAAK,iBAAiB,cAAc,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAC9H,QAAI,SAAS;AACX,cAAQ,kBAAkB,QAAQ,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,YAAM,cAAc,KAAK,YAAY,MAAM,KAAK,iBAAiB,OAAO,MAAM,aAAa,IAAI,KAAK,sBAAsB,MAAM,MAAM,KAAK,KAAK,cAAc,IAAI,KAAK,aAAa;AACpL,UAAI,MAAM,kBAAkB,KAAM,MAAK,YAAY,IAAI,WAAW;AAAA,IACpE;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,SAAS,MAAM;AACrB,QAAI,KAAK,WAAW,CAAC,KAAK,GAAG,cAAc,SAAS,MAAM,GAAG;AAC3D,WAAK,UAAU;AACf,WAAK,YAAY,IAAI,IAAI;AACzB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,kBAAc,WAAW,CAAC,cAAc;AACxC,UAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,cAAc,SAAS;AAChG,gBAAY,eAAe,KAAK,aAAa;AAC7C,SAAK,eAAe,IAAI,cAAc;AACtC,SAAK,eAAe,OAAO,WAAS,MAAM,IAAI,OAAK,MAAM,gBAAgB,gBAAgB,CAAC,CAAC;AAC3F,SAAK,YAAY,IAAI,aAAa;AAAA,EACpC;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM,WAAW,MAAM;AACvC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAEH;AAAA,MACF;AACE,YAAI,CAAC,WAAW,YAAY,qBAAqB,MAAM,GAAG,GAAG;AAC3D,eAAK,YAAY,OAAO,MAAM,GAAG;AAAA,QACnC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,gBAAgB,YAAY,WAAW,KAAK,YAAY,CAAC,IAAI,KAAK,aAAa,KAAK,YAAY,CAAC,IAAI,KAAK,cAAc;AAC9H,SAAK,kBAAkB;AAAA,MACrB,eAAe;AAAA,MACf;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,gBAAgB,YAAY,WAAW,KAAK,YAAY,CAAC,IAAI,KAAK,aAAa,KAAK,YAAY,CAAC,IAAI,KAAK,aAAa;AAC7H,SAAK,kBAAkB;AAAA,MACrB,eAAe;AAAA,MACf;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,GAAG;AAC9C,YAAM,UAAU,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,KAAK,YAAY,EAAE,GAAG;AAChF,UAAI,SAAS;AACX,cAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,QAAQ,KAAK,YAAY,EAAE,GAAG;AACzF,aAAK,eAAe,IAAI,cAAc;AAAA,MACxC,OAAO;AACL,cAAM,cAAc,YAAY,WAAW,KAAK,YAAY,EAAE,MAAM,IAAI,KAAK,YAAY,EAAE,SAAS,KAAK,YAAY;AACrH,aAAK,YAAY,IAAI,WAAW;AAAA,MAClC;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,GAAG;AAC9C,YAAM,UAAU,KAAK,YAAY,KAAK,YAAY,CAAC;AACnD,UAAI,SAAS;AACX,cAAM,UAAU,KAAK,eAAe,EAAE,KAAK,OAAK,EAAE,QAAQ,KAAK,YAAY,EAAE,GAAG;AAChF,YAAI,SAAS;AACX,eAAK,eAAe,KAAK;AAAA,QAC3B,OAAO;AACL,gBAAM,iBAAiB,KAAK,eAAe,EAAE,OAAO,OAAK,EAAE,cAAc,KAAK,YAAY,EAAE,SAAS;AACrG,yBAAe,KAAK,KAAK,YAAY,CAAC;AACtC,eAAK,eAAe,IAAI,cAAc;AAAA,QACxC;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,SAAK,kBAAkB;AAAA,MACrB,eAAe;AAAA,MACf,eAAe,KAAK,cAAc;AAAA,MAClC,kBAAkB;AAAA,IACpB,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,kBAAkB;AAAA,MACrB,eAAe;AAAA,MACf,eAAe,KAAK,aAAa;AAAA,MACjC,aAAa;AAAA,MACb,kBAAkB;AAAA,IACpB,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,GAAG;AAC9C,YAAM,UAAU,WAAW,WAAW,KAAK,iBAAiB,cAAc,eAAe,UAAU,GAAG,KAAK,aAAa,EAAE,IAAI;AAC9H,YAAM,gBAAgB,YAAY,WAAW,WAAW,SAAS,4BAA4B,KAAK,WAAW,WAAW,SAAS,UAAU;AAC3I,sBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AAAA,IACnE;AACA,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,aAAa,eAAe;AAC1B,UAAM,QAAQ,KAAK,aAAa,EAAE,UAAU,UAAQ,KAAK,QAAQ,cAAc,GAAG;AAClF,UAAM,cAAc,QAAQ,KAAK,aAAa,EAAE,SAAS,IAAI,KAAK,aAAa,EAAE,MAAM,QAAQ,CAAC,EAAE,KAAK,WAAS,KAAK,YAAY,KAAK,CAAC,IAAI;AAC3I,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,aAAa,eAAe;AAC1B,UAAM,QAAQ,KAAK,aAAa,EAAE,UAAU,UAAQ,KAAK,QAAQ,cAAc,GAAG;AAClF,UAAM,cAAc,QAAQ,IAAI,YAAY,SAAS,KAAK,aAAa,EAAE,MAAM,GAAG,KAAK,GAAG,WAAS,KAAK,YAAY,KAAK,CAAC,IAAI;AAC9H,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,eAAe,KAAK,eAAe,MAAM;AAC9C,QAAI,cAAc;AAClB,QAAI,UAAU;AACd,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,GAAG;AAC9C,YAAM,mBAAmB,KAAK,aAAa,EAAE,UAAU,mBAAiB,cAAc,QAAQ,KAAK,YAAY,EAAE,GAAG;AACpH,oBAAc,KAAK,aAAa,EAAE,MAAM,gBAAgB,EAAE,KAAK,mBAAiB,KAAK,cAAc,aAAa,CAAC;AACjH,oBAAc,YAAY,QAAQ,WAAW,IAAI,KAAK,aAAa,EAAE,MAAM,GAAG,gBAAgB,EAAE,KAAK,mBAAiB,KAAK,cAAc,aAAa,CAAC,IAAI;AAAA,IAC7J,OAAO;AACL,oBAAc,KAAK,aAAa,EAAE,KAAK,mBAAiB,KAAK,cAAc,aAAa,CAAC;AAAA,IAC3F;AACA,QAAI,YAAY,WAAW,WAAW,GAAG;AACvC,gBAAU;AAAA,IACZ;AACA,QAAI,YAAY,QAAQ,WAAW,KAAK,YAAY,QAAQ,KAAK,YAAY,CAAC,GAAG;AAC/E,oBAAc,KAAK,cAAc;AAAA,IACnC;AACA,QAAI,YAAY,WAAW,WAAW,GAAG;AACvC,WAAK,kBAAkB;AAAA,QACrB,eAAe;AAAA,QACf,eAAe;AAAA,QACf,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAAA,IACjC;AACA,SAAK,gBAAgB,WAAW,MAAM;AACpC,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,IACvB,GAAG,GAAG;AACN,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAkB,kBAAqB,UAAU,CAAC;AAAA,EACrE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,SAAS,oBAAoB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,MACd,gBAAgB,CAAI,WAAa,4BAA4B,kBAAkB,kBAAkB,gBAAgB;AAAA,MACjH,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MAC/F,mBAAmB;AAAA,MACnB,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,gBAAgB;AAAA,MACnF,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,MAC9F,YAAY;AAAA,IACd;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IAC/D,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,cAAc,WAAW,aAAa,YAAY,QAAQ,MAAM,WAAW,YAAY,gBAAgB,iBAAiB,kBAAkB,qBAAqB,SAAS,gBAAgB,CAAC;AAAA,IACvN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,kBAAkB,GAAG,CAAC;AAC3C,QAAG,WAAW,cAAc,SAAS,4DAA4D,QAAQ;AACvG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,QAChD,CAAC,EAAE,WAAW,SAAS,yDAAyD,QAAQ;AACtF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,aAAa,SAAS,2DAA2D,QAAQ;AAC1F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,QAC3C,CAAC,EAAE,YAAY,SAAS,0DAA0D,QAAQ;AACxF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,OAAO,MAAM,CAAC;AAAA,QAC1C,CAAC;AACD,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,IAAI,EAAE,MAAM,IAAI,UAAU,OAAO,EAAE,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,gBAAgB,IAAI,YAAY,EAAE,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,MAAS,EAAE,kBAAkB,IAAI,eAAe,CAAC,EAAE,qBAAqB,IAAI,iBAAiB,EAAE,SAAS,IAAI,eAAe,CAAC,EAAE,kBAAkB,IAAI,cAAc;AAAA,MACpW;AAAA,IACF;AAAA,IACA,cAAc,CAAC,YAAY;AAAA,IAC3B,QAAQ,CAAC,8uBAA8uB;AAAA,IACvvB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,8uBAA8uB;AAAA,IACzvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,OAAO,IAAI;AAAA,EACxB,WAAW;AACT,SAAK,KAAK,KAAK,MAAM,kBAAkB;AAAA,EACzC;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,sBAAsB,KAAK;AAChC;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF;AACE,eAAK,eAAe,KAAK;AACzB;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,aAAS,QAAQ,KAAK,OAAO;AAC3B,UAAI,KAAK,UAAU;AACjB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AACA,SAAK,GAAG,cAAc;AAAA,EACxB;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AACjB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,iBAAiB,OAAO,MAAM,OAAO,aAAa,OAAO;AACvD,QAAI,CAAC,KAAK,eAAe,IAAI,GAAG;AAC9B,YAAM,aAAa,aAAa,OAAO,KAAK,cAAc,YAAY,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO;AAC7G,WAAK,WAAW,IAAI,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,WAAW;AAAA,MACrB,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,kBAAkB,KAAK,YAAY,KAAK,oBAAoB;AAAA,QAC5D,QAAQ;AAAA,MACV;AAAA,IACF,IAAI;AAAA,MACF,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,kBAAkB,KAAK;AAAA,QACvB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,MAAM,MAAM;AACtB,WAAO,OAAO,YAAY,aAAa,KAAK,IAAI,CAAC,IAAI;AAAA,EACvD;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,YAAY,MAAM,OAAO;AAAA,EACvC;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,MAAM;AAClB,WAAO,KAAK,YAAY,MAAM,SAAS,MAAM;AAAA,EAC/C;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,KAAK,YAAY,MAAM,UAAU;AAAA,EAC1C;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,YAAY,WAAW,KAAK,KAAK;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO,MAAM;AACtB,WAAO,QAAQ,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK;AAAA,EACxD;AAAA,EACA,YAAY,MAAM,OAAO;AACvB,WAAO,KAAK,KAAK,KAAK,KAAK,YAAY,GAAG,KAAK,WAAW,KAAK,CAAC;AAAA,EAClE;AAAA,EACA,aAAa,MAAM,OAAO;AACxB,WAAO,KAAK,KAAK,KAAK,KAAK,aAAa,GAAG,KAAK,WAAW,KAAK,CAAC;AAAA,EACnE;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,cAAc,cAAc,QAAQ,2BAA2B;AACpF,UAAM,SAAS,YAAY,WAAW,WAAW,cAAc,4BAA4B,IAAI,cAAc,KAAK,eAAe,YAAY,IAAI,KAAK,eAAe,YAAY;AACjL,aAAS,KAAK,oBAAoB,eAAe,MAAM,IAAI,cAAc,KAAK,gBAAgB,aAAa,IAAI,KAAK,eAAe,aAAa;AAAA,EAClJ;AAAA,EACA,oBAAoB,OAAO,SAAS;AAClC,eAAW,WAAW,MAAM,OAAO;AAAA,EACrC;AAAA,EACA,eAAe,cAAc,YAAY,OAAO;AAC9C,UAAM,mBAAmB,YAAY,eAAe,aAAa;AACjE,UAAM,gBAAgB,WAAW,WAAW,kBAAkB,4BAA4B;AAC1F,WAAO,gBAAgB,WAAW,aAAa,eAAe,iBAAiB,IAAI,KAAK,eAAe,cAAc,aAAa,IAAI,gBAAgB;AAAA,EACxJ;AAAA,EACA,eAAe,cAAc,YAAY,OAAO;AAC9C,UAAM,mBAAmB,YAAY,eAAe,aAAa;AACjE,UAAM,gBAAgB,WAAW,WAAW,kBAAkB,4BAA4B;AAC1F,WAAO,gBAAgB,WAAW,aAAa,eAAe,iBAAiB,IAAI,KAAK,eAAe,cAAc,aAAa,IAAI,gBAAgB;AAAA,EACxJ;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,eAAe,KAAK,mBAAmB,cAAc,mBAAmB,IAAI;AAAA,EAC1F;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,eAAe,KAAK,mBAAmB,cAAc,kBAAkB,IAAI;AAAA,EACzF;AAAA,EACA,cAAc,OAAO,MAAM,OAAO;AAChC,QAAI,KAAK,eAAe,IAAI,GAAG;AAC7B,YAAM,eAAe;AACrB;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ;AAAA,QACX,eAAe;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,eAAS,aAAa,KAAK,OAAO;AAChC,YAAI,SAAS,aAAa,UAAU,UAAU;AAC5C,oBAAU,WAAW;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,WAAW,CAAC,KAAK;AACtB,SAAK,iBAAiB,OAAO,MAAM,KAAK;AACxC,SAAK,YAAY;AACjB,eAAW,MAAM,MAAM,aAAa;AAAA,EACtC;AAAA,EACA,gBAAgB,OAAO,MAAM,OAAO;AAClC,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,qBAAqB,KAAK;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB,KAAK;AAC7B;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB,KAAK;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,iBAAiB,OAAO,MAAM,KAAK;AACxC;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,qBAAqB,OAAO;AAC1B,UAAM,WAAW,WAAW,aAAa,MAAM,eAAe,kBAAkB,MAAM,OAAO,WAAW,WAAW,MAAM,cAAc,oBAAoB,0BAA0B,IAAI;AACzL,eAAW,WAAW,MAAM,QAAQ,IAAI,KAAK,oBAAoB;AAAA,MAC/D,eAAe;AAAA,MACf,aAAa;AAAA,IACf,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,mBAAmB,OAAO;AACxB,UAAM,aAAa,KAAK,eAAe,MAAM,cAAc,aAAa,KAAK,KAAK,eAAe;AACjG,UAAM,WAAW,WAAW,aAAa,YAAY,kBAAkB,MAAM,OAAO,WAAW,WAAW,WAAW,oBAAoB,0BAA0B,IAAI;AACvK,eAAW,WAAW,MAAM,QAAQ,IAAI,KAAK,oBAAoB;AAAA,MAC/D,eAAe;AAAA,MACf,aAAa;AAAA,IACf,CAAC;AACD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,oBAAoB,OAAO,KAAK,gBAAgB,CAAC;AACtD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,oBAAoB,OAAO,KAAK,eAAe,CAAC;AACrD,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,iBAAiB,OAAO,MAAM,OAAO;AACnC,UAAM,eAAe,WAAW,WAAW,MAAM,eAAe,kCAAkC;AAClG,mBAAe,aAAa,MAAM,IAAI,KAAK,cAAc,OAAO,MAAM,KAAK;AAC3E,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,GAAG;AAC1C,WAAO,KAAK,KAAK,YAAc,kBAAqB,iBAAiB,CAAC;AAAA,EACxE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MAC/F,mBAAmB;AAAA,MACnB,IAAI;AAAA,MACJ,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,IAChG;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,SAAS,qBAAqB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG,WAAW,SAAS,GAAG,CAAC,QAAQ,UAAU,GAAG,SAAS,WAAW,WAAW,WAAW,YAAY,YAAY,gBAAgB,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,6BAA6B,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,wBAAwB,QAAQ,UAAU,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,6BAA6B,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,6BAA6B,GAAG,QAAQ,GAAG,CAAC,SAAS,mBAAmB,GAAG,WAAW,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,oBAAoB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,GAAG,cAAc,eAAe,oBAAoB,2BAA2B,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,wBAAwB,GAAG,SAAS,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,eAAe,WAAW,SAAS,gBAAgB,qBAAqB,QAAQ,cAAc,YAAY,gBAAgB,CAAC;AAAA,IACtrD,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,gBAAgB,CAAC;AAC3E,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,KAAK,EAAE,WAAW,yBAAyB;AACxE,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,YAAe,kBAAqB,SAAS,iBAAiB,kBAAkB,aAAa;AAAA,IACvL,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,YAAY,CAAC,MAAM,UAAU,MAAM;AAAA,QACrD,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACjH;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4GV,YAAY,CAAC,QAAQ,YAAY,CAAC,MAAM,UAAU,MAAM;AAAA,QACtD,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MAC/G,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,8uBAA8uB;AAAA,IACzvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,WAAW,cAAc,aAAa;AAAA,IACrD,SAAS,CAAC,cAAc,cAAc,eAAe,cAAc,eAAe,gBAAgB,iBAAiB,gBAAgB;AAAA,IACnI,SAAS,CAAC,WAAW,cAAc,eAAe,YAAY;AAAA,EAChE,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,eAAe,cAAc,eAAe,gBAAgB,iBAAiB,kBAAkB,cAAc,eAAe,YAAY;AAAA,EAChL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,eAAe,cAAc,eAAe,gBAAgB,iBAAiB,gBAAgB;AAAA,MACnI,SAAS,CAAC,WAAW,cAAc,eAAe,YAAY;AAAA,MAC9D,cAAc,CAAC,WAAW,cAAc,aAAa;AAAA,IACvD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}