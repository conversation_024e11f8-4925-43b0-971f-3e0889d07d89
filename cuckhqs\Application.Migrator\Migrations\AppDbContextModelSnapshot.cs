﻿// <auto-generated />
using System;
using Application.Infrastructure.Configurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Application.Migrator.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Application.Infrastructure.Entities.AcademicRankEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AcademicRankCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AcademicRankCode");

                    b.Property<string>("AcademicRankName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AcademicRankName");

                    b.Property<string>("AcademicRankShortName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AcademicRankShortName");

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("AcademicRank", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.AdvertisementEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("AdvertisementCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AdvertisementCode");

                    b.Property<string>("AdvertisementName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AdvertisementName");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("Ends")
                        .HasColumnType("datetime2")
                        .HasColumnName("Ends");

                    b.Property<int?>("EndsHour")
                        .HasColumnType("int")
                        .HasColumnName("EndsHour");

                    b.Property<int?>("EndsMinute")
                        .HasColumnType("int")
                        .HasColumnName("EndsMinute");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<DateTime?>("Start")
                        .HasColumnType("datetime2")
                        .HasColumnName("Start");

                    b.Property<int?>("StartHour")
                        .HasColumnType("int")
                        .HasColumnName("StartHour");

                    b.Property<int?>("StartMinute")
                        .HasColumnType("int")
                        .HasColumnName("StartMinute");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("Advertisement", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.CountryEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CountryCode");

                    b.Property<string>("CountryName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CountryName");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("LanguageName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LanguageName");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<string>("VietnameseName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("VietnameseName");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("Country", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.DecisionLevelEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("DecisionLevelCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DecisionLevelCode");

                    b.Property<string>("DecisionLevelName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DecisionLevelName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("DecisionLevel", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.DegreeEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("DegreeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DegreeCode");

                    b.Property<string>("DegreeName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DegreeName");

                    b.Property<string>("DegreeShortName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DegreeShortName");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("Degree", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.DisciplineTypeEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("DisciplineTypeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DisciplineTypeCode");

                    b.Property<string>("DisciplineTypeName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DisciplineTypeName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("DisciplineType", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.DistrictEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("DistrictCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DistrictCode");

                    b.Property<string>("DistrictName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DistrictName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("int")
                        .HasColumnName("ProvinceId");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("District", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.EducationLevelEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("EducationLevelCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EducationLevelCode");

                    b.Property<string>("EducationLevelName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EducationLevelName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("EducationLevel", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.Employee.EmployeeCEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("Id");

                    b.Property<int?>("AcademicRankId")
                        .HasColumnType("int")
                        .HasColumnName("AcademicRankId");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AccountNumber");

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<bool?>("ActiveAccount")
                        .HasColumnType("bit")
                        .HasColumnName("ActiveAccount");

                    b.Property<string>("Avatar")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Avatar");

                    b.Property<string>("Bank")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Bank");

                    b.Property<DateTime?>("BirthDay")
                        .HasColumnType("datetime2")
                        .HasColumnName("BirthDay");

                    b.Property<string>("BirthPlace")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BirthPlace");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("DateBy")
                        .HasColumnType("datetime2")
                        .HasColumnName("DateBy");

                    b.Property<int?>("DegreeId")
                        .HasColumnType("int")
                        .HasColumnName("DegreeId");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Email");

                    b.Property<string>("EmployeeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EmployeeCode");

                    b.Property<string>("Fax")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Fax");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FirstName");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Fullname");

                    b.Property<int?>("Gender")
                        .HasColumnType("int")
                        .HasColumnName("Gender");

                    b.Property<string>("HomeAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("HomeAddress");

                    b.Property<string>("HomeLand")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("HomeLand");

                    b.Property<string>("HomeTel")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("HomeTel");

                    b.Property<string>("IDNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDNumber");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<bool?>("IsAdministrator")
                        .HasColumnType("bit")
                        .HasColumnName("IsAdministrator");

                    b.Property<string>("IssuedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IssuedBy");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LastName");

                    b.Property<string>("LongFullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("LongFullName");

                    b.Property<string>("Mobile")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Mobile");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("NativeAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NativeAddress");

                    b.Property<string>("OfficeAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OfficeAddress");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<bool?>("Owned")
                        .HasColumnType("bit")
                        .HasColumnName("Owned");

                    b.Property<int?>("PartyPositionId")
                        .HasColumnType("int")
                        .HasColumnName("PartyPositionId");

                    b.Property<int?>("PositionId")
                        .HasColumnType("int")
                        .HasColumnName("PositionId");

                    b.Property<short?>("PositionType")
                        .HasColumnType("smallint")
                        .HasColumnName("PositionType");

                    b.Property<int?>("RankId")
                        .HasColumnType("int")
                        .HasColumnName("RankId");

                    b.Property<string>("ShortName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<string>("Tel")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Tel");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Website");

                    b.Property<int?>("YearOfAcademicRank")
                        .HasColumnType("int")
                        .HasColumnName("YearOfAcademicRank");

                    b.Property<int?>("YearOfDegree")
                        .HasColumnType("int")
                        .HasColumnName("YearOfDegree");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationUnitId");

                    b.ToTable("Employee", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.EmployeeEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("Id");

                    b.Property<int?>("AcademicRankId")
                        .HasColumnType("int")
                        .HasColumnName("AcademicRankId");

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<bool?>("ActiveAccount")
                        .HasColumnType("bit")
                        .HasColumnName("ActiveAccount");

                    b.Property<DateTime?>("BirthDay")
                        .HasColumnType("datetime2")
                        .HasColumnName("BirthDay");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<int?>("DegreeId")
                        .HasColumnType("int")
                        .HasColumnName("DegreeId");

                    b.Property<string>("EmployeeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EmployeeCode");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Fullname");

                    b.Property<int?>("Gender")
                        .HasColumnType("int")
                        .HasColumnName("Gender");

                    b.Property<bool?>("IsAdministrator")
                        .HasColumnType("bit");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<string>("OrganizationUnitName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OrganizationUnitName");

                    b.Property<bool?>("Owned")
                        .HasColumnType("bit")
                        .HasColumnName("Owned");

                    b.Property<int?>("PartyPositionId")
                        .HasColumnType("int")
                        .HasColumnName("PartyPositionId");

                    b.Property<int?>("PositionId")
                        .HasColumnType("int")
                        .HasColumnName("PositionId");

                    b.Property<string>("PositionName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PositionName");

                    b.Property<short?>("PositionType")
                        .HasColumnType("smallint")
                        .HasColumnName("PositionType");

                    b.Property<int?>("RankId")
                        .HasColumnType("int")
                        .HasColumnName("RankId");

                    b.Property<string>("RankName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RankName");

                    b.Property<string>("ShortName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("YearOfAcademicRank")
                        .HasColumnType("int")
                        .HasColumnName("YearOfAcademicRank");

                    b.Property<int?>("YearOfDegree")
                        .HasColumnType("int")
                        .HasColumnName("YearOfDegree");

                    b.HasKey("Id");

                    b.ToTable((string)null);

                    b.ToView("Employee", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.JournalEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ISSN")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ISSN");

                    b.Property<string>("JournalCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalCode");

                    b.Property<string>("JournalGroupCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalGroupCode");

                    b.Property<int?>("JournalGroupId")
                        .HasColumnType("int")
                        .HasColumnName("JournalGroupId");

                    b.Property<string>("JournalGroupId_AN")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalGroupId_AN");

                    b.Property<string>("JournalName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalName");

                    b.Property<string>("JournalSpecialCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalSpecialCode");

                    b.Property<string>("JournalTypeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalTypeCode");

                    b.Property<int?>("JournalTypeId")
                        .HasColumnType("int")
                        .HasColumnName("JournalTypeId");

                    b.Property<string>("JournalTypeId_AN")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalTypeId_AN");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<decimal?>("PointFrom")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("PointFrom");

                    b.Property<decimal?>("PointTo")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("PointTo");

                    b.Property<string>("PublishingAgency")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PublishingAgency");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("Journal", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.JournalGroupEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("JournalGroupCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalGroupCode");

                    b.Property<string>("JournalGroupName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalGroupName");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("JournalGroup", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.JournalTypeEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("JournalTypeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalTypeCode");

                    b.Property<string>("JournalTypeName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JournalTypeName");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("JournalType", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.OnDutyCommandEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<Guid?>("EmployeeId_H")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EmployeeId_H");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Week")
                        .HasColumnType("int")
                        .HasColumnName("Week");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId_H");

                    b.ToTable("OnDutyComand", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.OrganizationUnit.OrganizationUnitEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AccountNumber");

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Address");

                    b.Property<string>("BankName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BankName");

                    b.Property<short?>("Classify")
                        .HasColumnType("smallint")
                        .HasColumnName("Classify");

                    b.Property<int?>("ClassifyGroup")
                        .HasColumnType("int")
                        .HasColumnName("ClassifyGroup");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("Director")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Director");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Email");

                    b.Property<string>("Fax")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Fax");

                    b.Property<string>("FullOrganizationUnitName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FullOrganizationUnitName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("bit")
                        .HasColumnName("IsRoot");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OrganizationUnitName");

                    b.Property<string>("OrganizationUnitCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OrganizationUnitCode");

                    b.Property<string>("ParentCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ParentCode");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("ParentId");

                    b.Property<string>("ShortOrganizationUnitName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ShortOrganizationUnitName");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<string>("Tel")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Tel");

                    b.Property<string>("TrainingMaterialCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TrainingMaterialCode");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Website");

                    b.HasKey("Id");

                    b.ToTable("OrganizationUnit", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.PositionEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<short?>("Classify")
                        .HasColumnType("smallint")
                        .HasColumnName("Classify");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("FullPositionName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FullPositionName");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<bool?>("IsRoot")
                        .HasColumnType("bit")
                        .HasColumnName("IsRoot");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("ParentCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ParentCode");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("ParentId");

                    b.Property<string>("PositionCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PositionCode");

                    b.Property<string>("PositionName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PositionName");

                    b.Property<string>("ShortPositionName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ShortPositionName");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("Position", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.ProvinceEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("ProvinceCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ProvinceCode");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ProvinceName");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("Province", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.RankEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("RankCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RankCode");

                    b.Property<string>("RankName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RankName");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.HasKey("Id");

                    b.ToTable("Rank", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.RewardTypeEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("RewardTypeCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RewardTypeCode");

                    b.Property<string>("RewardTypeName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RewardTypeName");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("RewardType", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.SoSEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<bool?>("IsRoot")
                        .HasColumnType("bit")
                        .HasColumnName("IsRoot");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("ParentId");

                    b.Property<string>("SoSCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SoSCode");

                    b.Property<string>("SoSName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SoSName");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("SoS", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.User.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PasswordLastChanged")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WardEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("int")
                        .HasColumnName("DistrictId");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("int")
                        .HasColumnName("ProvinceId");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<string>("WardCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("WardCode");

                    b.Property<string>("WardName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("WardName");

                    b.HasKey("Id");

                    b.ToTable("Ward", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingResultEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Class")
                        .HasColumnType("int")
                        .HasColumnName("Class");

                    b.Property<string>("Contents")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents");

                    b.Property<string>("Contents1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents1");

                    b.Property<string>("Contents2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents2");

                    b.Property<string>("Contents3")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents3");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateFrom")
                        .HasColumnType("datetime2")
                        .HasColumnName("DateFrom");

                    b.Property<DateTime?>("DateTo")
                        .HasColumnType("datetime2")
                        .HasColumnName("DateTo");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Note");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasColumnName("Status");

                    b.Property<int?>("Week")
                        .HasColumnType("int")
                        .HasColumnName("Week");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("WorkingResult", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingResultSyntheticEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Announced")
                        .HasColumnType("int")
                        .HasColumnName("Announced");

                    b.Property<string>("Contents")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("Week")
                        .HasColumnType("int")
                        .HasColumnName("Week");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("WorkingResultSynthetic", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleCEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EmployeeId");

                    b.Property<int>("WorkingScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleId");

                    b.ToTable("WorkingScheduleC", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleEPEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EmployeeId");

                    b.Property<int>("WorkingScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleId");

                    b.ToTable("WorkingScheduleEP", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleEPHEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EmployeeId");

                    b.Property<int>("WorkingScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleId");

                    b.ToTable("WorkingScheduleEPH", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleIssueDetailEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<Guid>("EmployeeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EmployeeId");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("WorkingScheduleIssueId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleIssueId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleIssueId");

                    b.ToTable("WorkingScheduleIssueDetail", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleIssueEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Announced")
                        .HasColumnType("int")
                        .HasColumnName("Announced");

                    b.Property<string>("Command")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Command");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("Number")
                        .HasColumnType("int")
                        .HasColumnName("Number");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<Guid?>("PersonSigning")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("PersonSigning");

                    b.Property<string>("PersonSigningOther")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PersonSigningOther");

                    b.Property<string>("Place")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Place");

                    b.Property<string>("Sign")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Sign");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<string>("UnitPositionSigning")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UnitPositionSigning");

                    b.Property<int?>("Week")
                        .HasColumnType("int")
                        .HasColumnName("Week");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("WorkingScheduleIssue", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleOUEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<int>("WorkingScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleId");

                    b.ToTable("WorkingScheduleOU", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleResultEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("Result")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Result");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("WorkingScheduleId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleId");

                    b.ToTable("WorkingScheduleResult", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleResult_AttachDetailEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Contents")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileName");

                    b.Property<double?>("FileSize")
                        .HasColumnType("float")
                        .HasColumnName("FileSize");

                    b.Property<string>("FileType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileType");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<bool?>("Inactive")
                        .HasColumnType("bit")
                        .HasColumnName("Inactive");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("SortOrder");

                    b.Property<int?>("WorkingScheduleResultId")
                        .HasColumnType("int")
                        .HasColumnName("WorkingScheduleResultId");

                    b.HasKey("Id");

                    b.HasIndex("WorkingScheduleResultId");

                    b.ToTable("WorkingScheduleResult_AttachDetail", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Models.DepartmentEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("dia_chi");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ma_don_vi");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int")
                        .HasColumnName("id_nguoi_tao");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("DepartmentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("loai_don_vi");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mo_ta");

                    b.Property<string>("IsActive")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("hieu_luc");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ten_don_vi");

                    b.Property<string>("NameShort")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("viet_tat");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int")
                        .HasColumnName("id_don_vi_cha");

                    b.Property<string>("ParentIdCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ma_don_vi_cha");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("dien_thoai");

                    b.Property<DateTime?>("ScheduledDeleteDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sap_xep");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("trang_thai");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int")
                        .HasColumnName("id_nguoi_cap_nhap");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("lan_cap_nhap_cuoi");

                    b.HasKey("Id");

                    b.ToTable("dm_don_vi_btm", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Models.WorkingScheduleEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("Active");

                    b.Property<int?>("Announced")
                        .HasColumnType("int")
                        .HasColumnName("Announced");

                    b.Property<short?>("Classify")
                        .HasColumnType("smallint")
                        .HasColumnName("Classify");

                    b.Property<string>("CoChair")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CoChair");

                    b.Property<string>("Contents")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Contents");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CreatedBy");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedDate");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("Date");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IPAddress");

                    b.Property<string>("Member")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Member");

                    b.Property<bool?>("Message")
                        .HasColumnType("bit")
                        .HasColumnName("Message");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ModifiedBy");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ModifiedDate");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Note");

                    b.Property<int?>("OrganizationUnitId")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId");

                    b.Property<int?>("OrganizationUnitId_Chair")
                        .HasColumnType("int")
                        .HasColumnName("OrganizationUnitId_Chair");

                    b.Property<string>("Place")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Place");

                    b.Property<bool?>("Register")
                        .HasColumnType("bit")
                        .HasColumnName("Register");

                    b.Property<short?>("SortOrder")
                        .HasColumnType("smallint")
                        .HasColumnName("SortOrder");

                    b.Property<short?>("Time")
                        .HasColumnType("smallint")
                        .HasColumnName("Time");

                    b.Property<DateTime?>("TimeFrom")
                        .HasColumnType("datetime2")
                        .HasColumnName("TimeFrom");

                    b.Property<DateTime?>("TimeTo")
                        .HasColumnType("datetime2")
                        .HasColumnName("TimeTo");

                    b.Property<int?>("Week")
                        .HasColumnType("int")
                        .HasColumnName("Week");

                    b.Property<int?>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("Id");

                    b.ToTable("WorkingSchedule", (string)null);
                });

            modelBuilder.Entity("Fido2Identity.FidoStoredCredential", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("AaGuid")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CredType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DescriptorJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("PublicKey")
                        .HasColumnType("varbinary(max)");

                    b.Property<DateTime>("RegDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("SignatureCounter")
                        .HasColumnType("bigint");

                    b.Property<byte[]>("UserHandle")
                        .HasColumnType("varbinary(max)");

                    b.Property<byte[]>("UserId")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FidoStoredCredential");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.Employee.EmployeeCEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.OrganizationUnit.OrganizationUnitEntity", null)
                        .WithMany()
                        .HasForeignKey("OrganizationUnitId");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.OnDutyCommandEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.Employee.EmployeeCEntity", "Employee")
                        .WithMany("OnDutyCommands")
                        .HasForeignKey("EmployeeId_H");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleCEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Models.WorkingScheduleEntity", "WorkingSchedule")
                        .WithMany("WorkingScheduleC")
                        .HasForeignKey("WorkingScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkingSchedule");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleEPEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Models.WorkingScheduleEntity", "WorkingSchedule")
                        .WithMany("WorkingScheduleEP")
                        .HasForeignKey("WorkingScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkingSchedule");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleEPHEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Models.WorkingScheduleEntity", "WorkingSchedule")
                        .WithMany("WorkingScheduleEPH")
                        .HasForeignKey("WorkingScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkingSchedule");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleIssueDetailEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.WorkingScheduleIssueEntity", "WorkingScheduleAnnounced")
                        .WithMany("WorkingScheduleIssueDetail")
                        .HasForeignKey("WorkingScheduleIssueId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("WorkingScheduleAnnounced");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleOUEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Models.WorkingScheduleEntity", "WorkingSchedule")
                        .WithMany("WorkingScheduleOU")
                        .HasForeignKey("WorkingScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkingSchedule");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleResultEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Models.WorkingScheduleEntity", "WorkingSchedule")
                        .WithMany("WorkingScheduleResult")
                        .HasForeignKey("WorkingScheduleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("WorkingSchedule");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleResult_AttachDetailEntity", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.WorkingScheduleResultEntity", "WorkingScheduleResult")
                        .WithMany("WorkingScheduleResult_AttachDetail")
                        .HasForeignKey("WorkingScheduleResultId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("WorkingScheduleResult");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.User.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.User.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Application.Infrastructure.Entities.User.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Application.Infrastructure.Entities.User.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.Employee.EmployeeCEntity", b =>
                {
                    b.Navigation("OnDutyCommands");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleIssueEntity", b =>
                {
                    b.Navigation("WorkingScheduleIssueDetail");
                });

            modelBuilder.Entity("Application.Infrastructure.Entities.WorkingScheduleResultEntity", b =>
                {
                    b.Navigation("WorkingScheduleResult_AttachDetail");
                });

            modelBuilder.Entity("Application.Infrastructure.Models.WorkingScheduleEntity", b =>
                {
                    b.Navigation("WorkingScheduleC");

                    b.Navigation("WorkingScheduleEP");

                    b.Navigation("WorkingScheduleEPH");

                    b.Navigation("WorkingScheduleOU");

                    b.Navigation("WorkingScheduleResult");
                });
#pragma warning restore 612, 618
        }
    }
}
