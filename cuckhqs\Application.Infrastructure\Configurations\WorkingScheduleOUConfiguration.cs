﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class WorkingScheduleOUConfiguration : IEntityTypeConfiguration<WorkingScheduleOUEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleOUEntity> builder)
        {
            builder.ToTable("WorkingScheduleOU");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
         //   builder.Property(x => x.OrganizationUnitName).HasColumnName("OrganizationUnitName");
            builder.Property(x => x.WorkingScheduleId).HasColumnName("WorkingScheduleId");
        }
    }
}
