﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Journal;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IJournalService
    {
        Task<BaseSearchResponse<JournalResponse>> SearchJournalAsync(SearchJournalRequest request);
        Task<JournalResponse> CreateJournalAsync(CreateJournalRequest request);
        Task<bool> UpdateJournalAsync(UpdateJournalRequest request);
        Task<bool> DeleteJournalAsync(DeleteJournalRequest request);
    }
}
