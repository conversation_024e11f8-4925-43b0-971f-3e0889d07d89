﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.Rank;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.JournalType
{
    public class UpdateJournalTypeRequest : CreateJournalTypeRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateJournalTypeRequest, JournalTypeEntity>> Expression
        {
            get
            {
                return entity => new JournalTypeEntity
                {
                    Id = entity.Id,
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeName = entity.JournalTypeName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static JournalTypeEntity Create(UpdateJournalTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
