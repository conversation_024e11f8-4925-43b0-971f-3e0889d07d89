﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <SatelliteResourceLanguages>vn</SatelliteResourceLanguages>
    <SatelliteResourceLanguages>fr</SatelliteResourceLanguages>
    <UseRelativePathsInPublishedAssetsFile>true</UseRelativePathsInPublishedAssetsFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspose.Words" Version="25.4.0" />
    <PackageReference Include="BoldReports.Net.Core" Version="6.2.39" />
    <PackageReference Include="ClosedXML" Version="0.104.1" />
    <PackageReference Include="EPPlus" Version="7.5.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.7.0" />
    <PackageReference Include="OpenIddict.Server" Version="6.3.0" />
    <PackageReference Include="OpenIddict.Server.AspNetCore" Version="6.3.0" />
    <PackageReference Include="OpenIddict.Validation.AspNetCore" Version="6.1.1" />
    <PackageReference Include="OpenIddict.Validation.SystemNetHttp" Version="6.1.1" />

    <PackageReference Include="NetEscapades.AspNetCore.SecurityHeaders" Version="1.0.0-preview.3" />

    <PackageReference Include="QuestPDF" Version="2025.4.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />

    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />

    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Server" Version="9.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Application.Infrastructure\Application.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\Resources\Mau_bao_cao_tuan.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\Templates\" />
    <Folder Include="wwwroot\Uploads\" />
  </ItemGroup>

</Project>
