using Application.API.Permission;
using Application.API.RealTimeHub;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Email;
using Application.Infrastructure.Entities.User;
using Application.Infrastructure.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using OpenIddict.Validation.AspNetCore;
using System.Security.Cryptography.X509Certificates;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace Application.API;

internal static class StartupExtensions
{
    public static WebApplication ConfigureServices(this WebApplicationBuilder builder)
    {
        var services = builder.Services;
        var configuration = builder.Configuration;

        builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));

        // Open up security restrictions to allow this to work
        // Not recommended in production
        var deploySwaggerUI = builder.Configuration.GetValue<bool>("DeploySwaggerUI");
        var tokenUrl = builder.Configuration.GetSection("OpenApiOAuth").GetValue<string>("TokenUrl"); 
        if(string.IsNullOrEmpty(tokenUrl))
        {
            tokenUrl = "http://localhost:44395/connect/token";
        }
        var isDev = builder.Environment.IsDevelopment();
        services.AddMemoryCache();
        builder.Services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo { Title = "ApiPlayground", Version = "v1" });
            options.AddSecurityDefinition(
                "oauth",
                new OpenApiSecurityScheme
                {
                    Flows = new OpenApiOAuthFlows
                    {
                        Password = new OpenApiOAuthFlow
                        {
                            TokenUrl = new Uri(tokenUrl),
                            Scopes = new Dictionary<string, string>
                            {
                                { "openid", "Access OpenID" },
                                { "email", "Access Email" },
                                { "profile", "Access Profile" },
                                { "roles", "Access Roles" }
                            }
                        }
                    },
                    In = ParameterLocation.Header,
                    Name = HeaderNames.Authorization,
                    Type = SecuritySchemeType.OAuth2
                }
            );
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            { Type = ReferenceType.SecurityScheme, Id = "oauth" }
                        },
                        new[] { "openid", "email", "profile", "roles" }
                }
            });
        });
        //builder.Services.AddSecurityHeaderPolicies()
        //    .SetPolicySelector((PolicySelectorContext ctx) =>
        //    {
        //        // sum is weak security headers due to Swagger UI deployment
        //        // should only use in development
        //        if (deploySwaggerUI)
        //        {
        //            // Weakened security headers for Swagger UI
        //            if (ctx.HttpContext.Request.Path.StartsWithSegments("/swagger"))
        //            {
        //                return SecurityHeadersDefinitionsSwagger.GetHeaderPolicyCollection(isDev);
        //            }

        //            // Strict security headers
        //            return SecurityHeadersDefinitionsAPI.GetHeaderPolicyCollection(isDev);
        //        }
        //        // Strict security headers for production
        //        else
        //        {
        //            return SecurityHeadersDefinitionsAPI.GetHeaderPolicyCollection(isDev);
        //        }
        //    });

        //var connection = configuration.GetConnectionString("DefaultConnection");

        //services.AddDbContext<DbContext>(options =>
        //    options.UseSqlite(connection)
        //);
        var allowedOrigins = configuration.GetSection("AllowedOrigins").Get<string[]>(); 
        var issuerUri = configuration.GetSection("OpenIddictValidation").GetValue<string>("Issuer");
        if (allowedOrigins == null || allowedOrigins.Length == 0)
        {
            throw new InvalidOperationException("AllowedOrigins must be configured in appsettings.json");
        }
        services.AddCors(options =>
        {
            options.AddPolicy("AllowAllOrigins",
                builder =>
                {
                    builder
                        .AllowCredentials()
                        .WithOrigins(allowedOrigins)
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                });
        });

        var guestPolicy = new AuthorizationPolicyBuilder()
            .RequireAuthenticatedUser()
            .RequireClaim("scope", "dataEventRecords")
            .Build();
        //builder.Services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

        builder.Services.AddSignalR();
        builder.Services.AddAuthentication(options =>
        {
            options.DefaultScheme = OpenIddict.Validation.AspNetCore.OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
            options.DefaultAuthenticateScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
        });
        builder.Services.AddOpenIddict()
        .AddValidation(options =>
        {
            options.SetIssuer(issuerUri);
            //options.SetClientId("resource_server");
            using (var store = new X509Store(StoreName.My, StoreLocation.LocalMachine))
            {
                store.Open(OpenFlags.ReadOnly);
                var certificate2 = store.Certificates.Find(X509FindType.FindBySubjectName, "localhost", false)[0];
                var xEncrypt = new X509Certificate2(certificate2);

                // Register the signing and encryption credentials used to protect
                // sensitive data like the state tokens produced by OpenIddict.
                options.AddEncryptionCertificate(xEncrypt);
            }
            options.UseSystemNetHttp(options =>
            {
                options.ConfigureHttpClientHandler(handler =>
                {
                    handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                });
            });
            options.UseAspNetCore();
        });

        builder.Services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
        });

        services.AddScoped<IAuthorizationHandler, RequireScopeHandler>();
        services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
        services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

        services.AddAuthorization(options =>
        {
            options.AddPolicy("dataEventRecordsAdmin", policyAdmin =>
            {
                policyAdmin.RequireClaim("role", "dataEventRecords.admin");
            });
            options.AddPolicy("dataEventRecordsUser", policyUser =>
            {
                policyUser.RequireClaim("role", "dataEventRecords.user");
            });
            options.AddPolicy("dataEventRecordsPolicy", policyUser =>
            {
                policyUser.Requirements.Add(new RequireScope());
            });
            options.AddPolicy("swaggerClientsUser", policyUser =>
            {
                policyUser.RequireClaim("role", "swaggerClient.user");
            });
            options.AddPolicy("swaggerClientPolicy", policyUser =>
            {
                policyUser.Requirements.Add(new RequireScope());
            });
        });

        services.AddControllers();

        builder.Services.AddServiceExtensions(builder.Configuration);

        services.AddIdentity<ApplicationUser, IdentityRole>()
          .AddEntityFrameworkStores<AppDbContext>()
          .AddDefaultTokenProviders();
        //services.AddScoped<IDataEventRecordRepository, DataEventRecordRepository>();
        services.Configure<IdentityOptions>(options =>
        {
            // Configure Identity to use the same JWT claims as OpenIddict instead
            // of the legacy WS-Federation claims it uses by default (ClaimTypes),
            // which saves you from doing the mapping in your authorization controller.
            options.ClaimsIdentity.UserNameClaimType = Claims.Name;
            options.ClaimsIdentity.UserIdClaimType = Claims.Subject;
            options.ClaimsIdentity.RoleClaimType = Claims.Role;
            options.ClaimsIdentity.EmailClaimType = Claims.Email;

            // Note: to require account confirmation before login,
            // register an email sender service (IEmailSender) and
            // set options.SignIn.RequireConfirmedAccount to true.
            //
            // For more information, visit https://aka.ms/aspaccountconf.
        });
        return builder.Build();
    }

    public static WebApplication ConfigurePipeline(this WebApplication app)
    {
        var deploySwaggerUI = app.Configuration.GetValue<bool>("DeploySwaggerUI");
        app.UseCors("AllowAllOrigins");

        app.UseSecurityHeaders();

        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.OAuthClientId("swaggerClient");
            c.OAuthClientSecret("swaggerClient@123");
            c.OAuthUsePkce();
            c.OAuthScopes("roles");
        });
        app.UseHttpsRedirection();
        app.UseAuthentication();
        app.UseAuthorization();
        app.MapHub<DataHub>("/dataHub");

        app.MapControllers();

        //app.MapOpenApi(); // /openapi/v1.json
        app.MapOpenApi("/openapi/v1/openapi.json");
        //app.MapOpenApi("/openapi/{documentName}/openapi.json");

        if (deploySwaggerUI)
        {
            //app.UseSwaggerUI(options =>
            //{
            //    options.SwaggerEndpoint("/openapi/v1/openapi.json", "v1");
            //});
        }
        return app;
    }
}
