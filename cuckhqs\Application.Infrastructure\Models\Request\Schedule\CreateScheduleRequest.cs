﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Microsoft.VisualBasic;

namespace Application.Infrastructure.Models.Request.Schedule
{
    public class CreateScheduleRequest
    {
        public string? Name { set; get; }
        public string? Week  { set; get; }
        public string? Year { set; get; }
        public DateTime? Date { set; get; }
        public string? Rank { set; get; }
        public short? Active { set; get; } = 0;
        public int? SortOrder { set; get; } = 0;

        public static Expression<Func<CreateScheduleRequest, ScheduleEntity>> Expression
        {
            get
            {
                return entity => new ScheduleEntity
                {
                    Name = Convert.ToString(entity.Name),
                    Year = Convert.ToInt32(entity.Year),
                    Week = Convert.ToInt32(entity.Week),
                    Date = Convert.ToDateTime(entity.Date),
                    Rank = Convert.ToString(entity.Rank),
                    Active = Convert.ToBoolean(entity.Active),
                    SortOrder = Convert.ToInt32(entity.SortOrder),
                };
            }
        }

        public static ScheduleEntity Create(CreateScheduleRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
