﻿using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Position;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers;

[ApiController]
[Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
[Route("api/[controller]")]
public class PositionController : ControllerBase
{
    private readonly IPositionService _positionService;

    public PositionController(IPositionService positionService)
    {
        _positionService = positionService;
    }

    [HttpGet("get-all")]
    public async Task<IActionResult> GetAllPositionAsync()
    {
        try
        {
            var response = await _positionService.GetAllPositionAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("get-tree")]
    public async Task<IActionResult> GetPositionBuildTreeAsync()
    {
        try
        {
            var response = await _positionService.GetAllPositionBuildTreeAsync();
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }


    [HttpPost("Search")]
    public async Task<IActionResult> SearchPositionAsync([FromBody] SearchPositionRequest request)
    {
        try
        {
            var response = await _positionService.SearchPositionAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("GetPositionById")]
    public async Task<IActionResult> GetPositionById([FromQuery] int Id)
    {
        try
        {
            var response = await _positionService.GetPositionById(Id);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("Create")]
    public async Task<IActionResult> CreatePositionAsync([FromBody] CreatePositionRequest request)
    {
        var response = await _positionService.CreatePositionAsync(request);
        return Ok(response);
    }

    [HttpPut("Update")]
    public async Task<IActionResult> UpdatePositionAsync([FromBody] UpdatePositionRequest request)
    {
        try
        {
            var response = await _positionService.UpdatePositionAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("Delete")]

    public async Task<IActionResult> DeletePositionAsync([FromBody] DeletePositionRequest request)
    {
        try
        {
            var response = await _positionService.DeletePositionAsync(request);
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}