﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class SoSRepository : GenericRepository<SoSEntity, int>, ISoSRepository
    {
        public AppDbContext _context { get; set; }

        public SoSRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<SoSEntity>> GetAllSoSAsync(int type)
        {
            if (type == 1)
            {
                return await _context.SoS
                .Where(p => p.IsRoot == false)
                .ToListAsync();
            }
            else if (type == 0)
            {
                return await _context.SoS
                .ToListAsync();
            }
            else return null;
        }

        protected override void Update(SoSEntity requestObject, SoSEntity targetObject)
        {
            targetObject.ParentId = requestObject.ParentId;
            targetObject.SoSCode = requestObject.SoSCode;
            targetObject.SoSName = requestObject.SoSName;
            targetObject.Year = requestObject.Year;
            targetObject.Active = requestObject.Active;
        }
    }
}
