﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class DistrictRepository : GenericRepository<DistrictEntity, int>, IDistrictRepository
    {
        public AppDbContext _context { get; set; }

        public DistrictRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<ProvinceEntity>> GetAllProvinceAsync()
        {
            return await _context.Province.ToListAsync();
        }

        public async Task<List<DistrictEntity>> GetAllDistrictAsync()
        {
            return await _context.District.ToListAsync();
        }

        protected override void Update(DistrictEntity requestObject, DistrictEntity targetObject)
        {
            targetObject.ProvinceId = requestObject.ProvinceId;
            targetObject.DistrictCode = requestObject.DistrictCode;
            targetObject.DistrictName = requestObject.DistrictName;
            targetObject.Active = requestObject.Active;
            targetObject.Description = requestObject.Description;
        }
    }
}
