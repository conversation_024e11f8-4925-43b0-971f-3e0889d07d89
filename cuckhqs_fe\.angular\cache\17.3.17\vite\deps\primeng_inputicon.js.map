{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-inputicon.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputIcon displays an icon.\n * @group Components\n */\nconst _c0 = [\"*\"];\nclass InputIcon {\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  styleClass;\n  static ɵfac = function InputIcon_Factory(t) {\n    return new (t || InputIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputIcon,\n    selectors: [[\"p-inputIcon\"]],\n    inputs: {\n      styleClass: \"styleClass\"\n    },\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"p-input-icon\", 3, \"ngClass\"]],\n    template: function InputIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.styleClass);\n      }\n    },\n    dependencies: [i1.NgClass],\n    styles: [\"@layer primeng{.p-fluid .p-icon-field-left,.p-fluid .p-icon-field-right{width:100%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputIcon, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputIcon',\n      template: `<span class=\"p-input-icon\" [ngClass]=\"styleClass\"><ng-content></ng-content></span>`,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\"@layer primeng{.p-fluid .p-icon-field-left,.p-fluid .p-icon-field-right{width:100%}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\nclass InputIconModule {\n  static ɵfac = function InputIconModule_Factory(t) {\n    return new (t || InputIconModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputIconModule,\n    declarations: [InputIcon],\n    imports: [CommonModule],\n    exports: [InputIcon, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [InputIcon, SharedModule],\n      declarations: [InputIcon]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputIcon, InputIconModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,GAAG;AAC1C,WAAO,KAAK,KAAK,YAAW;AAAA,EAC9B;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,SAAS,CAAC;AAAA,IAC1C,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,UAAU;AAAA,MACzC;AAAA,IACF;AAAA,IACA,cAAc,CAAI,OAAO;AAAA,IACzB,QAAQ,CAAC,wFAAwF;AAAA,IACjG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,wFAAwF;AAAA,IACnG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,SAAS;AAAA,IACxB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,WAAW,YAAY;AAAA,EACnC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,YAAY;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,WAAW,YAAY;AAAA,MACjC,cAAc,CAAC,SAAS;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}