﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Configurations;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Repositories.Abstractions;
using log4net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Application.Infrastructure.Services
{
    public class DepartmentServices : IDepartmentServices
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public DepartmentServices(IUnitOfWork unitOfWork, AppDbContext dbContext)
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
        }
        public async Task<BaseSearchResponse<DepartmentResponse>> SearchDepartmentAsync(SearchDepartmentRequest request)
        {
            try
            {
                var query = _unitOfWork.Department
                            .AsQueryable()
                            .AsNoTracking()
                            .Where(x => (string.IsNullOrEmpty(request.keyword) || x.Name.Contains(request.keyword) || x.Code.Contains(request.keyword))
                                        && (string.IsNullOrEmpty(request.IsActive) || x.IsActive == request.IsActive)
                                       && (!request.CreatedBy.HasValue || request.CreatedBy == 0 || x.CreatedBy == request.CreatedBy)
                                        &&  (request.CreatedDateTu == null && request.CreatedDateDen == null
                                        || (request.CreatedDateTu.HasValue && request.CreatedDateDen.HasValue
                                            && x.CreatedDate.Value.Date >= request.CreatedDateTu.Value.Date
                                            && x.CreatedDate.Value.Date <= request.CreatedDateDen.Value.Date)
                                        || (request.CreatedDateTu.HasValue && !request.CreatedDateDen.HasValue
                                            && x.CreatedDate.Value.Date >= request.CreatedDateTu.Value.Date)
                                        || (!request.CreatedDateTu.HasValue && request.CreatedDateDen.HasValue
                                            && x.CreatedDate.Value.Date <= request.CreatedDateDen.Value.Date)
                                            )
                                          && (string.IsNullOrEmpty(request.Status) || x.Status == request.Status)
                                        )
                            .Take(request.PageSize) ;

                var allUnits = await query.ToListAsync();
                var rootUnits = allUnits.Where(x => x.ParentId == null || x.ParentId == 0).ToList();

                // Xây dựng cấu trúc cha-con đệ quy
                foreach (var rootUnit in rootUnits)
                {
                    BuildUnitHierarchy(rootUnit, allUnits);
                }

                var response = rootUnits.Select(s => DepartmentResponse.Create(s)).ToList();
                return BaseSearchResponse<DepartmentResponse>.GetResponseNoPagination(response, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
        public async Task<bool> UpdateStatusByIds(UpdateStatusByIdsIntRequest request)
        {
            var entities = await _unitOfWork.Department.AsQueryable().Where(s => request.Ids.Contains(s.Id)).ToListAsync();

            entities.ForEach(s => { s.Status = request.Status; });

            _unitOfWork.Department.UpdateRange(entities);
            await _unitOfWork.CommitChangesAsync();
            return true;
        }

        private void BuildUnitHierarchy(DepartmentEntity parent, List<DepartmentEntity> allUnits)
        {
         
        }

        private void BuildUnitHierarchy1(DepartmentEntity parent, List<DepartmentEntity> allUnits)
        {
            // Lấy danh sách đơn vị con có ParentId trùng với Id của đơn vị cha
            var children = allUnits.Where(unit => unit.ParentId == parent.Id).ToList();

        }

        public async Task<DepartmentResponse> CreateDepartmentAsync(CreateDepartmentRequest request)
        {
            try
            {
                var entity = CreateDepartmentRequest.Create(request);

                await _unitOfWork.Department.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return DepartmentResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateDepartmentAsync(UpdateDepartmentRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Department.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException("NOT_FOUND");

                var updateRecord = UpdateDepartmentRequest.Create(request);

                await _unitOfWork.Department.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteDepartmentAsync(DeleteDepartmentRequest request)
        {
            try
            {
                var allUnits = await _unitOfWork.Department.AsQueryable().AsNoTracking().ToListAsync(); 

                var allIdsToDelete = new HashSet<int>(request.Ids); 

                var recordsToDelete = await _unitOfWork.Department.AsQueryable()
                                                            .Where(records => allIdsToDelete.Contains(records.Id))
                                                            .ToListAsync();

                _unitOfWork.Department.RemoveRange(recordsToDelete);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}