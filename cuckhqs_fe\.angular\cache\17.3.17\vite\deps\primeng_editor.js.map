{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-editor.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformServer, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, afterNextRender, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nconst _c0 = [[[\"p-header\"]]];\nconst _c1 = [\"p-header\"];\nfunction Editor_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Editor_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Editor_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\nfunction Editor_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 5)(2, \"select\", 6)(3, \"option\", 7);\n    i0.ɵɵtext(4, \"Heading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 8);\n    i0.ɵɵtext(6, \"Subheading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 9);\n    i0.ɵɵtext(8, \"Normal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"select\", 10)(10, \"option\", 9);\n    i0.ɵɵtext(11, \"Sans Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 11);\n    i0.ɵɵtext(13, \"Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Monospace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"span\", 5);\n    i0.ɵɵelement(17, \"button\", 13)(18, \"button\", 14)(19, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 5);\n    i0.ɵɵelement(21, \"select\", 16)(22, \"select\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 5);\n    i0.ɵɵelement(24, \"button\", 18)(25, \"button\", 19);\n    i0.ɵɵelementStart(26, \"select\", 20);\n    i0.ɵɵelement(27, \"option\", 9);\n    i0.ɵɵelementStart(28, \"option\", 21);\n    i0.ɵɵtext(29, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 22);\n    i0.ɵɵtext(31, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 23);\n    i0.ɵɵtext(33, \"justify\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"span\", 5);\n    i0.ɵɵelement(35, \"button\", 24)(36, \"button\", 25)(37, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 5);\n    i0.ɵɵelement(39, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst EDITOR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Editor),\n  multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nclass Editor {\n  el;\n  platformId;\n  /**\n   * Inline style of the container.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the container.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Placeholder text to show when editor is empty.\n   * @group Props\n   */\n  placeholder;\n  /**\n   * Whitelist of formats to display, see here for available options.\n   * @group Props\n   */\n  formats;\n  /**\n   * Modules configuration of Editor, see here for available options.\n   * @group Props\n   */\n  modules;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n   * @group Props\n   */\n  bounds;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n   * @group Props\n   */\n  scrollingContainer;\n  /**\n   * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n   * @group Props\n   */\n  debug;\n  /**\n   * Whether to instantiate the editor to read-only mode.\n   * @group Props\n   */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(val) {\n    this._readonly = val;\n    if (this.quill) {\n      if (this._readonly) this.quill.disable();else this.quill.enable();\n    }\n  }\n  /**\n   * Callback to invoke when the quill modules are loaded.\n   * @param {EditorInitEvent} event - custom event.\n   * @group Emits\n   */\n  onInit = new EventEmitter();\n  /**\n   * Callback to invoke when text of editor changes.\n   * @param {EditorTextChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onTextChange = new EventEmitter();\n  /**\n   * Callback to invoke when selection of the text changes.\n   * @param {EditorSelectionChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onSelectionChange = new EventEmitter();\n  templates;\n  toolbar;\n  value;\n  _readonly = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  quill;\n  dynamicQuill;\n  headerTemplate;\n  quillElements;\n  constructor(el, platformId) {\n    this.el = el;\n    this.platformId = platformId;\n    /**\n     * Read or write the DOM once, when initializing non-Angular (Quill) library.\n     */\n    afterNextRender(() => {\n      this.initQuillElements();\n      this.initQuillEditor();\n    });\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n      }\n    });\n  }\n  writeValue(value) {\n    this.value = value;\n    if (this.quill) {\n      if (value) {\n        this.quill.setContents(this.quill.clipboard.convert(this.dynamicQuill.version.startsWith('2') ? {\n          html: this.value\n        } : this.value));\n      } else {\n        this.quill.setText('');\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  getQuill() {\n    return this.quill;\n  }\n  initQuillEditor() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    /**\n     * Importing Quill at top level, throws `document is undefined` error during when\n     * building for SSR, so this dynamically loads quill when it's in browser module.\n     */\n    if (!this.dynamicQuill) {\n      import('quill').then(quillModule => {\n        this.dynamicQuill = quillModule.default;\n        this.createQuillEditor();\n      }).catch(e => console.error(e.message));\n    } else {\n      this.createQuillEditor();\n    }\n  }\n  createQuillEditor() {\n    this.initQuillElements();\n    const {\n      toolbarElement,\n      editorElement\n    } = this.quillElements;\n    let defaultModule = {\n      toolbar: toolbarElement\n    };\n    let modules = this.modules ? {\n      ...defaultModule,\n      ...this.modules\n    } : defaultModule;\n    this.quill = new this.dynamicQuill(editorElement, {\n      modules: modules,\n      placeholder: this.placeholder,\n      readOnly: this.readonly,\n      theme: 'snow',\n      formats: this.formats,\n      bounds: this.bounds,\n      debug: this.debug,\n      scrollingContainer: this.scrollingContainer\n    });\n    const isQuill2 = this.dynamicQuill.version.startsWith('2');\n    if (this.value) {\n      this.quill.setContents(this.quill.clipboard.convert(isQuill2 ? {\n        html: this.value\n      } : this.value));\n    }\n    this.quill.on('text-change', (delta, oldContents, source) => {\n      if (source === 'user') {\n        let html = isQuill2 ? this.quill.getSemanticHTML() : DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n        let text = this.quill.getText().trim();\n        if (html === '<p><br></p>') {\n          html = null;\n        }\n        this.onTextChange.emit({\n          htmlValue: html,\n          textValue: text,\n          delta: delta,\n          source: source\n        });\n        this.onModelChange(html);\n        this.onModelTouched();\n      }\n    });\n    this.quill.on('selection-change', (range, oldRange, source) => {\n      this.onSelectionChange.emit({\n        range: range,\n        oldRange: oldRange,\n        source: source\n      });\n    });\n    this.onInit.emit({\n      editor: this.quill\n    });\n  }\n  initQuillElements() {\n    if (!this.quillElements) {\n      this.quillElements = {\n        editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n        toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n      };\n    }\n  }\n  static ɵfac = function Editor_Factory(t) {\n    return new (t || Editor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Editor,\n    selectors: [[\"p-editor\"]],\n    contentQueries: function Editor_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.toolbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      placeholder: \"placeholder\",\n      formats: \"formats\",\n      modules: \"modules\",\n      bounds: \"bounds\",\n      scrollingContainer: \"scrollingContainer\",\n      debug: \"debug\",\n      readonly: \"readonly\"\n    },\n    outputs: {\n      onInit: \"onInit\",\n      onTextChange: \"onTextChange\",\n      onSelectionChange: \"onSelectionChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([EDITOR_VALUE_ACCESSOR])],\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 6,\n    consts: [[3, \"ngClass\"], [\"class\", \"p-editor-toolbar\", 4, \"ngIf\"], [1, \"p-editor-content\", 3, \"ngStyle\"], [1, \"p-editor-toolbar\"], [4, \"ngTemplateOutlet\"], [1, \"ql-formats\"], [1, \"ql-header\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"selected\", \"\"], [1, \"ql-font\"], [\"value\", \"serif\"], [\"value\", \"monospace\"], [\"aria-label\", \"Bold\", \"type\", \"button\", 1, \"ql-bold\"], [\"aria-label\", \"Italic\", \"type\", \"button\", 1, \"ql-italic\"], [\"aria-label\", \"Underline\", \"type\", \"button\", 1, \"ql-underline\"], [1, \"ql-color\"], [1, \"ql-background\"], [\"value\", \"ordered\", \"aria-label\", \"Ordered List\", \"type\", \"button\", 1, \"ql-list\"], [\"value\", \"bullet\", \"aria-label\", \"Unordered List\", \"type\", \"button\", 1, \"ql-list\"], [1, \"ql-align\"], [\"value\", \"center\"], [\"value\", \"right\"], [\"value\", \"justify\"], [\"aria-label\", \"Insert Link\", \"type\", \"button\", 1, \"ql-link\"], [\"aria-label\", \"Insert Image\", \"type\", \"button\", 1, \"ql-image\"], [\"aria-label\", \"Insert Code Block\", \"type\", \"button\", 1, \"ql-code-block\"], [\"aria-label\", \"Remove Styles\", \"type\", \"button\", 1, \"ql-clean\"]],\n    template: function Editor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, Editor_div_1_Template, 3, 1, \"div\", 1)(2, Editor_div_2_Template, 40, 0, \"div\", 1);\n        i0.ɵɵelement(3, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-editor-container\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.toolbar || ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.modules && !ctx.toolbar && !ctx.headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", ctx.style);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n    styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Editor, [{\n    type: Component,\n    args: [{\n      selector: 'p-editor',\n      template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!modules && !toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `,\n      providers: [EDITOR_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    formats: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    scrollingContainer: [{\n      type: Input\n    }],\n    debug: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    onInit: [{\n      type: Output\n    }],\n    onTextChange: [{\n      type: Output\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    toolbar: [{\n      type: ContentChild,\n      args: [Header]\n    }]\n  });\n})();\nclass EditorModule {\n  static ɵfac = function EditorModule_Factory(t) {\n    return new (t || EditorModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: EditorModule,\n    declarations: [Editor],\n    imports: [CommonModule],\n    exports: [Editor, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EditorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Editor, SharedModule],\n      declarations: [Editor]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAC3B,IAAM,MAAM,CAAC,UAAU;AACvB,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,gBAAgB,CAAC;AAC9E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,cAAc;AAAA,EACzD;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC;AAC3E,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,UAAU,EAAE,EAAE,IAAI,UAAU,CAAC;AAClD,IAAG,OAAO,IAAI,YAAY;AAC1B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AACjE,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AAC/C,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AAC/C,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,UAAU,IAAI,UAAU,CAAC;AAC5B,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,QAAQ;AACtB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,SAAS;AACvB,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AACjE,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE;AAC7B,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,MAAM;AAAA,EACpC,OAAO;AACT;AAKA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AACjB,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,UAAW,MAAK,MAAM,QAAQ;AAAA,UAAO,MAAK,MAAM,OAAO;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,oBAAoB,IAAI,aAAa;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,IAAI,YAAY;AAC1B,SAAK,KAAK;AACV,SAAK,aAAa;AAIlB,oBAAgB,MAAM;AACpB,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,QAAI,KAAK,OAAO;AACd,UAAI,OAAO;AACT,aAAK,MAAM,YAAY,KAAK,MAAM,UAAU,QAAQ,KAAK,aAAa,QAAQ,WAAW,GAAG,IAAI;AAAA,UAC9F,MAAM,KAAK;AAAA,QACb,IAAI,KAAK,KAAK,CAAC;AAAA,MACjB,OAAO;AACL,aAAK,MAAM,QAAQ,EAAE;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AAKA,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO,qBAAO,EAAE,KAAK,iBAAe;AAClC,aAAK,eAAe,YAAY;AAChC,aAAK,kBAAkB;AAAA,MACzB,CAAC,EAAE,MAAM,OAAK,QAAQ,MAAM,EAAE,OAAO,CAAC;AAAA,IACxC,OAAO;AACL,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,kBAAkB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,gBAAgB;AAAA,MAClB,SAAS;AAAA,IACX;AACA,QAAI,UAAU,KAAK,UAAU,kCACxB,gBACA,KAAK,WACN;AACJ,SAAK,QAAQ,IAAI,KAAK,aAAa,eAAe;AAAA,MAChD;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,UAAU,KAAK;AAAA,MACf,OAAO;AAAA,MACP,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,oBAAoB,KAAK;AAAA,IAC3B,CAAC;AACD,UAAM,WAAW,KAAK,aAAa,QAAQ,WAAW,GAAG;AACzD,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,YAAY,KAAK,MAAM,UAAU,QAAQ,WAAW;AAAA,QAC7D,MAAM,KAAK;AAAA,MACb,IAAI,KAAK,KAAK,CAAC;AAAA,IACjB;AACA,SAAK,MAAM,GAAG,eAAe,CAAC,OAAO,aAAa,WAAW;AAC3D,UAAI,WAAW,QAAQ;AACrB,YAAI,OAAO,WAAW,KAAK,MAAM,gBAAgB,IAAI,WAAW,WAAW,eAAe,YAAY,EAAE;AACxG,YAAI,OAAO,KAAK,MAAM,QAAQ,EAAE,KAAK;AACrC,YAAI,SAAS,eAAe;AAC1B,iBAAO;AAAA,QACT;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,WAAW;AAAA,UACX,WAAW;AAAA,UACX;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,cAAc,IAAI;AACvB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AACD,SAAK,MAAM,GAAG,oBAAoB,CAAC,OAAO,UAAU,WAAW;AAC7D,WAAK,kBAAkB,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,OAAO,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB;AAAA,QACnB,eAAe,WAAW,WAAW,KAAK,GAAG,eAAe,sBAAsB;AAAA,QAClF,gBAAgB,WAAW,WAAW,KAAK,GAAG,eAAe,sBAAsB;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,GAAG;AACvC,WAAO,KAAK,KAAK,SAAW,kBAAqB,UAAU,GAAM,kBAAkB,WAAW,CAAC;AAAA,EACjG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,gBAAgB,SAAS,sBAAsB,IAAI,KAAK,UAAU;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,QAAQ,CAAC;AACrC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;AAAA,IACzD,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,WAAW,GAAG,CAAC,cAAc,QAAQ,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,cAAc,UAAU,QAAQ,UAAU,GAAG,WAAW,GAAG,CAAC,cAAc,aAAa,QAAQ,UAAU,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,SAAS,WAAW,cAAc,gBAAgB,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,SAAS,UAAU,cAAc,kBAAkB,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,SAAS,GAAG,CAAC,cAAc,eAAe,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,cAAc,gBAAgB,QAAQ,UAAU,GAAG,UAAU,GAAG,CAAC,cAAc,qBAAqB,QAAQ,UAAU,GAAG,eAAe,GAAG,CAAC,cAAc,iBAAiB,QAAQ,UAAU,GAAG,UAAU,CAAC;AAAA,IAChhC,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,uBAAuB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,uBAAuB,IAAI,GAAG,OAAO,CAAC;AACjG,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,oBAAoB;AAC7C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,cAAc;AACvD,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,cAAc;AACzE,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,MAAS,kBAAqB,OAAO;AAAA,IACnE,QAAQ,CAAC,mIAAmI;AAAA,IAC5I,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkDV,WAAW,CAAC,qBAAqB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,mIAAmI;AAAA,IAC9I,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,GAAG;AAC7C,WAAO,KAAK,KAAK,eAAc;AAAA,EACjC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,MAAM;AAAA,IACrB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,QAAQ,YAAY;AAAA,EAChC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,YAAY;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,QAAQ,YAAY;AAAA,MAC9B,cAAc,CAAC,MAAM;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}