﻿using Application.Infrastructure.Constants;
using Application.Infrastructure.Models;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Helpers
{
    public static class ClaimsHelper
    {
        public static void GetPermissions(this List<RoleClaimsViewResponse> allPermissions, Type policy, string roleId)
        {
            FieldInfo[] fields = policy.GetFields(BindingFlags.Static | BindingFlags.Public);
            foreach (FieldInfo fi in fields)
            {
                allPermissions.Add(new RoleClaimsViewResponse { Value = fi.GetValue(null).ToString(), Type = "Permissions" });
            }
        }
        public static void GetAllPermissions(this List<RoleClaimsViewResponse> allPermissions, string roleId)
        {
            var permissionTypes = new[]
             {
                typeof(Permissions.Products),
                typeof(Permissions.Users),
                typeof(Permissions.Roles)
            };

            foreach (var permissionType in permissionTypes)
            {
                allPermissions.GetPermissions(permissionType, roleId);
            }
        }
        public static async Task AddPermissionClaim(this RoleManager<IdentityRole> roleManager, IdentityRole role, string permission)
        {
            var allClaims = await roleManager.GetClaimsAsync(role);
            if (!allClaims.Any(a => a.Type == "Permission" && a.Value == permission))
            {
                await roleManager.AddClaimAsync(role, new Claim("Permission", permission));
            }
        }
    }
}
