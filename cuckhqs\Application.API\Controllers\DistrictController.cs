﻿using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.District;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class DistrictController : ControllerBase
    {
        private readonly IDistrictService _districtService;

        public DistrictController(IDistrictService districtervice)
        {
            _districtService = districtervice;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchDistrictAsync([FromBody] SearchDistrictRequest request)
        {
            try
            {
                var response = await _districtService.SearchDistrictAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateDistrictAsync([FromBody] CreateDistrictRequest request)
        {
            var response = await _districtService.CreateDistrictAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateDistrictAsync([FromBody] UpdateDistrictRequest request)
        {
            try
            {
                var response = await _districtService.UpdateDistrictAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteDistrictAsync([FromBody] DeleteDistrictRequest request)
        {
            try
            {
                var response = await _districtService.DeleteDistrictAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
