﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class WorkingResultConfiguration : IEntityTypeConfiguration<WorkingResultEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingResultEntity> builder)
        {
            builder.ToTable("WorkingResult");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.Name).HasColumnName("Name");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Week).HasColumnName("Week");
            builder.Property(x => x.DateFrom).HasColumnName("DateFrom");
            builder.Property(x => x.DateTo).HasColumnName("DateTo");
            builder.Property(x => x.Class).HasColumnName("Class");
            builder.Property(x => x.Contents).HasColumnName("Contents");
            builder.Property(x => x.Status).HasColumnName("Status");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.Contents1).HasColumnName("Contents1");
            builder.Property(x => x.Contents2).HasColumnName("Contents2");
            builder.Property(x => x.Contents3).HasColumnName("Contents3");
            builder.Property(x => x.Note).HasColumnName("Note");
        }
    }
}
