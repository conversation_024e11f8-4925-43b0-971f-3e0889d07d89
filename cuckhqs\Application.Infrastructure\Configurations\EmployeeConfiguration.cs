﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Entities.Employee;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class EmployeeConfiguration : IEntityTypeConfiguration<EmployeeCEntity>
    {
        public void Configure(EntityTypeBuilder<EmployeeCEntity> builder)
        {
            builder.ToTable("Employee");

            builder.Property(x => x.Id).HasColumnName("Id");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
         //   builder.Property(x => x.OrganizationUnitName).HasColumnName("OrganizationUnitName");
            builder.Property(x => x.EmployeeCode).HasColumnName("EmployeeCode");
            builder.Property(x => x.FullName).HasColumnName("Fullname");
            builder.Property(x => x.FirstName).HasColumnName("FirstName");
            builder.Property(x => x.LastName).HasColumnName("LastName");
            builder.Property(x => x.RankId).HasColumnName("RankId");
            builder.Property(x => x.Gender).HasColumnName("Gender");
            builder.Property(x => x.AcademicRankId).HasColumnName("AcademicRankId");
            builder.Property(x => x.YearOfAcademicRank).HasColumnName("YearOfAcademicRank");
            builder.Property(x => x.DegreeId).HasColumnName("DegreeId");
            builder.Property(x => x.YearOfDegree).HasColumnName("YearOfDegree");
            builder.Property(x => x.PositionId).HasColumnName("PositionId");
            builder.Property(x => x.PositionType).HasColumnName("PositionType");
            builder.Property(x => x.PartyPositionId).HasColumnName("PartyPositionId");
            builder.Property(x => x.BirthDay).HasColumnName("BirthDay");
            builder.Property(x => x.Owned).HasColumnName("Owned");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.ActiveAccount).HasColumnName("ActiveAccount");
            builder.Property(x => x.IsAdministrator).HasColumnName("IsAdministrator");
          //  builder.Property(x => x.RankName).HasColumnName("RankName");
            #region thêm sau
            builder.Property(x => x.LongFullName).HasColumnName("LongFullName");
            builder.Property(x => x.BirthPlace).HasColumnName("BirthPlace");
            builder.Property(x => x.HomeLand).HasColumnName("HomeLand");
            builder.Property(x => x.NativeAddress).HasColumnName("NativeAddress");
            builder.Property(x => x.Tel).HasColumnName("Tel");
            builder.Property(x => x.HomeTel).HasColumnName("HomeTel");
            builder.Property(x => x.Mobile).HasColumnName("Mobile");
            builder.Property(x => x.Fax).HasColumnName("Fax");
            builder.Property(x => x.Email).HasColumnName("Email");
            builder.Property(x => x.OfficeAddress).HasColumnName("OfficeAddress");
            builder.Property(x => x.HomeAddress).HasColumnName("HomeAddress");
            builder.Property(x => x.Website).HasColumnName("Website");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.IDNumber).HasColumnName("IDNumber");
            builder.Property(x => x.IssuedBy).HasColumnName("IssuedBy");
            builder.Property(x => x.DateBy).HasColumnName("DateBy");
            builder.Property(x => x.AccountNumber).HasColumnName("AccountNumber");
            builder.Property(x => x.Bank).HasColumnName("Bank");
            builder.Property(x => x.Avatar).HasColumnName("Avatar");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
            #endregion

            builder.HasOne<OrganizationUnitEntity>().WithMany().HasForeignKey(x => x.OrganizationUnitId);
        }
    }
}
