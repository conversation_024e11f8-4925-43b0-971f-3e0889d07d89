﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Country
{
    public class CreateCountryRequest
    {
        public string? CountryCode { get; set; }
        public string? CountryName { get; set; }
        public string? LanguageName { get; set; }
        public string? VietnameseName { get; set; }
        public int? Class { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateCountryRequest, CountryEntity>> Expression
        {
            get
            {
                return entity => new CountryEntity
                {
                    CountryCode = entity.CountryCode,
                    CountryName = entity.CountryName,
                    LanguageName = entity.LanguageName,
                    VietnameseName = entity.VietnameseName,
                    Active = entity.Active,
                    Class = entity.Class,
                    Year = DateTime.Now.Year,
                };
            }
        }

        public static CountryEntity Create(CreateCountryRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
