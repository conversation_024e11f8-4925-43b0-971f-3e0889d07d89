﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleIssueEntity : BaseEntity<int>
    {
        public int? OrganizationUnitId { get; set; }
        public int? Announced { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }
        public int? Number { get; set; }
        public string? Sign {  get; set; }
        public DateTime? Date { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public string? Place { get; set; }
        public string? Command { get; set; }
        public Guid? PersonSigning {  get; set; }
        public string? PersonSigningOther { get; set; }
        public string? UnitPositionSigning { get; set; }
        public string? IPAddress { get; set; }
        public List<WorkingScheduleIssueDetailEntity> WorkingScheduleIssueDetail { get; set; }
    }
}
