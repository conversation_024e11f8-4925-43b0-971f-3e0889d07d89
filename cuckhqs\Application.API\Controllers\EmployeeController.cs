﻿using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Abstractions.Category;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Application.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AllowAnonymous]
    public class EmployeeController : ControllerBase
    {
        private readonly IEmployeeCService _employeeService;

        public EmployeeController(IEmployeeCService employeeService)
        {
            _employeeService = employeeService;
        }

        [HttpGet("department-head")]
        public async Task<IActionResult> GetAlDepartmentHeadAsync()
        {
            try
            {
                var response = await _employeeService.GetAlDepartmentHeadAsync();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        [HttpGet("get-all")]
        public async Task<IActionResult> GetAllEmployeesAsync()
        {
            try
            {
                var employees = await _employeeService.GetAllEmployeesAsync();
                return Ok(employees);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Search")]
        public async Task<IActionResult> SearchEmployeeAsync([FromBody] SearchEmployeeRequest request)
        {
            try
            {
                var response = await _employeeService.SearchEmployeeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeeById")]
        public async Task<IActionResult> GetEmployeeById([FromQuery] Guid Id)
        {
            try
            {
                var response = await _employeeService.GetEmployeeById(Id);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateEmployeeAsync([FromBody] CreateEmployeeRequest request)
        {
            var response = await _employeeService.CreateEmployeeAsync(request);
            return Ok(response);
        }


        [HttpPut("Update")]
        public async Task<IActionResult> UpdateEmployeeAsync([FromBody] UpdateEmployeeRequest request)
        {
            try
            {
                var response = await _employeeService.UpdateEmployeeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteEmployeeAsync([FromBody] DeleteEmployeeRequest request)
        {
            try
            {
                var response = await _employeeService.DeleteEmployeeAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
