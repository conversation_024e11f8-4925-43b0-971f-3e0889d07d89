﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class WorkingScheduleIssueConfiguration : IEntityTypeConfiguration<WorkingScheduleIssueEntity>
    {
        public void Configure(EntityTypeBuilder<WorkingScheduleIssueEntity> builder)
        {
            builder.ToTable("WorkingScheduleIssue");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.OrganizationUnitId).HasColumnName("OrganizationUnitId");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Week).HasColumnName("Week");
            builder.Property(x => x.Announced).HasColumnName("Announced");
            builder.Property(x => x.Number).HasColumnName("Number");
            builder.Property(x => x.Sign).HasColumnName("Sign");
            builder.Property(x => x.Date).HasColumnName("Date");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
            builder.Property(x => x.Place).HasColumnName("Place");
            builder.Property(x => x.Command).HasColumnName("Command");
            builder.Property(x => x.PersonSigning).HasColumnName("PersonSigning");
            builder.Property(x => x.PersonSigningOther).HasColumnName("PersonSigningOther");
            builder.Property(x => x.UnitPositionSigning).HasColumnName("UnitPositionSigning");

            builder.HasMany(x => x.WorkingScheduleIssueDetail)
                   .WithOne(ws => ws.WorkingScheduleAnnounced)
                   .HasForeignKey(ws => ws.WorkingScheduleIssueId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
