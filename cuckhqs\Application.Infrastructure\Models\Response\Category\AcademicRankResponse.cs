﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class AcademicRankResponse
    {
        public int? Id { get; set; }
        public string? AcademicRankCode { get; set; }
        public string? AcademicRankName { get; set; }
        public string? AcademicRankShortName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<AcademicRankEntity, AcademicRankResponse>> Expression
        {
            get
            {
                return entity => new AcademicRankResponse()
                {
                    Id = entity.Id,
                    AcademicRankCode = entity.AcademicRankCode,
                    AcademicRankName = entity.AcademicRankName,
                    AcademicRankShortName = entity.AcademicRankShortName,
                    Description = entity.Description,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                };
            }
        }

        public static AcademicRankResponse Create(AcademicRankEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
