﻿using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models
{
    public class DepartmentEntity : BaseEntity<int>
    {
        public string? Name { get; set; }
        public string? NameShort { get; set; }
        public string? Code { get; set; }
        public int? SortOrder { get; set; }
        public string? Address { get; set; }
        public string? IsActive { get; set; }   
        public int? ParentId { get; set; }
        public string? ParentIdCode { get; set; }
        public string? PhoneNumber { get; set; }
        public string? DepartmentType { get; set; }
        public string? Description { get; set; }
        public DateTime? ScheduledDeleteDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Status { get; set; }
    }
}
