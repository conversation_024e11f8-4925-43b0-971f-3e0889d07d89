﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Country;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class CountryService : ICountryService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public CountryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<CountryResponse>> SearchCountryAsync(SearchCountryRequest request)
        {
            try
            {
                IQueryable<CountryResponse> query = _unitOfWork.Country.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.CountryCode.Contains(request.keyword) ||
                                x.CountryName.Contains(request.keyword) ||
                                x.LanguageName.Contains(request.keyword) ||
                                x.VietnameseName.Contains(request.keyword) 
                                ))
                    .Select(s => new CountryResponse()
                    {
                        Id = s.Id,
                        CountryCode = s.CountryCode,
                        CountryName = s.CountryName,
                        LanguageName = s.LanguageName,
                        Class = s.Class,
                        Year = s.Year,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                        VietnameseName = s.VietnameseName,
                    });

                return await BaseSearchResponse<CountryResponse>.GetResponse(query, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<CountryResponse> CreateCountryAsync(CreateCountryRequest request)
        {
            try
            {
                var entity = CreateCountryRequest.Create(request);

                await _unitOfWork.Country.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return CountryResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteCountryAsync(DeleteCountryRequest request)
        {
            try
            {
                var record = await _unitOfWork.Country.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Country.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateCountryAsync(UpdateCountryRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Country.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateCountryRequest.Create(request);

                await _unitOfWork.Country.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
