﻿using Application.Infrastructure.Models.Request.Category.JournalGroup;
using Application.Infrastructure.Models.Request.Category.JournalType;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class JournalGroupController : ControllerBase
    {
        private readonly IJournalGroupService _journalGroupService;

        public JournalGroupController(IJournalGroupService journalGroupService)
        {
            _journalGroupService = journalGroupService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchJournalTypeAsync([FromBody] SearchJournalGroupRequest request)
        {
            try
            {
                var response = await _journalGroupService.SearchJournalGroupAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateJournalTypeAsync([FromBody] CreateJournalGroupRequest request)
        {
            var response = await _journalGroupService.CreateJournalGroupAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateJournalTypeAsync([FromBody] UpdateJournalGroupRequest request)
        {
            try
            {
                var response = await _journalGroupService.UpdateJournalGroupAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteJournalTypeAsync([FromBody] DeleteJournalGroupRequest request)
        {
            try
            {
                var response = await _journalGroupService.DeleteJournalGroupAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
