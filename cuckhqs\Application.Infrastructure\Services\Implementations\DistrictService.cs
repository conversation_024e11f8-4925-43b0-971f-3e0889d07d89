﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.District;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class DistrictService : IDistrictService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public DistrictService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<DistrictResponse>> SearchDistrictAsync(SearchDistrictRequest request)
        {
            try
            {
                var province = await _unitOfWork.District.GetAllProvinceAsync();
                var province_Name = province?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ProvinceName)
                    ?? new Dictionary<string, string>();

                IQueryable<DistrictResponse> query = _unitOfWork.District.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.DistrictCode.Contains(request.keyword) ||
                                x.DistrictName.Contains(request.keyword))
                                && (request.ProvinceId == null || request.ProvinceId == 0 || x.ProvinceId == request.ProvinceId)
                                )
                    .Select(s => new DistrictResponse()
                    {
                        Id = s.Id,
                        ProvinceId = s.ProvinceId,
                        Province_Name = null,
                        DistrictCode = s.DistrictCode,
                        DistrictName = s.DistrictName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });
                var resultQuery = query.AsEnumerable() 
                    .Select(emp =>
                    {
                        emp.Province_Name = province_Name.TryGetValue(emp.ProvinceId?.ToString().Trim() ?? "", out var name) ? name : "";
                        return emp;
                    })
                    .AsQueryable();
                return await BaseSearchResponse<DistrictResponse>.GetResponse(resultQuery, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<DistrictResponse> CreateDistrictAsync(CreateDistrictRequest request)
        {
            try
            {
                var entity = CreateDistrictRequest.Create(request);

                await _unitOfWork.District.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return DistrictResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<string> DeleteDistrictAsync(DeleteDistrictRequest request)
        {
            try
            {
                var ward = await _unitOfWork.Province.GetAllWardAsync();
                var hasParentReference = ward.Any(o => o.DistrictId.HasValue && request.Ids.Contains(o.DistrictId.Value));
                if (hasParentReference)
                {
                    return "Xóa không thành công: Một hoặc nhiều huyện vẫn còn đơn vị cấp xã";
                }
                var record = await _unitOfWork.District.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.District.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return "Xóa thành công";
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateDistrictAsync(UpdateDistrictRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.District.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateDistrictRequest.Create(request);

                await _unitOfWork.District.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
