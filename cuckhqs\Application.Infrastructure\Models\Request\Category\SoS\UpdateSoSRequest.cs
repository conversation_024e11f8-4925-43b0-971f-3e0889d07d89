﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.SoS
{
    public class UpdateSoSRequest : CreateSoSRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateSoSRequest, SoSEntity>> Expression
        {
            get
            {
                return entity => new SoSEntity
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    SoSCode = entity.SoSCode,
                    SoSName = entity.SoSName,
                    Year = entity.Year,
                    Active = entity.Active,
                    IsRoot = false
                };
            }
        }

        public static SoSEntity Create(UpdateSoSRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
