﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.District
{
    public class CreateDistrictRequest
    {
        public int? ProvinceId { get; set; }
        public string? DistrictCode { get; set; }
        public string? DistrictName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateDistrictRequest, DistrictEntity>> Expression
        {
            get
            {
                return entity => new DistrictEntity
                {
                    ProvinceId = entity.ProvinceId,
                    DistrictCode = entity.DistrictCode,
                    DistrictName = entity.DistrictName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static DistrictEntity Create(CreateDistrictRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
