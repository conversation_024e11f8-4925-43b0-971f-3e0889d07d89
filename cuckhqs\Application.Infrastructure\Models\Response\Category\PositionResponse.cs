﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class PositionResponse
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public bool? IsRoot { get; set; }
        public string? PositionCode { get; set; }
        public string? ParentCode { get; set; }
        public short? Classify { get; set; }
        public string? PositionName { get; set; }
        public string? ShortPositionName { get; set; }
        public string? FullPositionName { get; set; }
        public string? Description { get; set; }
        public string? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public bool? Expandable { get; set; }
        public List<PositionResponse> Children { get; set; } = new();


        public static Expression<Func<PositionEntity, PositionResponse>> Expression
        {
            get
            {
                return entity => new PositionResponse()
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    PositionCode = entity.PositionCode,
                    PositionName = entity.PositionName,
                    IsRoot = entity.IsRoot,
                    Classify = entity.Classify,
                    ShortPositionName = entity.ShortPositionName,
                    Description = entity.Description,
                    Active = entity.Active.ToString(),
                };
            }
        }

        public static PositionResponse Create(PositionEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
