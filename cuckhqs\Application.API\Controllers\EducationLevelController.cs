﻿using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.EducationLevel;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class EducationLevelController : ControllerBase
    {
        private readonly IEducationLevelService _educationLevel;

        public EducationLevelController(IEducationLevelService educationLevelService)
        {
            _educationLevel = educationLevelService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchEducationLevelAsync([FromBody] SearchEducationLevelRequest request)
        {
            try
            {
                var response = await _educationLevel.SearchEducationLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateEducationLevelAsync([FromBody] CreateEducationLevelRequest request)
        {
            var response = await _educationLevel.CreateEducationLevelAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateEducationLevelAsync([FromBody] UpdateEducationLevelRequest request)
        {
            try
            {
                var response = await _educationLevel.UpdateEducationLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteEducationLevelAsync([FromBody] DeleteEducationLevelRequest request)
        {
            try
            {
                var response = await _educationLevel.DeleteEducationLevelAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
