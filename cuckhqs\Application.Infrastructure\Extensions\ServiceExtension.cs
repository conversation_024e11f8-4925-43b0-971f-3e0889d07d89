﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.Category;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Repositories.Implementations;
using Application.Infrastructure.Repositories.Implementations.EmployeeRepository;
using Application.Infrastructure.Repositories.Implementations.OrganizationRepository;
using Application.Infrastructure.Services;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Application.Infrastructure.Repositories.Implementations.Category;
using Application.Infrastructure.Services.Abstractions.Category;
using System.Configuration;
using Application.Infrastructure.Email;

namespace Application.Infrastructure.Extensions
{
    public static class ServiceExtension
    {
        public static void AddServiceExtensions(this IServiceCollection serviceCollection, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            serviceCollection.AddTransient<IEmailSender, MailDevEmailSender>();
            serviceCollection.AddDbContext<AppDbContext>((service, option) =>
            {
                option.UseSqlServer(connectionString).EnableSensitiveDataLogging();

                // Register the entity sets needed by OpenIddict.
                // Note: use the generic overload if you need
                // to replace the default OpenIddict entities.
                option.UseOpenIddict();
            });

            serviceCollection.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());


            serviceCollection.AddTransient<IUnitOfWork, UnitOfWork>();
            serviceCollection.AddScoped<IUserContext, UserContext>();
            serviceCollection.AddScoped<DbContext, AppDbContext>();

            #region Repository
            serviceCollection.AddScoped<IDepartmentRepository, DepartmentRepository>();
            serviceCollection.AddScoped<IWorkingScheduleRepository, WorkingScheduleRepository>();
            serviceCollection.AddScoped<IOrganizationUnitRepository,OrganizationUnitRepository>();
            serviceCollection.AddScoped<IEmployeeRepository, EmployeeCRepository>();
            serviceCollection.AddScoped<IAdvertisementRepository, AdvertisementRepository>();
            serviceCollection.AddScoped<IWorkingResultRepository, WorkingResultRepository>();
            serviceCollection.AddScoped<IOnDutyCommandRepository, OnDutyCommandRepository>();
            serviceCollection.AddScoped<IWorkingResultSyntheticRepository, WorkingResultSyntheticRepository>();
            serviceCollection.AddScoped<IWorkingScheduleAnnouncedRepository, WorkingScheduleAnnouncedRepository>();
            serviceCollection.AddScoped<IViewEmployeeRepository, EmployeeRepository>();
            serviceCollection.AddScoped<IRankRepository, RankRepository>();
            serviceCollection.AddScoped<IPositionRepository, PositionRepository>();
            serviceCollection.AddScoped<IAcademicRankRepository, AcademicRankRepository>();
            serviceCollection.AddScoped<IDegreeRepository, DegreeRepository>();
            serviceCollection.AddScoped<ICountryRepository, CountryRepository>();
            serviceCollection.AddScoped<IProvinceRepository, ProvinceRepository>();
            serviceCollection.AddScoped<IDistrictRepository, DistrictRepository>();
            serviceCollection.AddScoped<IWardRepository, WardRepository>();
            serviceCollection.AddScoped<IDecisionLevelRepository, DecisionLevelRepository>();
            serviceCollection.AddScoped<IRewardTypeRepository, RewardTypeRepository>();
            serviceCollection.AddScoped<IDisciplineTypeRepository, DisciplineTypeRepository>();
            serviceCollection.AddScoped<IEducationLevelRepository, EducationLevelRepository>();
            serviceCollection.AddScoped<IJournalTypeRepository, JournalTypeRepository>();
            serviceCollection.AddScoped<IJournalGroupRepository, JournalGroupRepository>();
            serviceCollection.AddScoped<IJournalRepository, JournalRepository>();
            serviceCollection.AddScoped<ISoSRepository, SoSRepository>();
            serviceCollection.AddScoped<IWorkingScheduleResultRepository, WorkingScheduleResultRepository>();
            serviceCollection.AddScoped<IScheduleRepository, ScheduleRepository>();
            #endregion

            #region Service
            serviceCollection.AddScoped<IDepartmentServices, DepartmentServices>();
            serviceCollection.AddScoped<IWorkingScheduleService, WorkingScheduleService>();
            serviceCollection.AddScoped<IEmployeeCService, EmployeeCService>();
            serviceCollection.AddScoped<IAdvertisementService, AdvertisementService>();
            serviceCollection.AddScoped<IWorkingResultService, WorkingResultService>();
            serviceCollection.AddScoped<IOnDutyCommandService, OnDutyCommandService>();
            serviceCollection.AddScoped<IWorkingResultSyntheticService, WorkingResultSyntheticService>();
            serviceCollection.AddScoped<IOrganizationUnitService, OrganizationUnitService>();
            serviceCollection.AddScoped<IViewEmployeeService, EmployeeService>();
            serviceCollection.AddScoped<IRankService, RankService>();
            serviceCollection.AddScoped<IPositionService, PositionService>();
            serviceCollection.AddScoped<IAcademicRankService, AcademicRankService>();
            serviceCollection.AddScoped<IDegreeService, DegreeService>();
            serviceCollection.AddScoped<ICountryService, CountryService>();
            serviceCollection.AddScoped<IProvinceService, ProvinceService>();
            serviceCollection.AddScoped<IDistrictService, DistrictService>();
            serviceCollection.AddScoped<IWardService, WardService>();
            serviceCollection.AddScoped<IDecisionLevelService, DecisionLevelService>();
            serviceCollection.AddScoped<IRewardTypeService, RewardTypeService>();
            serviceCollection.AddScoped<IDisciplineTypeService, DisciplineTypeService>();
            serviceCollection.AddScoped<IEducationLevelService, EducationLevelService>();
            serviceCollection.AddScoped<IJournalTypeService, JournalTypeService>();
            serviceCollection.AddScoped<IJournalGroupService, JournalGroupService>();
            serviceCollection.AddScoped<IJournalService, JournalService>();
            serviceCollection.AddScoped<ISoSService, SoSService>();
            serviceCollection.AddScoped<IWorkingScheduleResultService, WorkingScheduleResultService>();
            serviceCollection.AddScoped<IFileService, FileService>();
            serviceCollection.AddScoped<IReportProductService, ReportProductService>();
            serviceCollection.AddScoped<IScheduleService, ScheduleService>();
            #endregion
        }
    }
}
