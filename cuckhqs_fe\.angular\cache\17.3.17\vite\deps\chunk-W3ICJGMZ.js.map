{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-tooltip.mjs"], "sourcesContent": ["import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, booleanAttribute, numberAttribute, Directive, Inject, Input, NgModule } from '@angular/core';\nimport { <PERSON>Handler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nclass Tooltip {\n  platformId;\n  el;\n  zone;\n  config;\n  renderer;\n  viewContainer;\n  /**\n   * Position of the tooltip.\n   * @group Props\n   */\n  tooltipPosition;\n  /**\n   * Event to show the tooltip.\n   * @group Props\n   */\n  tooltipEvent = 'hover';\n  /**\n   *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Type of CSS position.\n   * @group Props\n   */\n  positionStyle;\n  /**\n   * Style class of the tooltip.\n   * @group Props\n   */\n  tooltipStyleClass;\n  /**\n   * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n   * @group Props\n   */\n  tooltipZIndex;\n  /**\n   * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n   * @group Props\n   */\n  escape = true;\n  /**\n   * Delay to show the tooltip in milliseconds.\n   * @group Props\n   */\n  showDelay;\n  /**\n   * Delay to hide the tooltip in milliseconds.\n   * @group Props\n   */\n  hideDelay;\n  /**\n   * Time to wait in milliseconds to hide the tooltip even it is active.\n   * @group Props\n   */\n  life;\n  /**\n   * Specifies the additional vertical offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionTop;\n  /**\n   * Specifies the additional horizontal offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionLeft;\n  /**\n   * Whether to hide tooltip when hovering over tooltip content.\n   * @group Props\n   */\n  autoHide = true;\n  /**\n   * Automatically adjusts the element position when there is not enough space on the selected position.\n   * @group Props\n   */\n  fitContent = true;\n  /**\n   * Whether to hide tooltip on escape key press.\n   * @group Props\n   */\n  hideOnEscape = true;\n  /**\n   * Content of the tooltip.\n   * @group Props\n   */\n  content;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @defaultValue false\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n    this.deactivate();\n  }\n  /**\n   * Specifies the tooltip configuration options for the component.\n   * @group Props\n   */\n  tooltipOptions;\n  _tooltipOptions = {\n    tooltipLabel: null,\n    tooltipPosition: 'right',\n    tooltipEvent: 'hover',\n    appendTo: 'body',\n    positionStyle: null,\n    tooltipStyleClass: null,\n    tooltipZIndex: 'auto',\n    escape: true,\n    disabled: null,\n    showDelay: null,\n    hideDelay: null,\n    positionTop: null,\n    positionLeft: null,\n    life: null,\n    autoHide: true,\n    hideOnEscape: true,\n    id: UniqueComponentId() + '_tooltip'\n  };\n  _disabled;\n  container;\n  styleClass;\n  tooltipText;\n  showTimeout;\n  hideTimeout;\n  active;\n  mouseEnterListener;\n  mouseLeaveListener;\n  containerMouseleaveListener;\n  clickListener;\n  focusListener;\n  blurListener;\n  documentEscapeListener;\n  scrollHandler;\n  resizeListener;\n  interactionInProgress = false;\n  constructor(platformId, el, zone, config, renderer, viewContainer) {\n    this.platformId = platformId;\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n    this.renderer = renderer;\n    this.viewContainer = viewContainer;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        const tooltipEvent = this.getOption('tooltipEvent');\n        if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n          this.mouseEnterListener = this.onMouseEnter.bind(this);\n          this.mouseLeaveListener = this.onMouseLeave.bind(this);\n          this.clickListener = this.onInputClick.bind(this);\n          this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n          this.el.nativeElement.addEventListener('click', this.clickListener);\n          this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n        }\n        if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n          this.focusListener = this.onFocus.bind(this);\n          this.blurListener = this.onBlur.bind(this);\n          let target = this.el.nativeElement.querySelector('.p-component');\n          if (!target) {\n            target = this.getTarget(this.el.nativeElement);\n          }\n          target.addEventListener('focus', this.focusListener);\n          target.addEventListener('blur', this.blurListener);\n        }\n      });\n    }\n  }\n  setAriaDescribedBy() {\n    const tooltipId = this.getOption('id');\n    if (tooltipId && this.active) {\n      this.renderer.setAttribute(this.el.nativeElement, 'aria-describedby', tooltipId);\n    }\n  }\n  removeAriaDescribedBy() {\n    this.renderer.removeAttribute(this.el.nativeElement, 'aria-describedby');\n  }\n  ngOnChanges(simpleChange) {\n    if (simpleChange.tooltipPosition) {\n      this.setOption({\n        tooltipPosition: simpleChange.tooltipPosition.currentValue\n      });\n    }\n    if (simpleChange.tooltipEvent) {\n      this.setOption({\n        tooltipEvent: simpleChange.tooltipEvent.currentValue\n      });\n    }\n    if (simpleChange.appendTo) {\n      this.setOption({\n        appendTo: simpleChange.appendTo.currentValue\n      });\n    }\n    if (simpleChange.positionStyle) {\n      this.setOption({\n        positionStyle: simpleChange.positionStyle.currentValue\n      });\n    }\n    if (simpleChange.tooltipStyleClass) {\n      this.setOption({\n        tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n      });\n    }\n    if (simpleChange.tooltipZIndex) {\n      this.setOption({\n        tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n      });\n    }\n    if (simpleChange.escape) {\n      this.setOption({\n        escape: simpleChange.escape.currentValue\n      });\n    }\n    if (simpleChange.showDelay) {\n      this.setOption({\n        showDelay: simpleChange.showDelay.currentValue\n      });\n    }\n    if (simpleChange.hideDelay) {\n      this.setOption({\n        hideDelay: simpleChange.hideDelay.currentValue\n      });\n    }\n    if (simpleChange.life) {\n      this.setOption({\n        life: simpleChange.life.currentValue\n      });\n    }\n    if (simpleChange.positionTop) {\n      this.setOption({\n        positionTop: simpleChange.positionTop.currentValue\n      });\n    }\n    if (simpleChange.positionLeft) {\n      this.setOption({\n        positionLeft: simpleChange.positionLeft.currentValue\n      });\n    }\n    if (simpleChange.disabled) {\n      this.setOption({\n        disabled: simpleChange.disabled.currentValue\n      });\n    }\n    if (simpleChange.content) {\n      this.setOption({\n        tooltipLabel: simpleChange.content.currentValue\n      });\n      if (this.active) {\n        if (simpleChange.content.currentValue) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n    if (simpleChange.autoHide) {\n      this.setOption({\n        autoHide: simpleChange.autoHide.currentValue\n      });\n    }\n    if (simpleChange.id) {\n      this.setOption({\n        id: simpleChange.id.currentValue\n      });\n    }\n    if (simpleChange.tooltipOptions) {\n      this._tooltipOptions = {\n        ...this._tooltipOptions,\n        ...simpleChange.tooltipOptions.currentValue\n      };\n      this.deactivate();\n      if (this.active) {\n        if (this.getOption('tooltipLabel')) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n  }\n  isAutoHide() {\n    return this.getOption('autoHide');\n  }\n  onMouseEnter(e) {\n    if (!this.container && !this.showTimeout) {\n      this.activate();\n    }\n  }\n  onMouseLeave(e) {\n    if (!this.isAutoHide()) {\n      const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n      !valid && this.deactivate();\n    } else {\n      this.deactivate();\n    }\n  }\n  onFocus(e) {\n    this.activate();\n  }\n  onBlur(e) {\n    this.deactivate();\n  }\n  onInputClick(e) {\n    this.deactivate();\n  }\n  activate() {\n    if (!this.interactionInProgress) {\n      this.active = true;\n      this.clearHideTimeout();\n      if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n        this.show();\n      }, this.getOption('showDelay'));else this.show();\n      if (this.getOption('life')) {\n        let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n        this.hideTimeout = setTimeout(() => {\n          this.hide();\n        }, duration);\n      }\n      if (this.getOption('hideOnEscape')) {\n        this.documentEscapeListener = this.renderer.listen('document', 'keydown.escape', () => {\n          this.deactivate();\n          this.documentEscapeListener();\n        });\n      }\n    }\n    this.interactionInProgress = true;\n  }\n  deactivate() {\n    this.interactionInProgress = false;\n    this.active = false;\n    this.clearShowTimeout();\n    if (this.getOption('hideDelay')) {\n      this.clearHideTimeout(); //life timeout\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, this.getOption('hideDelay'));\n    } else {\n      this.hide();\n    }\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n    }\n  }\n  create() {\n    if (this.container) {\n      this.clearHideTimeout();\n      this.remove();\n    }\n    this.container = document.createElement('div');\n    this.container.setAttribute('id', this.getOption('id'));\n    this.container.setAttribute('role', 'tooltip');\n    let tooltipArrow = document.createElement('div');\n    tooltipArrow.className = 'p-tooltip-arrow';\n    this.container.appendChild(tooltipArrow);\n    this.tooltipText = document.createElement('div');\n    this.tooltipText.className = 'p-tooltip-text';\n    this.updateText();\n    if (this.getOption('positionStyle')) {\n      this.container.style.position = this.getOption('positionStyle');\n    }\n    this.container.appendChild(this.tooltipText);\n    if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n    this.container.style.display = 'inline-block';\n    if (this.fitContent) {\n      this.container.style.width = 'fit-content';\n    }\n    if (this.isAutoHide()) {\n      this.container.style.pointerEvents = 'none';\n    } else {\n      this.container.style.pointerEvents = 'unset';\n      this.bindContainerMouseleaveListener();\n    }\n    this.setAriaDescribedBy();\n  }\n  bindContainerMouseleaveListener() {\n    if (!this.containerMouseleaveListener) {\n      const targetEl = this.container ?? this.container.nativeElement;\n      this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', e => {\n        this.deactivate();\n      });\n    }\n  }\n  unbindContainerMouseleaveListener() {\n    if (this.containerMouseleaveListener) {\n      this.bindContainerMouseleaveListener();\n      this.containerMouseleaveListener = null;\n    }\n  }\n  show() {\n    if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n      return;\n    }\n    this.create();\n    const nativeElement = this.el.nativeElement;\n    const pDialogWrapper = nativeElement.closest('p-dialog');\n    if (pDialogWrapper) {\n      setTimeout(() => {\n        this.container && this.align();\n      }, 100);\n    } else {\n      this.align();\n    }\n    DomHandler.fadeIn(this.container, 250);\n    if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n  hide() {\n    if (this.getOption('tooltipZIndex') === 'auto') {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n  }\n  updateText() {\n    const content = this.getOption('tooltipLabel');\n    if (content instanceof TemplateRef) {\n      const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n      embeddedViewRef.detectChanges();\n      embeddedViewRef.rootNodes.forEach(node => this.tooltipText.appendChild(node));\n    } else if (this.getOption('escape')) {\n      this.tooltipText.innerHTML = '';\n      this.tooltipText.appendChild(document.createTextNode(content));\n    } else {\n      this.tooltipText.innerHTML = content;\n    }\n  }\n  align() {\n    let position = this.getOption('tooltipPosition');\n    switch (position) {\n      case 'top':\n        this.alignTop();\n        if (this.isOutOfBounds()) {\n          this.alignBottom();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'bottom':\n        this.alignBottom();\n        if (this.isOutOfBounds()) {\n          this.alignTop();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'left':\n        this.alignLeft();\n        if (this.isOutOfBounds()) {\n          this.alignRight();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n      case 'right':\n        this.alignRight();\n        if (this.isOutOfBounds()) {\n          this.alignLeft();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n    }\n  }\n  getHostOffset() {\n    if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n      let offset = this.el.nativeElement.getBoundingClientRect();\n      let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n      let targetTop = offset.top + DomHandler.getWindowScrollTop();\n      return {\n        left: targetLeft,\n        top: targetTop\n      };\n    } else {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  alignRight() {\n    this.preAlign('right');\n    const el = this.activeElement;\n    const hostOffset = this.getHostOffset();\n    const left = hostOffset.left + DomHandler.getOuterWidth(el);\n    const top = hostOffset.top + (DomHandler.getOuterHeight(el) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  get activeElement() {\n    return this.el.nativeElement.nodeName.includes('P-') ? DomHandler.findSingle(this.el.nativeElement, '.p-component') || this.el.nativeElement : this.el.nativeElement;\n  }\n  alignLeft() {\n    this.preAlign('left');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignTop() {\n    this.preAlign('top');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignBottom() {\n    this.preAlign('bottom');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  setOption(option) {\n    this._tooltipOptions = {\n      ...this._tooltipOptions,\n      ...option\n    };\n  }\n  getOption(option) {\n    return this._tooltipOptions[option];\n  }\n  getTarget(el) {\n    return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n  }\n  preAlign(position) {\n    this.container.style.left = -999 + 'px';\n    this.container.style.top = -999 + 'px';\n    let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n    this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n  }\n  isOutOfBounds() {\n    let offset = this.container.getBoundingClientRect();\n    let targetTop = offset.top;\n    let targetLeft = offset.left;\n    let width = DomHandler.getOuterWidth(this.container);\n    let height = DomHandler.getOuterHeight(this.container);\n    let viewport = DomHandler.getViewport();\n    return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n  }\n  onWindowResize(e) {\n    this.hide();\n  }\n  bindDocumentResizeListener() {\n    this.zone.runOutsideAngular(() => {\n      this.resizeListener = this.onWindowResize.bind(this);\n      window.addEventListener('resize', this.resizeListener);\n    });\n  }\n  unbindDocumentResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n        if (this.container) {\n          this.hide();\n        }\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  unbindEvents() {\n    const tooltipEvent = this.getOption('tooltipEvent');\n    if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n      this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n      this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n      this.el.nativeElement.removeEventListener('click', this.clickListener);\n    }\n    if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n      let target = this.el.nativeElement.querySelector('.p-component');\n      if (!target) {\n        target = this.getTarget(this.el.nativeElement);\n      }\n    }\n    this.unbindDocumentResizeListener();\n  }\n  remove() {\n    if (this.container && this.container.parentElement) {\n      if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n    }\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.unbindContainerMouseleaveListener();\n    this.clearTimeouts();\n    this.removeAriaDescribedBy();\n    this.container = null;\n    this.scrollHandler = null;\n  }\n  clearShowTimeout() {\n    if (this.showTimeout) {\n      clearTimeout(this.showTimeout);\n      this.showTimeout = null;\n    }\n  }\n  clearHideTimeout() {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n  }\n  clearTimeouts() {\n    this.clearShowTimeout();\n    this.clearHideTimeout();\n  }\n  ngOnDestroy() {\n    this.unbindEvents();\n    if (this.container) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n    }\n  }\n  static ɵfac = function Tooltip_Factory(t) {\n    return new (t || Tooltip)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Tooltip,\n    selectors: [[\"\", \"pTooltip\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      tooltipPosition: \"tooltipPosition\",\n      tooltipEvent: \"tooltipEvent\",\n      appendTo: \"appendTo\",\n      positionStyle: \"positionStyle\",\n      tooltipStyleClass: \"tooltipStyleClass\",\n      tooltipZIndex: \"tooltipZIndex\",\n      escape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"escape\", \"escape\", booleanAttribute],\n      showDelay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showDelay\", \"showDelay\", numberAttribute],\n      hideDelay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideDelay\", \"hideDelay\", numberAttribute],\n      life: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"life\", \"life\", numberAttribute],\n      positionTop: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"positionTop\", \"positionTop\", numberAttribute],\n      positionLeft: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"positionLeft\", \"positionLeft\", numberAttribute],\n      autoHide: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoHide\", \"autoHide\", booleanAttribute],\n      fitContent: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"fitContent\", \"fitContent\", booleanAttribute],\n      hideOnEscape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideOnEscape\", \"hideOnEscape\", booleanAttribute],\n      content: [i0.ɵɵInputFlags.None, \"pTooltip\", \"content\"],\n      disabled: [i0.ɵɵInputFlags.None, \"tooltipDisabled\", \"disabled\"],\n      tooltipOptions: \"tooltipOptions\"\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[pTooltip]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.PrimeNGConfig\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipEvent: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    positionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    tooltipZIndex: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    hideDelay: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    life: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    positionTop: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    positionLeft: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    autoHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fitContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideOnEscape: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    content: [{\n      type: Input,\n      args: ['pTooltip']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['tooltipDisabled']\n    }],\n    tooltipOptions: [{\n      type: Input\n    }]\n  });\n})();\nclass TooltipModule {\n  static ɵfac = function TooltipModule_Factory(t) {\n    return new (t || TooltipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TooltipModule,\n    declarations: [Tooltip],\n    imports: [CommonModule],\n    exports: [Tooltip]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tooltip],\n      declarations: [Tooltip]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,kBAAkB;AAAA,IAChB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc;AAAA,IACd,IAAI,kBAAkB,IAAI;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB,YAAY,YAAY,IAAI,MAAM,QAAQ,UAAU,eAAe;AACjE,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,KAAK,kBAAkB,MAAM;AAChC,cAAM,eAAe,KAAK,UAAU,cAAc;AAClD,YAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,eAAK,qBAAqB,KAAK,aAAa,KAAK,IAAI;AACrD,eAAK,qBAAqB,KAAK,aAAa,KAAK,IAAI;AACrD,eAAK,gBAAgB,KAAK,aAAa,KAAK,IAAI;AAChD,eAAK,GAAG,cAAc,iBAAiB,cAAc,KAAK,kBAAkB;AAC5E,eAAK,GAAG,cAAc,iBAAiB,SAAS,KAAK,aAAa;AAClE,eAAK,GAAG,cAAc,iBAAiB,cAAc,KAAK,kBAAkB;AAAA,QAC9E;AACA,YAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,eAAK,gBAAgB,KAAK,QAAQ,KAAK,IAAI;AAC3C,eAAK,eAAe,KAAK,OAAO,KAAK,IAAI;AACzC,cAAI,SAAS,KAAK,GAAG,cAAc,cAAc,cAAc;AAC/D,cAAI,CAAC,QAAQ;AACX,qBAAS,KAAK,UAAU,KAAK,GAAG,aAAa;AAAA,UAC/C;AACA,iBAAO,iBAAiB,SAAS,KAAK,aAAa;AACnD,iBAAO,iBAAiB,QAAQ,KAAK,YAAY;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,KAAK,UAAU,IAAI;AACrC,QAAI,aAAa,KAAK,QAAQ;AAC5B,WAAK,SAAS,aAAa,KAAK,GAAG,eAAe,oBAAoB,SAAS;AAAA,IACjF;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,SAAK,SAAS,gBAAgB,KAAK,GAAG,eAAe,kBAAkB;AAAA,EACzE;AAAA,EACA,YAAY,cAAc;AACxB,QAAI,aAAa,iBAAiB;AAChC,WAAK,UAAU;AAAA,QACb,iBAAiB,aAAa,gBAAgB;AAAA,MAChD,CAAC;AAAA,IACH;AACA,QAAI,aAAa,cAAc;AAC7B,WAAK,UAAU;AAAA,QACb,cAAc,aAAa,aAAa;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,QAAI,aAAa,UAAU;AACzB,WAAK,UAAU;AAAA,QACb,UAAU,aAAa,SAAS;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,eAAe;AAC9B,WAAK,UAAU;AAAA,QACb,eAAe,aAAa,cAAc;AAAA,MAC5C,CAAC;AAAA,IACH;AACA,QAAI,aAAa,mBAAmB;AAClC,WAAK,UAAU;AAAA,QACb,mBAAmB,aAAa,kBAAkB;AAAA,MACpD,CAAC;AAAA,IACH;AACA,QAAI,aAAa,eAAe;AAC9B,WAAK,UAAU;AAAA,QACb,eAAe,aAAa,cAAc;AAAA,MAC5C,CAAC;AAAA,IACH;AACA,QAAI,aAAa,QAAQ;AACvB,WAAK,UAAU;AAAA,QACb,QAAQ,aAAa,OAAO;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,QAAI,aAAa,WAAW;AAC1B,WAAK,UAAU;AAAA,QACb,WAAW,aAAa,UAAU;AAAA,MACpC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,WAAW;AAC1B,WAAK,UAAU;AAAA,QACb,WAAW,aAAa,UAAU;AAAA,MACpC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,MAAM;AACrB,WAAK,UAAU;AAAA,QACb,MAAM,aAAa,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,QAAI,aAAa,aAAa;AAC5B,WAAK,UAAU;AAAA,QACb,aAAa,aAAa,YAAY;AAAA,MACxC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,cAAc;AAC7B,WAAK,UAAU;AAAA,QACb,cAAc,aAAa,aAAa;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,QAAI,aAAa,UAAU;AACzB,WAAK,UAAU;AAAA,QACb,UAAU,aAAa,SAAS;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,SAAS;AACxB,WAAK,UAAU;AAAA,QACb,cAAc,aAAa,QAAQ;AAAA,MACrC,CAAC;AACD,UAAI,KAAK,QAAQ;AACf,YAAI,aAAa,QAAQ,cAAc;AACrC,cAAI,KAAK,aAAa,KAAK,UAAU,cAAc;AACjD,iBAAK,WAAW;AAChB,iBAAK,MAAM;AAAA,UACb,OAAO;AACL,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,OAAO;AACL,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa,UAAU;AACzB,WAAK,UAAU;AAAA,QACb,UAAU,aAAa,SAAS;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,aAAa,IAAI;AACnB,WAAK,UAAU;AAAA,QACb,IAAI,aAAa,GAAG;AAAA,MACtB,CAAC;AAAA,IACH;AACA,QAAI,aAAa,gBAAgB;AAC/B,WAAK,kBAAkB,kCAClB,KAAK,kBACL,aAAa,eAAe;AAEjC,WAAK,WAAW;AAChB,UAAI,KAAK,QAAQ;AACf,YAAI,KAAK,UAAU,cAAc,GAAG;AAClC,cAAI,KAAK,aAAa,KAAK,UAAU,cAAc;AACjD,iBAAK,WAAW;AAChB,iBAAK,MAAM;AAAA,UACb,OAAO;AACL,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,OAAO;AACL,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK,UAAU,UAAU;AAAA,EAClC;AAAA,EACA,aAAa,GAAG;AACd,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,aAAa;AACxC,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,aAAa,GAAG;AACd,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,YAAM,QAAQ,WAAW,SAAS,EAAE,eAAe,WAAW,KAAK,WAAW,SAAS,EAAE,eAAe,gBAAgB,KAAK,WAAW,SAAS,EAAE,eAAe,iBAAiB;AACnL,OAAC,SAAS,KAAK,WAAW;AAAA,IAC5B,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AACT,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,GAAG;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa,GAAG;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,uBAAuB;AAC/B,WAAK,SAAS;AACd,WAAK,iBAAiB;AACtB,UAAI,KAAK,UAAU,WAAW,EAAG,MAAK,cAAc,WAAW,MAAM;AACnE,aAAK,KAAK;AAAA,MACZ,GAAG,KAAK,UAAU,WAAW,CAAC;AAAA,UAAO,MAAK,KAAK;AAC/C,UAAI,KAAK,UAAU,MAAM,GAAG;AAC1B,YAAI,WAAW,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,MAAM;AACzH,aAAK,cAAc,WAAW,MAAM;AAClC,eAAK,KAAK;AAAA,QACZ,GAAG,QAAQ;AAAA,MACb;AACA,UAAI,KAAK,UAAU,cAAc,GAAG;AAClC,aAAK,yBAAyB,KAAK,SAAS,OAAO,YAAY,kBAAkB,MAAM;AACrF,eAAK,WAAW;AAChB,eAAK,uBAAuB;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,aAAa;AACX,SAAK,wBAAwB;AAC7B,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,iBAAiB;AACtB,WAAK,cAAc,WAAW,MAAM;AAClC,aAAK,KAAK;AAAA,MACZ,GAAG,KAAK,UAAU,WAAW,CAAC;AAAA,IAChC,OAAO;AACL,WAAK,KAAK;AAAA,IACZ;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,KAAK,WAAW;AAClB,WAAK,iBAAiB;AACtB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,SAAK,UAAU,aAAa,MAAM,KAAK,UAAU,IAAI,CAAC;AACtD,SAAK,UAAU,aAAa,QAAQ,SAAS;AAC7C,QAAI,eAAe,SAAS,cAAc,KAAK;AAC/C,iBAAa,YAAY;AACzB,SAAK,UAAU,YAAY,YAAY;AACvC,SAAK,cAAc,SAAS,cAAc,KAAK;AAC/C,SAAK,YAAY,YAAY;AAC7B,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU,eAAe,GAAG;AACnC,WAAK,UAAU,MAAM,WAAW,KAAK,UAAU,eAAe;AAAA,IAChE;AACA,SAAK,UAAU,YAAY,KAAK,WAAW;AAC3C,QAAI,KAAK,UAAU,UAAU,MAAM,OAAQ,UAAS,KAAK,YAAY,KAAK,SAAS;AAAA,aAAW,KAAK,UAAU,UAAU,MAAM,SAAU,YAAW,YAAY,KAAK,WAAW,KAAK,GAAG,aAAa;AAAA,QAAO,YAAW,YAAY,KAAK,WAAW,KAAK,UAAU,UAAU,CAAC;AAC3Q,SAAK,UAAU,MAAM,UAAU;AAC/B,QAAI,KAAK,YAAY;AACnB,WAAK,UAAU,MAAM,QAAQ;AAAA,IAC/B;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,UAAU,MAAM,gBAAgB;AAAA,IACvC,OAAO;AACL,WAAK,UAAU,MAAM,gBAAgB;AACrC,WAAK,gCAAgC;AAAA,IACvC;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAChC,QAAI,CAAC,KAAK,6BAA6B;AACrC,YAAM,WAAW,KAAK,aAAa,KAAK,UAAU;AAClD,WAAK,8BAA8B,KAAK,SAAS,OAAO,UAAU,cAAc,OAAK;AACnF,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,oCAAoC;AAClC,QAAI,KAAK,6BAA6B;AACpC,WAAK,gCAAgC;AACrC,WAAK,8BAA8B;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,UAAU,cAAc,KAAK,KAAK,UAAU,UAAU,GAAG;AACjE;AAAA,IACF;AACA,SAAK,OAAO;AACZ,UAAM,gBAAgB,KAAK,GAAG;AAC9B,UAAM,iBAAiB,cAAc,QAAQ,UAAU;AACvD,QAAI,gBAAgB;AAClB,iBAAW,MAAM;AACf,aAAK,aAAa,KAAK,MAAM;AAAA,MAC/B,GAAG,GAAG;AAAA,IACR,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AACA,eAAW,OAAO,KAAK,WAAW,GAAG;AACrC,QAAI,KAAK,UAAU,eAAe,MAAM,OAAQ,aAAY,IAAI,WAAW,KAAK,WAAW,KAAK,OAAO,OAAO,OAAO;AAAA,QAAO,MAAK,UAAU,MAAM,SAAS,KAAK,UAAU,eAAe;AACxL,SAAK,2BAA2B;AAChC,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,QAAI,KAAK,UAAU,eAAe,MAAM,QAAQ;AAC9C,kBAAY,MAAM,KAAK,SAAS;AAAA,IAClC;AACA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,aAAa;AACX,UAAM,UAAU,KAAK,UAAU,cAAc;AAC7C,QAAI,mBAAmB,aAAa;AAClC,YAAM,kBAAkB,KAAK,cAAc,mBAAmB,OAAO;AACrE,sBAAgB,cAAc;AAC9B,sBAAgB,UAAU,QAAQ,UAAQ,KAAK,YAAY,YAAY,IAAI,CAAC;AAAA,IAC9E,WAAW,KAAK,UAAU,QAAQ,GAAG;AACnC,WAAK,YAAY,YAAY;AAC7B,WAAK,YAAY,YAAY,SAAS,eAAe,OAAO,CAAC;AAAA,IAC/D,OAAO;AACL,WAAK,YAAY,YAAY;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,QAAI,WAAW,KAAK,UAAU,iBAAiB;AAC/C,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,aAAK,SAAS;AACd,YAAI,KAAK,cAAc,GAAG;AACxB,eAAK,YAAY;AACjB,cAAI,KAAK,cAAc,GAAG;AACxB,iBAAK,WAAW;AAChB,gBAAI,KAAK,cAAc,GAAG;AACxB,mBAAK,UAAU;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,aAAK,YAAY;AACjB,YAAI,KAAK,cAAc,GAAG;AACxB,eAAK,SAAS;AACd,cAAI,KAAK,cAAc,GAAG;AACxB,iBAAK,WAAW;AAChB,gBAAI,KAAK,cAAc,GAAG;AACxB,mBAAK,UAAU;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,aAAK,UAAU;AACf,YAAI,KAAK,cAAc,GAAG;AACxB,eAAK,WAAW;AAChB,cAAI,KAAK,cAAc,GAAG;AACxB,iBAAK,SAAS;AACd,gBAAI,KAAK,cAAc,GAAG;AACxB,mBAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH,aAAK,WAAW;AAChB,YAAI,KAAK,cAAc,GAAG;AACxB,eAAK,UAAU;AACf,cAAI,KAAK,cAAc,GAAG;AACxB,iBAAK,SAAS;AACd,gBAAI,KAAK,cAAc,GAAG;AACxB,mBAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,UAAU,UAAU,MAAM,UAAU,KAAK,UAAU,UAAU,MAAM,UAAU;AACpF,UAAI,SAAS,KAAK,GAAG,cAAc,sBAAsB;AACzD,UAAI,aAAa,OAAO,OAAO,WAAW,oBAAoB;AAC9D,UAAI,YAAY,OAAO,MAAM,WAAW,mBAAmB;AAC3D,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,SAAS,OAAO;AACrB,UAAM,KAAK,KAAK;AAChB,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,OAAO,WAAW,OAAO,WAAW,cAAc,EAAE;AAC1D,UAAM,MAAM,WAAW,OAAO,WAAW,eAAe,EAAE,IAAI,WAAW,eAAe,KAAK,SAAS,KAAK;AAC3G,SAAK,UAAU,MAAM,OAAO,OAAO,KAAK,UAAU,cAAc,IAAI;AACpE,SAAK,UAAU,MAAM,MAAM,MAAM,KAAK,UAAU,aAAa,IAAI;AAAA,EACnE;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,GAAG,cAAc,SAAS,SAAS,IAAI,IAAI,WAAW,WAAW,KAAK,GAAG,eAAe,cAAc,KAAK,KAAK,GAAG,gBAAgB,KAAK,GAAG;AAAA,EACzJ;AAAA,EACA,YAAY;AACV,SAAK,SAAS,MAAM;AACpB,QAAI,aAAa,KAAK,cAAc;AACpC,QAAI,OAAO,WAAW,OAAO,WAAW,cAAc,KAAK,SAAS;AACpE,QAAI,MAAM,WAAW,OAAO,WAAW,eAAe,KAAK,GAAG,aAAa,IAAI,WAAW,eAAe,KAAK,SAAS,KAAK;AAC5H,SAAK,UAAU,MAAM,OAAO,OAAO,KAAK,UAAU,cAAc,IAAI;AACpE,SAAK,UAAU,MAAM,MAAM,MAAM,KAAK,UAAU,aAAa,IAAI;AAAA,EACnE;AAAA,EACA,WAAW;AACT,SAAK,SAAS,KAAK;AACnB,QAAI,aAAa,KAAK,cAAc;AACpC,QAAI,OAAO,WAAW,QAAQ,WAAW,cAAc,KAAK,GAAG,aAAa,IAAI,WAAW,cAAc,KAAK,SAAS,KAAK;AAC5H,QAAI,MAAM,WAAW,MAAM,WAAW,eAAe,KAAK,SAAS;AACnE,SAAK,UAAU,MAAM,OAAO,OAAO,KAAK,UAAU,cAAc,IAAI;AACpE,SAAK,UAAU,MAAM,MAAM,MAAM,KAAK,UAAU,aAAa,IAAI;AAAA,EACnE;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,QAAQ;AACtB,QAAI,aAAa,KAAK,cAAc;AACpC,QAAI,OAAO,WAAW,QAAQ,WAAW,cAAc,KAAK,GAAG,aAAa,IAAI,WAAW,cAAc,KAAK,SAAS,KAAK;AAC5H,QAAI,MAAM,WAAW,MAAM,WAAW,eAAe,KAAK,GAAG,aAAa;AAC1E,SAAK,UAAU,MAAM,OAAO,OAAO,KAAK,UAAU,cAAc,IAAI;AACpE,SAAK,UAAU,MAAM,MAAM,MAAM,KAAK,UAAU,aAAa,IAAI;AAAA,EACnE;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,kBAAkB,kCAClB,KAAK,kBACL;AAAA,EAEP;AAAA,EACA,UAAU,QAAQ;AAChB,WAAO,KAAK,gBAAgB,MAAM;AAAA,EACpC;AAAA,EACA,UAAU,IAAI;AACZ,WAAO,WAAW,SAAS,IAAI,gBAAgB,IAAI,WAAW,WAAW,IAAI,OAAO,IAAI;AAAA,EAC1F;AAAA,EACA,SAAS,UAAU;AACjB,SAAK,UAAU,MAAM,OAAO;AAC5B,SAAK,UAAU,MAAM,MAAM;AAC3B,QAAI,mBAAmB,qCAAqC;AAC5D,SAAK,UAAU,YAAY,KAAK,UAAU,mBAAmB,IAAI,mBAAmB,MAAM,KAAK,UAAU,mBAAmB,IAAI;AAAA,EAClI;AAAA,EACA,gBAAgB;AACd,QAAI,SAAS,KAAK,UAAU,sBAAsB;AAClD,QAAI,YAAY,OAAO;AACvB,QAAI,aAAa,OAAO;AACxB,QAAI,QAAQ,WAAW,cAAc,KAAK,SAAS;AACnD,QAAI,SAAS,WAAW,eAAe,KAAK,SAAS;AACrD,QAAI,WAAW,WAAW,YAAY;AACtC,WAAO,aAAa,QAAQ,SAAS,SAAS,aAAa,KAAK,YAAY,KAAK,YAAY,SAAS,SAAS;AAAA,EACjH;AAAA,EACA,eAAe,GAAG;AAChB,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,6BAA6B;AAC3B,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,aAAO,iBAAiB,UAAU,KAAK,cAAc;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,gBAAgB;AACvB,aAAO,oBAAoB,UAAU,KAAK,cAAc;AACxD,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB,IAAI,8BAA8B,KAAK,GAAG,eAAe,MAAM;AAClF,YAAI,KAAK,WAAW;AAClB,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,cAAc,mBAAmB;AAAA,EACxC;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,eAAe,KAAK,UAAU,cAAc;AAClD,QAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,WAAK,GAAG,cAAc,oBAAoB,cAAc,KAAK,kBAAkB;AAC/E,WAAK,GAAG,cAAc,oBAAoB,cAAc,KAAK,kBAAkB;AAC/E,WAAK,GAAG,cAAc,oBAAoB,SAAS,KAAK,aAAa;AAAA,IACvE;AACA,QAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,UAAI,SAAS,KAAK,GAAG,cAAc,cAAc,cAAc;AAC/D,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,UAAU,KAAK,GAAG,aAAa;AAAA,MAC/C;AAAA,IACF;AACA,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,SAAS;AACP,QAAI,KAAK,aAAa,KAAK,UAAU,eAAe;AAClD,UAAI,KAAK,UAAU,UAAU,MAAM,OAAQ,UAAS,KAAK,YAAY,KAAK,SAAS;AAAA,eAAW,KAAK,UAAU,UAAU,MAAM,SAAU,MAAK,GAAG,cAAc,YAAY,KAAK,SAAS;AAAA,UAAO,YAAW,YAAY,KAAK,WAAW,KAAK,UAAU,UAAU,CAAC;AAAA,IACjQ;AACA,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAC1B,SAAK,kCAAkC;AACvC,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,aAAa;AACpB,mBAAa,KAAK,WAAW;AAC7B,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,aAAa;AACpB,mBAAa,KAAK,WAAW;AAC7B,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAClB,QAAI,KAAK,WAAW;AAClB,kBAAY,MAAM,KAAK,SAAS;AAAA,IAClC;AACA,SAAK,OAAO;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ;AAC3B,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,GAAG;AACxC,WAAO,KAAK,KAAK,UAAY,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,aAAa,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,EAC1P;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,IAChC,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,QAAQ,CAAI,WAAa,4BAA4B,UAAU,UAAU,gBAAgB;AAAA,MACzF,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,eAAe;AAAA,MACjG,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,eAAe;AAAA,MACjG,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,eAAe;AAAA,MAClF,aAAa,CAAI,WAAa,4BAA4B,eAAe,eAAe,eAAe;AAAA,MACvG,cAAc,CAAI,WAAa,4BAA4B,gBAAgB,gBAAgB,eAAe;AAAA,MAC1G,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MAC/F,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,cAAc,CAAI,WAAa,4BAA4B,gBAAgB,gBAAgB,gBAAgB;AAAA,MAC3G,SAAS,CAAI,WAAa,MAAM,YAAY,SAAS;AAAA,MACrD,UAAU,CAAI,WAAa,MAAM,mBAAmB,UAAU;AAAA,MAC9D,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,EACjE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAe;AAAA,EAClC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,OAAO;AAAA,IACtB,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,OAAO;AAAA,EACnB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,OAAO;AAAA,MACjB,cAAc,CAAC,OAAO;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}