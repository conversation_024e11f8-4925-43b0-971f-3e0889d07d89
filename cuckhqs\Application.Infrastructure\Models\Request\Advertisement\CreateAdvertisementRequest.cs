﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.Advertisement
{
    public class CreateAdvertisementRequest
    {
        public string? AdvertisementCode { set; get; }
        public string? AdvertisementName { set; get; }
        public DateTime? Start { set; get; }
        public int? StartHour { set; get; }
        public int? StartMinute { set; get; }
        public DateTime? Ends { set; get; }
        public int? EndsHour { set; get; }
        public int? EndsMinute { set; get; }
        public int? Year { set; get; }
        public bool? Active { set; get; }

        public static Expression<Func<CreateAdvertisementRequest, AdvertisementEntity>> Expression
        {
            get
            {
                return entity => new AdvertisementEntity
                {
                    AdvertisementCode = entity.AdvertisementCode,
                    AdvertisementName = entity.AdvertisementName,
                    Start = entity.Start,
                    StartHour = entity.StartHour,
                    StartMinute = entity.StartMinute,
                    Ends = entity.Ends,
                    EndsHour = entity.EndsHour,
                    EndsMinute = entity.EndsMinute,
                    Year = entity.Year,
                    Active = entity.Active
                };
            }
        }

        public static AdvertisementEntity Create(CreateAdvertisementRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
