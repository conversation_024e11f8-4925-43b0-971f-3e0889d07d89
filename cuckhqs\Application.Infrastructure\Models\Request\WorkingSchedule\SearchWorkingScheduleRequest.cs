﻿using Application.Infrastructure.Commons;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.WorkingSchedule
{
    public class SearchWorkingScheduleRequest : BaseCriteria
    {
        public Guid employeeId { get; set; }
        public string? isAll {  get; set; }
        public bool isDefault { get; set; }
        public string? organizationUnitId { get; set; }
        public string week {  get; set; }
        public string year { get; set; }
        public string? typeExport { get; set; }
    }
}
