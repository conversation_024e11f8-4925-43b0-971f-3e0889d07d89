﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.OrganizationUnit
{
    public class UpdateOrganizationUnitRequest : CreateOrganizationUnitRequest
    {
        public int Id { get; set; }
    

      public static Expression<Func<UpdateOrganizationUnitRequest, OrganizationUnitEntity>> Expression
        {
            get
            {
                return entity => new OrganizationUnitEntity
                {
                    Id = entity.Id,
                    OrganizationUnitCode = entity.OrganizationUnitCode,
                    Name = entity.OrganizationUnitName,
                    ShortOrganizationUnitName = entity.ShortOrganizationUnitName,
                    ParentId = entity.ParentId,
                    ClassifyGroup = entity.ClassifyGroup,
                    TrainingMaterialCode = entity.TrainingMaterialCode,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static OrganizationUnitEntity Create(UpdateOrganizationUnitRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
