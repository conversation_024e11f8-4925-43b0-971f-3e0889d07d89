﻿using Application.Infrastructure.Models;
using System.Linq.Expressions;

namespace Application.Infrastructure.Models.Request
{
    public class UpdateDepartmentRequest : CreateDepartmentRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateDepartmentRequest, DepartmentEntity>> Expression
        {
            get
            {
                return entity => new DepartmentEntity
                {
                    Id = entity.Id,
                    Name = entity.Name,
                    NameShort = entity.NameShort,
                    Code = entity.Code,
                    SortOrder = entity.SortOrder,
                    Address = entity.Address,
                    ParentId = entity.ParentId,
                    ParentIdCode = entity.ParentIdCode,
                    PhoneNumber = entity.PhoneNumber,
                    DepartmentType = entity.DepartmentType,
                    Description = entity.Description,
                    IsActive = entity.IsActive,
                };
            }
        }

        public static DepartmentEntity Create(UpdateDepartmentRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}