﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.DisciplineType;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.EducationLevel
{
    public class UpdateEducationLevelRequest : CreateEducationLevelRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateEducationLevelRequest, EducationLevelEntity>> Expression
        {
            get
            {
                return entity => new EducationLevelEntity
                {
                    Id = entity.Id,
                    EducationLevelCode = entity.EducationLevelCode,
                    EducationLevelName = entity.EducationLevelName,
                    Class = entity.Class,
                    Active = entity.Active
                };
            }
        }

        public static EducationLevelEntity Create(UpdateEducationLevelRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
