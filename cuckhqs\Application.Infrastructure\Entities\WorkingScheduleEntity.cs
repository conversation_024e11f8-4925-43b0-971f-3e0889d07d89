﻿using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models
{
    public class WorkingScheduleEntity : BaseEntity<int>
    {
        public short? Classify { get; set; }
        public int? OrganizationUnitId { get; set; }
        public bool? Register { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }
        public DateTime? Date { get; set; }
        public string? CoChair { get; set; }
        public short? Time { get; set; }
        public DateTime? TimeFrom { get; set; }
        public DateTime? TimeTo { get; set; }
        public string? Place { get; set; }
        public string? Contents { get; set; }
        public string? Member { get; set; }
        public string? Note { get; set; }
        public bool? Message { get; set; }
        public bool? Active { get; set; }
        public int? OrganizationUnitId_Chair { get; set; }
        //public string? OrganizationUnitIdName { get; set; }
        public List<WorkingScheduleCEntity> WorkingScheduleC { get; set; }
        public List<WorkingScheduleEPHEntity> WorkingScheduleEPH { get; set; }
        public List<WorkingScheduleEPEntity> WorkingScheduleEP { get; set; }
        public List<WorkingScheduleOUEntity> WorkingScheduleOU { get; set; }
        public List<WorkingScheduleResultEntity> WorkingScheduleResult { get; set; }
        //public List<WorkingScheduleResult_AttachDetailEntity> WorkingScheduleResult_AttachDetail { get; set; }
        public int? Announced { get; set; }
        //public int? WorkingScheduleResultId { get; set; }
        //public string? OrganizationUnitId_ChairName { get; set; }
        public string? IPAddress { get; set; }
        //public int? MTEntityState { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public short? SortOrder { get; set; }
    }
}
