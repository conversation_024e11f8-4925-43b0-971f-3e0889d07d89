﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request
{
    public class CreateDepartmentRequest
    {
        public string? Name { get; set; }
        public string? NameShort { get; set; }
        public string? Code { get; set; }
        public int? SortOrder { get; set; }
        public string? Address { get; set; }

        public string? IsActive { get; set; }
        public int? ParentId { get; set; }
        public string? ParentIdCode { get; set; }
        public string? PhoneNumber { get; set; }
        public string? DepartmentType { get; set; }
        public string? Description { get; set; }


        public static Expression<Func<CreateDepartmentRequest, DepartmentEntity>> Expression
        {
            get
            {
                return entity => new DepartmentEntity
                {
                    Name = entity.Name,
                    NameShort = entity.NameShort,
                    Code = entity.Code,
                    SortOrder = entity.SortOrder,
                    Address = entity.Address,
                    ParentId = entity.ParentId,
                    ParentIdCode = entity.ParentIdCode,
                    PhoneNumber = entity.PhoneNumber,
                    DepartmentType = entity.DepartmentType,
                    Description = entity.Description,
                    IsActive = entity.IsActive,
                };
            }
        }

        public static DepartmentEntity Create(CreateDepartmentRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
