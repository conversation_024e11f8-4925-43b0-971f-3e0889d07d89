﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class EducationLevelRepository : GenericRepository<EducationLevelEntity, int>, IEducationLevelRepository
    {
        public AppDbContext Context { get; set; }

        public EducationLevelRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(EducationLevelEntity requestObject, EducationLevelEntity targetObject)
        {
            targetObject.EducationLevelCode = requestObject.EducationLevelCode;
            targetObject.EducationLevelName = requestObject.EducationLevelName;
            targetObject.Active = requestObject.Active;
            targetObject.Class = requestObject.Class;
        }
    }
}
