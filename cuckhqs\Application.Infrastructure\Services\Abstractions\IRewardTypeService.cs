﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.RewardType;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IRewardTypeService
    {
        Task<BaseSearchResponse<RewardTypeResponse>> SearchRewardTypeAsync(SearchRewardTypeRequest request);
        Task<RewardTypeResponse> CreateRewardTypeAsync(CreateRewardTypeRequest request);
        Task<bool> UpdateRewardTypeAsync(UpdateRewardTypeRequest request);
        Task<bool> DeleteRewardTypeAsync(DeleteRewardTypeRequest request);
    }
}
