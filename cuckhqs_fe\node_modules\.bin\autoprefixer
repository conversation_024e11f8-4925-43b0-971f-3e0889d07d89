#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@angular-devkit/build-angular/node_modules/autoprefixer/bin/autoprefixer" "$@"
  ret=$?
else 
  node  "$basedir/../@angular-devkit/build-angular/node_modules/autoprefixer/bin/autoprefixer" "$@"
  ret=$?
fi
exit $ret
