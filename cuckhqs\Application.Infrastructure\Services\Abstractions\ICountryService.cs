﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Country;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface ICountryService
    {
        Task<BaseSearchResponse<CountryResponse>> SearchCountryAsync(SearchCountryRequest request);
        Task<CountryResponse> CreateCountryAsync(CreateCountryRequest request);
        Task<bool> UpdateCountryAsync(UpdateCountryRequest request);
        Task<bool> DeleteCountryAsync(DeleteCountryRequest request);
    }
}
