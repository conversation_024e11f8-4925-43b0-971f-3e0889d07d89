﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Microsoft.VisualBasic;

namespace Application.Infrastructure.Models.Request.Advertisement
{
    public class CreateWorkingResultRequest
    {
        public string? Name { set; get; }
        public string? Week  { set; get; }
        public string? Year { set; get; }
        public string? Class { set; get; }
        public string? DateFrom { set; get; }
        public string? DateTo { set; get; }
        public string? OrganizationUnitId { set; get; }
        public string? Status { set; get; }
        public string? Contents { set; get; }
        public string? Contents1 { set; get; }
        public string? Contents2 { set; get; }
        public string? Contents3 { set; get; }
        public string? Note { set; get; }

        public static Expression<Func<CreateWorkingResultRequest, WorkingResultEntity>> Expression
        {
            get
            {
                return entity => new WorkingResultEntity
                {
                    OrganizationUnitId = Convert.ToInt32(entity.OrganizationUnitId),
                    Name = Convert.ToString(entity.Name),
                    Year = Convert.ToInt32(entity.Year),
                    Week = Convert.ToInt32(entity.Week),
                    Class = Convert.ToInt32(entity.Class),
                    Contents = Convert.ToString(entity.Contents),
                    Contents1 = Convert.ToString(entity.Contents1),
                    Contents2 = Convert.ToString(entity.Contents2),
                    Contents3 = Convert.ToString(entity.Contents3),
                    DateFrom =Convert.ToDateTime( entity.DateFrom),
                    DateTo = Convert.ToDateTime(entity.DateTo),
                    Status = Convert.ToInt32(entity.Status),
                    Note = Convert.ToString(entity.Note),
                };
            }
        }

        public static WorkingResultEntity Create(CreateWorkingResultRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
