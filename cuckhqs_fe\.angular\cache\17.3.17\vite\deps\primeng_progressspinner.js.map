{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-progressspinner.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nclass ProgressSpinner {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Width of the circle stroke.\n   * @group Props\n   */\n  strokeWidth = '2';\n  /**\n   * Color for the background of the circle.\n   * @group Props\n   */\n  fill = 'none';\n  /**\n   * Duration of the rotate animation.\n   * @group Props\n   */\n  animationDuration = '2s';\n  /**\n   * Used to define a aria label attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  static ɵfac = function ProgressSpinner_Factory(t) {\n    return new (t || ProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ProgressSpinner,\n    selectors: [[\"p-progressSpinner\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      strokeWidth: \"strokeWidth\",\n      fill: \"fill\",\n      animationDuration: \"animationDuration\",\n      ariaLabel: \"ariaLabel\"\n    },\n    decls: 3,\n    vars: 11,\n    consts: [[\"role\", \"progressbar\", 1, \"p-progress-spinner\", 3, \"ngStyle\", \"ngClass\"], [\"viewBox\", \"25 25 50 50\", 1, \"p-progress-spinner-svg\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\", 1, \"p-progress-spinner-circle\"]],\n    template: function ProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(1, \"svg\", 1);\n        i0.ɵɵelement(2, \"circle\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.styleClass);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-busy\", true)(\"data-pc-name\", \"progressspinner\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'p-progressSpinner',\n      template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-label]=\"ariaLabel\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    fill: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }]\n  });\n})();\nclass ProgressSpinnerModule {\n  static ɵfac = function ProgressSpinnerModule_Factory(t) {\n    return new (t || ProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ProgressSpinnerModule,\n    declarations: [ProgressSpinner],\n    imports: [CommonModule],\n    exports: [ProgressSpinner]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ProgressSpinner],\n      declarations: [ProgressSpinner]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,WAAW;AAAA,IACb;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,eAAe,GAAG,sBAAsB,GAAG,WAAW,SAAS,GAAG,CAAC,WAAW,eAAe,GAAG,wBAAwB,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,qBAAqB,MAAM,GAAG,2BAA2B,CAAC;AAAA,IAC3O,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,KAAK,EAAE,WAAW,IAAI,UAAU;AAC7D,QAAG,YAAY,cAAc,IAAI,SAAS,EAAE,aAAa,IAAI,EAAE,gBAAgB,iBAAiB,EAAE,mBAAmB,MAAM;AAC3H,QAAG,UAAU;AACb,QAAG,YAAY,sBAAsB,IAAI,iBAAiB;AAC1D,QAAG,YAAY,mBAAmB,MAAM;AACxC,QAAG,UAAU;AACb,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,gBAAgB,IAAI,WAAW;AAAA,MAClE;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,OAAO;AAAA,IACrC,QAAQ,CAAC,i7BAAm7B;AAAA,IAC57B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,i7BAAm7B;AAAA,IAC97B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,GAAG;AACtD,WAAO,KAAK,KAAK,wBAAuB;AAAA,EAC1C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,eAAe;AAAA,IAC9B,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,eAAe;AAAA,MACzB,cAAc,CAAC,eAAe;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}