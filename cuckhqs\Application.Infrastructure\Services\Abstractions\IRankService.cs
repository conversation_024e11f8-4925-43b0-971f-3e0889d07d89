﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IRankService
    {
        Task<BaseSearchResponse<RankResponse>> SearchRankAsync(SearchRankRequest request);
        Task<RankResponse> CreateRankAsync(CreateRankRequest request);
        Task<bool> UpdateRankAsync(UpdateRankRequest request);
        Task<bool> DeleteRankAsync(DeleteRankRequest request);
    }
}
