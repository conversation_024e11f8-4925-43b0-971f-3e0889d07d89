﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Request.Category.Ward;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Services.Abstractions;
using log4net;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Implementations
{
    public class WardService : IWardService
    {
        private readonly IUnitOfWork _unitOfWork;
        //private readonly AppDbContext _dbContext;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);

        public WardService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

        }

        public async Task<BaseSearchResponse<WardResponse>> SearchWardAsync(SearchWardRequest request)
        {
            try
            {
                var province = await _unitOfWork.District.GetAllProvinceAsync();
                var province_Name = province?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.ProvinceName)
                    ?? new Dictionary<string, string>();

                var district = await _unitOfWork.District.GetAllDistrictAsync();
                var district_Name = district?.ToDictionary(e => e.Id.ToString().Trim().ToLower(), e => e.DistrictName)
                    ?? new Dictionary<string, string>();


                IQueryable<WardResponse> query = _unitOfWork.Ward.AsQueryable().AsNoTracking()
                    .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                x.WardCode.Contains(request.keyword) ||
                                x.WardName.Contains(request.keyword) 
                                 && (request.ProvinceId == null || request.ProvinceId == 0 || x.ProvinceId == request.ProvinceId)
                                  && (request.DistrictId == null || request.DistrictId == 0 || x.DistrictId == request.DistrictId)))
                    .Select(s => new WardResponse()
                    {
                        Id = s.Id,
                        ProvinceId = s.ProvinceId,
                        Province_Name = null,
                        DistrictId = s.DistrictId,
                        District_Name = null,
                        WardCode = s.WardCode,
                        WardName = s.WardName,
                        Description = s.Description,
                        Active = s.Active,
                        SortOrder = s.SortOrder,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate,
                        IPAddress = s.IPAddress,
                        ModifiedBy = s.ModifiedBy,
                        CreatedBy = s.CreatedBy,
                    });

                var resultQuery = query.AsEnumerable()
                   .Select(emp =>
                   {
                       emp.Province_Name = province_Name.TryGetValue(emp.ProvinceId?.ToString().Trim() ?? "", out var name) ? name : "";
                       emp.District_Name = district_Name.TryGetValue(emp.DistrictId?.ToString().Trim() ?? "", out var dname) ? dname : "";
                       return emp;
                   })
                   .AsQueryable();
                return await BaseSearchResponse<WardResponse>.GetResponse(resultQuery, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại SearchAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<WardResponse> CreateWardAsync(CreateWardRequest request)
        {
            try
            {
                var entity = CreateWardRequest.Create(request);

                await _unitOfWork.Ward.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return WardResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteWardAsync(DeleteWardRequest request)
        {
            try
            {
                var record = await _unitOfWork.Ward.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Ward.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateWardAsync(UpdateWardRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Ward.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateWardRequest.Create(request);

                await _unitOfWork.Ward.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
