#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@openapitools/openapi-generator-cli/node_modules/glob/dist/esm/bin.mjs" "$@"
  ret=$?
else 
  node  "$basedir/../@openapitools/openapi-generator-cli/node_modules/glob/dist/esm/bin.mjs" "$@"
  ret=$?
fi
exit $ret
