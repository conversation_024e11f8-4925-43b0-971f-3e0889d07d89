﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class CountryResponse
    {
        public int Id { get; set; }
        public string? CountryCode { get; set; }
        public string? CountryName { get; set; }
        public string? LanguageName { get; set; }
        public int? Class { get; set; }
        public int? Year { get; set; }
        public bool? Active { get; set; }
        public short? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public string? VietnameseName { get; set; }

        public static Expression<Func<CountryEntity, CountryResponse>> Expression
        {
            get
            {
                return entity => new CountryResponse()
                {
                    Id = entity.Id,
                    CountryCode = entity.CountryCode,
                    CountryName = entity.CountryName,
                    LanguageName = entity.LanguageName,
                    Class = entity.Class,
                    Year = entity.Year,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy,
                    VietnameseName = entity.VietnameseName,
                };
            }
        }

        public static CountryResponse Create(CountryEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
