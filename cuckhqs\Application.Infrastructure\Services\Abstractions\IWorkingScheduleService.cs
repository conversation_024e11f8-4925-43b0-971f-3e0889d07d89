﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Request.WorkingSchedule;
using Application.Infrastructure.Models.Response;
using Microsoft.AspNetCore.Routing.Template;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IWorkingScheduleService
    {
        //  public IWorkingScheduleService() { }
        Task<BaseSearchResponse<WorkingScheduleResponse>> SearchWorkingScheduleAsync(SearchWorkingScheduleRequest request);
        Task<WorkingScheduleResponse> CreateWorkingScheduleAsync(CreateWorkingScheduleRequest request);
        Task<List<WSPlaceResponse>> GetPlaceAsync();
        Task<List<WorkingScheduleResponse>> GetWorkingScheduleById(int Id);

        Task<bool> UpdateWorkingScheduleAsync(UpdateWorkingScheduleRequest request);

        Task<bool> DeleteWorkingSchedulesAsync(DeleteWorkingScheduleRequest request);
        Task<MemoryStream> CreateScheduleWordFile(string templatePath, SearchWorkingScheduleRequest request);

        DateTime GetFirstDayOfWeek(int year, int week);
        Task<BaseSearchResponse<WorkingScheduleAnnouncedResponse>> SearchWorkingScheduleAnnouncedAsync(SearchWorkingScheduleAnnouncedRequest request);
        Task<object> WorkingScheduleIssueAnnouncedAsync(WorkingScheduleIssueAnnouncedRequest request);
        Task<List<ScheduleData>> CheckDuplicateSchedule(CreateWorkingScheduleRequest request);
    }
}
