﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.District;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Ward
{
    public class CreateWardRequest
    {
        public int? ProvinceId { get; set; }
        public int? DistrictId { get; set; }
        public string? WardCode { get; set; }
        public string? WardName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateWardRequest, WardEntity>> Expression
        {
            get
            {
                return entity => new WardEntity
                {
                    ProvinceId = entity.ProvinceId,
                    DistrictId = entity.DistrictId,
                    WardCode = entity.WardCode,
                    WardName = entity.WardName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static WardEntity Create(CreateWardRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
