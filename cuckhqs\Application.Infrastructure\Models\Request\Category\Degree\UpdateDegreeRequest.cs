﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.AcademicRank;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Degree
{
    public  class UpdateDegreeRequest : CreateDegreeRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateDegreeRequest, DegreeEntity>> Expression
        {
            get
            {
                return entity => new DegreeEntity
                {
                    Id = entity.Id,
                    DegreeCode = entity.DegreeCode,
                    DegreeName = entity.DegreeName,
                    DegreeShortName = entity.DegreeShortName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static DegreeEntity Create(UpdateDegreeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}

