﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using Application.Infrastructure.Models.Request.Category.Degree;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IDecisionLevelService
    {
        Task<BaseSearchResponse<DecisionLevelResponse>> SearchDecisionLevelAsync(SearchDecisionLevelRequest request);
        Task<DecisionLevelResponse> CreateDecisionLevelAsync(CreateDecisionLevelRequest request);
        Task<bool> UpdateDecisionLevelAsync(UpdateDecisionLevelRequest request);
        Task<bool> DeleteDecisionLevelAsync(DeleteDecisionLevelRequest request);
    }
}
