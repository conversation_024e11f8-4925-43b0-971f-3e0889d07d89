﻿namespace Application.Infrastructure.Entities;

public class WorkingResultSyntheticEntity : BaseEntity<int>
{
    public int? OrganizationUnitId { get; set; }
    public int? Year { get; set; }
    public int? Week { get; set; }
    public string? Contents { get; set; }
    public int? Announced { get; set; }
    public bool? Active { get; set; }
    public short? SortOrder { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public string? IPAddress { get; set; }
    public string? CreatedBy { get; set; }
    public string? ModifiedBy { get; set; }
}