{"version": 3, "sources": ["../../../../../node_modules/@ngrx/store/fesm2022/ngrx-store.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, computed, isDevMode, inject, makeEnvironmentProviders, ENVIRONMENT_INITIALIZER, NgModule, Optional } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject, queueScheduler } from 'rxjs';\nimport { observeOn, withLatestFrom, scan, pluck, map, distinctUntilChanged } from 'rxjs/operators';\nimport { toSignal } from '@angular/core/rxjs-interop';\nconst REGISTERED_ACTION_TYPES = {};\nfunction resetRegisteredActionTypes() {\n  for (const key of Object.keys(REGISTERED_ACTION_TYPES)) {\n    delete REGISTERED_ACTION_TYPES[key];\n  }\n}\n\n/**\n * @description\n * Creates a configured `Creator` function that, when called, returns an object in the shape of the `Action` interface.\n *\n * Action creators reduce the explicitness of class-based action creators.\n *\n * @param type Describes the action that will be dispatched\n * @param config Additional metadata needed for the handling of the action.  See {@link createAction#usage-notes Usage Notes}.\n *\n * @usageNotes\n *\n * **Declaring an action creator**\n *\n * Without additional metadata:\n * ```ts\n * export const increment = createAction('[Counter] Increment');\n * ```\n * With additional metadata:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   props<{ user: User }>()\n * );\n * ```\n * With a function:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   (response: Response) => response.user\n * );\n * ```\n *\n * **Dispatching an action**\n *\n * Without additional metadata:\n * ```ts\n * store.dispatch(increment());\n * ```\n * With additional metadata:\n * ```ts\n * store.dispatch(loginSuccess({ user: newUser }));\n * ```\n *\n * **Referencing an action in a reducer**\n *\n * Using a switch statement:\n * ```ts\n * switch (action.type) {\n *   // ...\n *   case AuthApiActions.loginSuccess.type: {\n *     return {\n *       ...state,\n *       user: action.user\n *     };\n *   }\n * }\n * ```\n * Using a reducer creator:\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n *\n *  **Referencing an action in an effect**\n * ```ts\n * effectName$ = createEffect(\n *   () => this.actions$.pipe(\n *     ofType(AuthApiActions.loginSuccess),\n *     // ...\n *   )\n * );\n * ```\n */\nfunction createAction(type, config) {\n  REGISTERED_ACTION_TYPES[type] = (REGISTERED_ACTION_TYPES[type] || 0) + 1;\n  if (typeof config === 'function') {\n    return defineType(type, (...args) => ({\n      ...config(...args),\n      type\n    }));\n  }\n  const as = config ? config._as : 'empty';\n  switch (as) {\n    case 'empty':\n      return defineType(type, () => ({\n        type\n      }));\n    case 'props':\n      return defineType(type, props => ({\n        ...props,\n        type\n      }));\n    default:\n      throw new Error('Unexpected config.');\n  }\n}\nfunction props() {\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return {\n    _as: 'props',\n    _p: undefined\n  };\n}\nfunction union(creators) {\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return undefined;\n}\nfunction defineType(type, creator) {\n  return Object.defineProperty(creator, 'type', {\n    value: type,\n    writable: false\n  });\n}\nfunction capitalize(text) {\n  return text.charAt(0).toUpperCase() + text.substring(1);\n}\nfunction uncapitalize(text) {\n  return text.charAt(0).toLowerCase() + text.substring(1);\n}\n\n/**\n * @description\n * A function that creates a group of action creators with the same source.\n *\n * @param config An object that contains a source and dictionary of events.\n * An event is a key-value pair of an event name and event props.\n * @returns A dictionary of action creators.\n * The name of each action creator is created by camel casing the event name.\n * The type of each action is created using the \"[Source] Event Name\" pattern.\n *\n * @usageNotes\n *\n * ```ts\n * const authApiActions = createActionGroup({\n *   source: 'Auth API',\n *   events: {\n *     // defining events with payload using the `props` function\n *     'Login Success': props<{ userId: number; token: string }>(),\n *     'Login Failure': props<{ error: string }>(),\n *\n *     // defining an event without payload using the `emptyProps` function\n *     'Logout Success': emptyProps(),\n *\n *     // defining an event with payload using the props factory\n *     'Logout Failure': (error: Error) => ({ error }),\n *   },\n * });\n *\n * // action type: \"[Auth API] Login Success\"\n * authApiActions.loginSuccess({ userId: 10, token: 'ngrx' });\n *\n * // action type: \"[Auth API] Login Failure\"\n * authApiActions.loginFailure({ error: 'Login Failure!' });\n *\n * // action type: \"[Auth API] Logout Success\"\n * authApiActions.logoutSuccess();\n *\n * // action type: \"[Auth API] Logout Failure\";\n * authApiActions.logoutFailure(new Error('Logout Failure!'));\n * ```\n */\nfunction createActionGroup(config) {\n  const {\n    source,\n    events\n  } = config;\n  return Object.keys(events).reduce((actionGroup, eventName) => ({\n    ...actionGroup,\n    [toActionName(eventName)]: createAction(toActionType(source, eventName), events[eventName])\n  }), {});\n}\nfunction emptyProps() {\n  return props();\n}\nfunction toActionName(eventName) {\n  return eventName.trim().split(' ').map((word, i) => i === 0 ? uncapitalize(word) : capitalize(word)).join('');\n}\nfunction toActionType(source, eventName) {\n  return `[${source}] ${eventName}`;\n}\nconst INIT = '@ngrx/store/init';\nclass ActionsSubject extends BehaviorSubject {\n  constructor() {\n    super({\n      type: INIT\n    });\n  }\n  next(action) {\n    if (typeof action === 'function') {\n      throw new TypeError(`\n        Dispatch expected an object, instead it received a function.\n        If you're using the createAction function, make sure to invoke the function\n        before dispatching the action. For example, someAction should be someAction().`);\n    } else if (typeof action === 'undefined') {\n      throw new TypeError(`Actions must be objects`);\n    } else if (typeof action.type === 'undefined') {\n      throw new TypeError(`Actions must have a type property`);\n    }\n    super.next(action);\n  }\n  complete() {\n    /* noop */\n  }\n  ngOnDestroy() {\n    super.complete();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function ActionsSubject_Factory(t) {\n      return new (t || ActionsSubject)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ActionsSubject,\n      factory: ActionsSubject.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ActionsSubject, [{\n    type: Injectable\n  }], () => [], null);\n})();\nconst ACTIONS_SUBJECT_PROVIDERS = [ActionsSubject];\nconst _ROOT_STORE_GUARD = new InjectionToken('@ngrx/store Internal Root Guard');\nconst _INITIAL_STATE = new InjectionToken('@ngrx/store Internal Initial State');\nconst INITIAL_STATE = new InjectionToken('@ngrx/store Initial State');\nconst REDUCER_FACTORY = new InjectionToken('@ngrx/store Reducer Factory');\nconst _REDUCER_FACTORY = new InjectionToken('@ngrx/store Internal Reducer Factory Provider');\nconst INITIAL_REDUCERS = new InjectionToken('@ngrx/store Initial Reducers');\nconst _INITIAL_REDUCERS = new InjectionToken('@ngrx/store Internal Initial Reducers');\nconst STORE_FEATURES = new InjectionToken('@ngrx/store Store Features');\nconst _STORE_REDUCERS = new InjectionToken('@ngrx/store Internal Store Reducers');\nconst _FEATURE_REDUCERS = new InjectionToken('@ngrx/store Internal Feature Reducers');\nconst _FEATURE_CONFIGS = new InjectionToken('@ngrx/store Internal Feature Configs');\nconst _STORE_FEATURES = new InjectionToken('@ngrx/store Internal Store Features');\nconst _FEATURE_REDUCERS_TOKEN = new InjectionToken('@ngrx/store Internal Feature Reducers Token');\nconst FEATURE_REDUCERS = new InjectionToken('@ngrx/store Feature Reducers');\n/**\n * User-defined meta reducers from StoreModule.forRoot()\n */\nconst USER_PROVIDED_META_REDUCERS = new InjectionToken('@ngrx/store User Provided Meta Reducers');\n/**\n * Meta reducers defined either internally by @ngrx/store or by library authors\n */\nconst META_REDUCERS = new InjectionToken('@ngrx/store Meta Reducers');\n/**\n * Concats the user provided meta reducers and the meta reducers provided on the multi\n * injection token\n */\nconst _RESOLVED_META_REDUCERS = new InjectionToken('@ngrx/store Internal Resolved Meta Reducers');\n/**\n * Runtime checks defined by the user via an InjectionToken\n * Defaults to `_USER_RUNTIME_CHECKS`\n */\nconst USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store User Runtime Checks Config');\n/**\n * Runtime checks defined by the user via forRoot()\n */\nconst _USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal User Runtime Checks Config');\n/**\n * Runtime checks currently in use\n */\nconst ACTIVE_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal Runtime Checks');\nconst _ACTION_TYPE_UNIQUENESS_CHECK = new InjectionToken('@ngrx/store Check if Action types are unique');\n/**\n * InjectionToken that registers the global Store.\n * Mainly used to provide a hook that can be injected\n * to ensure the root state is loaded before something\n * that depends on it.\n */\nconst ROOT_STORE_PROVIDER = new InjectionToken('@ngrx/store Root Store Provider');\n/**\n * InjectionToken that registers feature states.\n * Mainly used to provide a hook that can be injected\n * to ensure feature state is loaded before something\n * that depends on it.\n */\nconst FEATURE_STATE_PROVIDER = new InjectionToken('@ngrx/store Feature State Provider');\n\n/**\n * @description\n * Combines reducers for individual features into a single reducer.\n *\n * You can use this function to delegate handling of state transitions to multiple reducers, each acting on their\n * own sub-state within the root state.\n *\n * @param reducers An object mapping keys of the root state to their corresponding feature reducer.\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * **Example combining two feature reducers into one \"root\" reducer**\n *\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * });\n * ```\n *\n * You can also override the initial states of the sub-features:\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * }, {\n *   featureA: { counterA: 13 },\n *   featureB: { counterB: 37 }\n * });\n * ```\n */\nfunction combineReducers(reducers, initialState = {}) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n  return function combination(state, action) {\n    state = state === undefined ? initialState : state;\n    let hasChanged = false;\n    const nextState = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    return hasChanged ? nextState : state;\n  };\n}\nfunction omit(object, keyToRemove) {\n  return Object.keys(object).filter(key => key !== keyToRemove).reduce((result, key) => Object.assign(result, {\n    [key]: object[key]\n  }), {});\n}\nfunction compose(...functions) {\n  return function (arg) {\n    if (functions.length === 0) {\n      return arg;\n    }\n    const last = functions[functions.length - 1];\n    const rest = functions.slice(0, -1);\n    return rest.reduceRight((composed, fn) => fn(composed), last(arg));\n  };\n}\nfunction createReducerFactory(reducerFactory, metaReducers) {\n  if (Array.isArray(metaReducers) && metaReducers.length > 0) {\n    reducerFactory = compose.apply(null, [...metaReducers, reducerFactory]);\n  }\n  return (reducers, initialState) => {\n    const reducer = reducerFactory(reducers);\n    return (state, action) => {\n      state = state === undefined ? initialState : state;\n      return reducer(state, action);\n    };\n  };\n}\nfunction createFeatureReducerFactory(metaReducers) {\n  const reducerFactory = Array.isArray(metaReducers) && metaReducers.length > 0 ? compose(...metaReducers) : r => r;\n  return (reducer, initialState) => {\n    reducer = reducerFactory(reducer);\n    return (state, action) => {\n      state = state === undefined ? initialState : state;\n      return reducer(state, action);\n    };\n  };\n}\nclass ReducerObservable extends Observable {}\nclass ReducerManagerDispatcher extends ActionsSubject {}\nconst UPDATE = '@ngrx/store/update-reducers';\nclass ReducerManager extends BehaviorSubject {\n  get currentReducers() {\n    return this.reducers;\n  }\n  constructor(dispatcher, initialState, reducers, reducerFactory) {\n    super(reducerFactory(reducers, initialState));\n    this.dispatcher = dispatcher;\n    this.initialState = initialState;\n    this.reducers = reducers;\n    this.reducerFactory = reducerFactory;\n  }\n  addFeature(feature) {\n    this.addFeatures([feature]);\n  }\n  addFeatures(features) {\n    const reducers = features.reduce((reducerDict, {\n      reducers,\n      reducerFactory,\n      metaReducers,\n      initialState,\n      key\n    }) => {\n      const reducer = typeof reducers === 'function' ? createFeatureReducerFactory(metaReducers)(reducers, initialState) : createReducerFactory(reducerFactory, metaReducers)(reducers, initialState);\n      reducerDict[key] = reducer;\n      return reducerDict;\n    }, {});\n    this.addReducers(reducers);\n  }\n  removeFeature(feature) {\n    this.removeFeatures([feature]);\n  }\n  removeFeatures(features) {\n    this.removeReducers(features.map(p => p.key));\n  }\n  addReducer(key, reducer) {\n    this.addReducers({\n      [key]: reducer\n    });\n  }\n  addReducers(reducers) {\n    this.reducers = {\n      ...this.reducers,\n      ...reducers\n    };\n    this.updateReducers(Object.keys(reducers));\n  }\n  removeReducer(featureKey) {\n    this.removeReducers([featureKey]);\n  }\n  removeReducers(featureKeys) {\n    featureKeys.forEach(key => {\n      this.reducers = omit(this.reducers, key) /*TODO(#823)*/;\n    });\n    this.updateReducers(featureKeys);\n  }\n  updateReducers(featureKeys) {\n    this.next(this.reducerFactory(this.reducers, this.initialState));\n    this.dispatcher.next({\n      type: UPDATE,\n      features: featureKeys\n    });\n  }\n  ngOnDestroy() {\n    this.complete();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function ReducerManager_Factory(t) {\n      return new (t || ReducerManager)(i0.ɵɵinject(ReducerManagerDispatcher), i0.ɵɵinject(INITIAL_STATE), i0.ɵɵinject(INITIAL_REDUCERS), i0.ɵɵinject(REDUCER_FACTORY));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ReducerManager,\n      factory: ReducerManager.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReducerManager, [{\n    type: Injectable\n  }], () => [{\n    type: ReducerManagerDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [INITIAL_STATE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [INITIAL_REDUCERS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REDUCER_FACTORY]\n    }]\n  }], null);\n})();\nconst REDUCER_MANAGER_PROVIDERS = [ReducerManager, {\n  provide: ReducerObservable,\n  useExisting: ReducerManager\n}, {\n  provide: ReducerManagerDispatcher,\n  useExisting: ActionsSubject\n}];\nclass ScannedActionsSubject extends Subject {\n  ngOnDestroy() {\n    this.complete();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵScannedActionsSubject_BaseFactory;\n      return function ScannedActionsSubject_Factory(t) {\n        return (ɵScannedActionsSubject_BaseFactory || (ɵScannedActionsSubject_BaseFactory = i0.ɵɵgetInheritedFactory(ScannedActionsSubject)))(t || ScannedActionsSubject);\n      };\n    })();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScannedActionsSubject,\n      factory: ScannedActionsSubject.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScannedActionsSubject, [{\n    type: Injectable\n  }], null, null);\n})();\nconst SCANNED_ACTIONS_SUBJECT_PROVIDERS = [ScannedActionsSubject];\nclass StateObservable extends Observable {}\nclass State extends BehaviorSubject {\n  static {\n    this.INIT = INIT;\n  }\n  constructor(actions$, reducer$, scannedActions, initialState) {\n    super(initialState);\n    const actionsOnQueue$ = actions$.pipe(observeOn(queueScheduler));\n    const withLatestReducer$ = actionsOnQueue$.pipe(withLatestFrom(reducer$));\n    const seed = {\n      state: initialState\n    };\n    const stateAndAction$ = withLatestReducer$.pipe(scan(reduceState, seed));\n    this.stateSubscription = stateAndAction$.subscribe(({\n      state,\n      action\n    }) => {\n      this.next(state);\n      scannedActions.next(action);\n    });\n    this.state = toSignal(this, {\n      manualCleanup: true,\n      requireSync: true\n    });\n  }\n  ngOnDestroy() {\n    this.stateSubscription.unsubscribe();\n    this.complete();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function State_Factory(t) {\n      return new (t || State)(i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerObservable), i0.ɵɵinject(ScannedActionsSubject), i0.ɵɵinject(INITIAL_STATE));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: State,\n      factory: State.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(State, [{\n    type: Injectable\n  }], () => [{\n    type: ActionsSubject\n  }, {\n    type: ReducerObservable\n  }, {\n    type: ScannedActionsSubject\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [INITIAL_STATE]\n    }]\n  }], null);\n})();\nfunction reduceState(stateActionPair = {\n  state: undefined\n}, [action, reducer]) {\n  const {\n    state\n  } = stateActionPair;\n  return {\n    state: reducer(state, action),\n    action\n  };\n}\nconst STATE_PROVIDERS = [State, {\n  provide: StateObservable,\n  useExisting: State\n}];\n\n// disabled because we have lowercase generics for `select`\nclass Store extends Observable {\n  constructor(state$, actionsObserver, reducerManager) {\n    super();\n    this.actionsObserver = actionsObserver;\n    this.reducerManager = reducerManager;\n    this.source = state$;\n    this.state = state$.state;\n  }\n  select(pathOrMapFn, ...paths) {\n    return select.call(null, pathOrMapFn, ...paths)(this);\n  }\n  /**\n   * Returns a signal of the provided selector.\n   *\n   * @param selector selector function\n   * @param options select signal options\n   */\n  selectSignal(selector, options) {\n    return computed(() => selector(this.state()), options);\n  }\n  lift(operator) {\n    const store = new Store(this, this.actionsObserver, this.reducerManager);\n    store.operator = operator;\n    return store;\n  }\n  dispatch(action) {\n    this.actionsObserver.next(action);\n  }\n  next(action) {\n    this.actionsObserver.next(action);\n  }\n  error(err) {\n    this.actionsObserver.error(err);\n  }\n  complete() {\n    this.actionsObserver.complete();\n  }\n  addReducer(key, reducer) {\n    this.reducerManager.addReducer(key, reducer);\n  }\n  removeReducer(key) {\n    this.reducerManager.removeReducer(key);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function Store_Factory(t) {\n      return new (t || Store)(i0.ɵɵinject(StateObservable), i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerManager));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Store,\n      factory: Store.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Store, [{\n    type: Injectable\n  }], () => [{\n    type: StateObservable\n  }, {\n    type: ActionsSubject\n  }, {\n    type: ReducerManager\n  }], null);\n})();\nconst STORE_PROVIDERS = [Store];\nfunction select(pathOrMapFn, propsOrPath, ...paths) {\n  return function selectOperator(source$) {\n    let mapped$;\n    if (typeof pathOrMapFn === 'string') {\n      const pathSlices = [propsOrPath, ...paths].filter(Boolean);\n      mapped$ = source$.pipe(pluck(pathOrMapFn, ...pathSlices));\n    } else if (typeof pathOrMapFn === 'function') {\n      mapped$ = source$.pipe(map(source => pathOrMapFn(source, propsOrPath)));\n    } else {\n      throw new TypeError(`Unexpected type '${typeof pathOrMapFn}' in select operator,` + ` expected 'string' or 'function'`);\n    }\n    return mapped$.pipe(distinctUntilChanged());\n  };\n}\nconst RUNTIME_CHECK_URL = 'https://ngrx.io/guide/store/configuration/runtime-checks';\nfunction isUndefined(target) {\n  return target === undefined;\n}\nfunction isNull(target) {\n  return target === null;\n}\nfunction isArray(target) {\n  return Array.isArray(target);\n}\nfunction isString(target) {\n  return typeof target === 'string';\n}\nfunction isBoolean(target) {\n  return typeof target === 'boolean';\n}\nfunction isNumber(target) {\n  return typeof target === 'number';\n}\nfunction isObjectLike(target) {\n  return typeof target === 'object' && target !== null;\n}\nfunction isObject(target) {\n  return isObjectLike(target) && !isArray(target);\n}\nfunction isPlainObject(target) {\n  if (!isObject(target)) {\n    return false;\n  }\n  const targetPrototype = Object.getPrototypeOf(target);\n  return targetPrototype === Object.prototype || targetPrototype === null;\n}\nfunction isFunction(target) {\n  return typeof target === 'function';\n}\nfunction isComponent(target) {\n  return isFunction(target) && target.hasOwnProperty('ɵcmp');\n}\nfunction hasOwnProperty(target, propertyName) {\n  return Object.prototype.hasOwnProperty.call(target, propertyName);\n}\nlet _ngrxMockEnvironment = false;\nfunction setNgrxMockEnvironment(value) {\n  _ngrxMockEnvironment = value;\n}\nfunction isNgrxMockEnvironment() {\n  return _ngrxMockEnvironment;\n}\nfunction isEqualCheck(a, b) {\n  return a === b;\n}\nfunction isArgumentsChanged(args, lastArguments, comparator) {\n  for (let i = 0; i < args.length; i++) {\n    if (!comparator(args[i], lastArguments[i])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction resultMemoize(projectionFn, isResultEqual) {\n  return defaultMemoize(projectionFn, isEqualCheck, isResultEqual);\n}\nfunction defaultMemoize(projectionFn, isArgumentsEqual = isEqualCheck, isResultEqual = isEqualCheck) {\n  let lastArguments = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, , , , ,\n  let lastResult = null;\n  let overrideResult;\n  function reset() {\n    lastArguments = null;\n    lastResult = null;\n  }\n  function setResult(result = undefined) {\n    overrideResult = {\n      result\n    };\n  }\n  function clearResult() {\n    overrideResult = undefined;\n  }\n  /* eslint-disable prefer-rest-params, prefer-spread */\n  // disabled because of the use of `arguments`\n  function memoized() {\n    if (overrideResult !== undefined) {\n      return overrideResult.result;\n    }\n    if (!lastArguments) {\n      lastResult = projectionFn.apply(null, arguments);\n      lastArguments = arguments;\n      return lastResult;\n    }\n    if (!isArgumentsChanged(arguments, lastArguments, isArgumentsEqual)) {\n      return lastResult;\n    }\n    const newResult = projectionFn.apply(null, arguments);\n    lastArguments = arguments;\n    if (isResultEqual(lastResult, newResult)) {\n      return lastResult;\n    }\n    lastResult = newResult;\n    return newResult;\n  }\n  return {\n    memoized,\n    reset,\n    setResult,\n    clearResult\n  };\n}\nfunction createSelector(...input) {\n  return createSelectorFactory(defaultMemoize)(...input);\n}\nfunction defaultStateFn(state, selectors, props, memoizedProjector) {\n  if (props === undefined) {\n    const args = selectors.map(fn => fn(state));\n    return memoizedProjector.memoized.apply(null, args);\n  }\n  const args = selectors.map(fn => fn(state, props));\n  return memoizedProjector.memoized.apply(null, [...args, props]);\n}\n/**\n *\n * @param memoize The function used to memoize selectors\n * @param options Config Object that may include a `stateFn` function defining how to return the selector's value, given the entire `Store`'s state, parent `Selector`s, `Props`, and a `MemoizedProjection`\n *\n * @usageNotes\n *\n * **Creating a Selector Factory Where Array Order Does Not Matter**\n *\n * ```ts\n * function removeMatch(arr: string[], target: string): string[] {\n *   const matchIndex = arr.indexOf(target);\n *   return [...arr.slice(0, matchIndex), ...arr.slice(matchIndex + 1)];\n * }\n *\n * function orderDoesNotMatterComparer(a: any, b: any): boolean {\n *   if (!Array.isArray(a) || !Array.isArray(b)) {\n *     return a === b;\n *   }\n *   if (a.length !== b.length) {\n *     return false;\n *   }\n *   let tempB = [...b];\n *   function reduceToDetermineIfArraysContainSameContents(\n *     previousCallResult: boolean,\n *     arrayMember: any\n *   ): boolean {\n *     if (previousCallResult === false) {\n *       return false;\n *     }\n *     if (tempB.includes(arrayMember)) {\n *       tempB = removeMatch(tempB, arrayMember);\n *       return true;\n *     }\n *     return false;\n *   }\n *   return a.reduce(reduceToDetermineIfArraysContainSameContents, true);\n * }\n *\n * export const createOrderDoesNotMatterSelector = createSelectorFactory(\n *   (projectionFun) => defaultMemoize(\n *     projectionFun,\n *     orderDoesNotMatterComparer,\n *     orderDoesNotMatterComparer\n *   )\n * );\n * ```\n *\n * **Creating an Alternative Memoization Strategy**\n *\n * ```ts\n * function serialize(x: any): string {\n *   return JSON.stringify(x);\n * }\n *\n * export const createFullHistorySelector = createSelectorFactory(\n *  (projectionFunction) => {\n *    const cache = {};\n *\n *    function memoized() {\n *      const serializedArguments = serialize(...arguments);\n *       if (cache[serializedArguments] != null) {\n *         cache[serializedArguments] = projectionFunction.apply(null, arguments);\n *       }\n *       return cache[serializedArguments];\n *     }\n *     return {\n *       memoized,\n *       reset: () => {},\n *       setResult: () => {},\n *       clearResult: () => {},\n *     };\n *   }\n * );\n * ```\n */\nfunction createSelectorFactory(memoize, options = {\n  stateFn: defaultStateFn\n}) {\n  return function (...input) {\n    let args = input;\n    if (Array.isArray(args[0])) {\n      const [head, ...tail] = args;\n      args = [...head, ...tail];\n    } else if (args.length === 1 && isSelectorsDictionary(args[0])) {\n      args = extractArgsFromSelectorsDictionary(args[0]);\n    }\n    const selectors = args.slice(0, args.length - 1);\n    const projector = args[args.length - 1];\n    const memoizedSelectors = selectors.filter(selector => selector.release && typeof selector.release === 'function');\n    const memoizedProjector = memoize(function (...selectors) {\n      return projector.apply(null, selectors);\n    });\n    const memoizedState = defaultMemoize(function (state, props) {\n      return options.stateFn.apply(null, [state, selectors, props, memoizedProjector]);\n    });\n    function release() {\n      memoizedState.reset();\n      memoizedProjector.reset();\n      memoizedSelectors.forEach(selector => selector.release());\n    }\n    return Object.assign(memoizedState.memoized, {\n      release,\n      projector: memoizedProjector.memoized,\n      setResult: memoizedState.setResult,\n      clearResult: memoizedState.clearResult\n    });\n  };\n}\nfunction createFeatureSelector(featureName) {\n  return createSelector(state => {\n    const featureState = state[featureName];\n    if (!isNgrxMockEnvironment() && isDevMode() && !(featureName in state)) {\n      console.warn(`@ngrx/store: The feature name \"${featureName}\" does ` + 'not exist in the state, therefore createFeatureSelector ' + 'cannot access it.  Be sure it is imported in a loaded module ' + `using StoreModule.forRoot('${featureName}', ...) or ` + `StoreModule.forFeature('${featureName}', ...).  If the default ` + 'state is intended to be undefined, as is the case with router ' + 'state, this development-only warning message can be ignored.');\n    }\n    return featureState;\n  }, featureState => featureState);\n}\nfunction isSelectorsDictionary(selectors) {\n  return !!selectors && typeof selectors === 'object' && Object.values(selectors).every(selector => typeof selector === 'function');\n}\nfunction extractArgsFromSelectorsDictionary(selectorsDictionary) {\n  const selectors = Object.values(selectorsDictionary);\n  const resultKeys = Object.keys(selectorsDictionary);\n  const projector = (...selectorResults) => resultKeys.reduce((result, key, index) => ({\n    ...result,\n    [key]: selectorResults[index]\n  }), {});\n  return [...selectors, projector];\n}\n\n/**\n * @description\n * A function that accepts a feature name and a feature reducer, and creates\n * a feature selector and a selector for each feature state property.\n * This function also provides the ability to add extra selectors to\n * the feature object.\n *\n * @param featureConfig An object that contains a feature name and a feature\n * reducer as required, and extra selectors factory as an optional argument.\n * @returns An object that contains a feature name, a feature reducer,\n * a feature selector, a selector for each feature state property, and extra\n * selectors.\n *\n * @usageNotes\n *\n * ```ts\n * interface ProductsState {\n *   products: Product[];\n *   selectedId: string | null;\n * }\n *\n * const initialState: ProductsState = {\n *   products: [],\n *   selectedId: null,\n * };\n *\n * const productsFeature = createFeature({\n *   name: 'products',\n *   reducer: createReducer(\n *     initialState,\n *     on(ProductsApiActions.loadSuccess(state, { products }) => ({\n *       ...state,\n *       products,\n *     }),\n *   ),\n * });\n *\n * const {\n *   name,\n *   reducer,\n *   // feature selector\n *   selectProductsState, // type: MemoizedSelector<Record<string, any>, ProductsState>\n *   // feature state properties selectors\n *   selectProducts, // type: MemoizedSelector<Record<string, any>, Product[]>\n *   selectSelectedId, // type: MemoizedSelector<Record<string, any>, string | null>\n * } = productsFeature;\n * ```\n *\n * **Creating Feature with Extra Selectors**\n *\n * ```ts\n * type CallState = 'init' | 'loading' | 'loaded' | { error: string };\n *\n * interface State extends EntityState<Product> {\n *   callState: CallState;\n * }\n *\n * const adapter = createEntityAdapter<Product>();\n * const initialState: State = adapter.getInitialState({\n *   callState: 'init',\n * });\n *\n * export const productsFeature = createFeature({\n *   name: 'products',\n *   reducer: createReducer(initialState),\n *   extraSelectors: ({ selectProductsState, selectCallState }) => ({\n *     ...adapter.getSelectors(selectProductsState),\n *     ...getCallStateSelectors(selectCallState)\n *   }),\n * });\n *\n * const {\n *   name,\n *   reducer,\n *   // feature selector\n *   selectProductsState,\n *   // feature state properties selectors\n *   selectIds,\n *   selectEntities,\n *   selectCallState,\n *   // selectors returned by `adapter.getSelectors`\n *   selectAll,\n *   selectTotal,\n *   // selectors returned by `getCallStateSelectors`\n *   selectIsLoading,\n *   selectIsLoaded,\n *   selectError,\n * } = productsFeature;\n * ```\n */\nfunction createFeature(featureConfig) {\n  const {\n    name,\n    reducer,\n    extraSelectors: extraSelectorsFactory\n  } = featureConfig;\n  const featureSelector = createFeatureSelector(name);\n  const nestedSelectors = createNestedSelectors(featureSelector, reducer);\n  const baseSelectors = {\n    [`select${capitalize(name)}State`]: featureSelector,\n    ...nestedSelectors\n  };\n  const extraSelectors = extraSelectorsFactory ? extraSelectorsFactory(baseSelectors) : {};\n  return {\n    name,\n    reducer,\n    ...baseSelectors,\n    ...extraSelectors\n  };\n}\nfunction createNestedSelectors(featureSelector, reducer) {\n  const initialState = getInitialState(reducer);\n  const nestedKeys = isPlainObject(initialState) ? Object.keys(initialState) : [];\n  return nestedKeys.reduce((nestedSelectors, nestedKey) => ({\n    ...nestedSelectors,\n    [`select${capitalize(nestedKey)}`]: createSelector(featureSelector, parentState => parentState?.[nestedKey])\n  }), {});\n}\nfunction getInitialState(reducer) {\n  return reducer(undefined, {\n    type: '@ngrx/feature/init'\n  });\n}\nfunction _createStoreReducers(reducers) {\n  return reducers instanceof InjectionToken ? inject(reducers) : reducers;\n}\nfunction _createFeatureStore(configs, featureStores) {\n  return featureStores.map((feat, index) => {\n    if (configs[index] instanceof InjectionToken) {\n      const conf = inject(configs[index]);\n      return {\n        key: feat.key,\n        reducerFactory: conf.reducerFactory ? conf.reducerFactory : combineReducers,\n        metaReducers: conf.metaReducers ? conf.metaReducers : [],\n        initialState: conf.initialState\n      };\n    }\n    return feat;\n  });\n}\nfunction _createFeatureReducers(reducerCollection) {\n  return reducerCollection.map(reducer => {\n    return reducer instanceof InjectionToken ? inject(reducer) : reducer;\n  });\n}\nfunction _initialStateFactory(initialState) {\n  if (typeof initialState === 'function') {\n    return initialState();\n  }\n  return initialState;\n}\nfunction _concatMetaReducers(metaReducers, userProvidedMetaReducers) {\n  return metaReducers.concat(userProvidedMetaReducers);\n}\nfunction _provideForRootGuard() {\n  const store = inject(Store, {\n    optional: true,\n    skipSelf: true\n  });\n  if (store) {\n    throw new TypeError(`The root Store has been provided more than once. Feature modules should provide feature states instead.`);\n  }\n  return 'guarded';\n}\nfunction immutabilityCheckMetaReducer(reducer, checks) {\n  return function (state, action) {\n    const act = checks.action(action) ? freeze(action) : action;\n    const nextState = reducer(state, act);\n    return checks.state() ? freeze(nextState) : nextState;\n  };\n}\nfunction freeze(target) {\n  Object.freeze(target);\n  const targetIsFunction = isFunction(target);\n  Object.getOwnPropertyNames(target).forEach(prop => {\n    // Ignore Ivy properties, ref: https://github.com/ngrx/platform/issues/2109#issuecomment-582689060\n    if (prop.startsWith('ɵ')) {\n      return;\n    }\n    if (hasOwnProperty(target, prop) && (targetIsFunction ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments' : true)) {\n      const propValue = target[prop];\n      if ((isObjectLike(propValue) || isFunction(propValue)) && !Object.isFrozen(propValue)) {\n        freeze(propValue);\n      }\n    }\n  });\n  return target;\n}\nfunction serializationCheckMetaReducer(reducer, checks) {\n  return function (state, action) {\n    if (checks.action(action)) {\n      const unserializableAction = getUnserializable(action);\n      throwIfUnserializable(unserializableAction, 'action');\n    }\n    const nextState = reducer(state, action);\n    if (checks.state()) {\n      const unserializableState = getUnserializable(nextState);\n      throwIfUnserializable(unserializableState, 'state');\n    }\n    return nextState;\n  };\n}\nfunction getUnserializable(target, path = []) {\n  // Guard against undefined and null, e.g. a reducer that returns undefined\n  if ((isUndefined(target) || isNull(target)) && path.length === 0) {\n    return {\n      path: ['root'],\n      value: target\n    };\n  }\n  const keys = Object.keys(target);\n  return keys.reduce((result, key) => {\n    if (result) {\n      return result;\n    }\n    const value = target[key];\n    // Ignore Ivy components\n    if (isComponent(value)) {\n      return result;\n    }\n    if (isUndefined(value) || isNull(value) || isNumber(value) || isBoolean(value) || isString(value) || isArray(value)) {\n      return false;\n    }\n    if (isPlainObject(value)) {\n      return getUnserializable(value, [...path, key]);\n    }\n    return {\n      path: [...path, key],\n      value\n    };\n  }, false);\n}\nfunction throwIfUnserializable(unserializable, context) {\n  if (unserializable === false) {\n    return;\n  }\n  const unserializablePath = unserializable.path.join('.');\n  const error = new Error(`Detected unserializable ${context} at \"${unserializablePath}\". ${RUNTIME_CHECK_URL}#strict${context}serializability`);\n  error.value = unserializable.value;\n  error.unserializablePath = unserializablePath;\n  throw error;\n}\nfunction inNgZoneAssertMetaReducer(reducer, checks) {\n  return function (state, action) {\n    if (checks.action(action) && !i0.NgZone.isInAngularZone()) {\n      throw new Error(`Action '${action.type}' running outside NgZone. ${RUNTIME_CHECK_URL}#strictactionwithinngzone`);\n    }\n    return reducer(state, action);\n  };\n}\nfunction createActiveRuntimeChecks(runtimeChecks) {\n  if (isDevMode()) {\n    return {\n      strictStateSerializability: false,\n      strictActionSerializability: false,\n      strictStateImmutability: true,\n      strictActionImmutability: true,\n      strictActionWithinNgZone: false,\n      strictActionTypeUniqueness: false,\n      ...runtimeChecks\n    };\n  }\n  return {\n    strictStateSerializability: false,\n    strictActionSerializability: false,\n    strictStateImmutability: false,\n    strictActionImmutability: false,\n    strictActionWithinNgZone: false,\n    strictActionTypeUniqueness: false\n  };\n}\nfunction createSerializationCheckMetaReducer({\n  strictActionSerializability,\n  strictStateSerializability\n}) {\n  return reducer => strictActionSerializability || strictStateSerializability ? serializationCheckMetaReducer(reducer, {\n    action: action => strictActionSerializability && !ignoreNgrxAction(action),\n    state: () => strictStateSerializability\n  }) : reducer;\n}\nfunction createImmutabilityCheckMetaReducer({\n  strictActionImmutability,\n  strictStateImmutability\n}) {\n  return reducer => strictActionImmutability || strictStateImmutability ? immutabilityCheckMetaReducer(reducer, {\n    action: action => strictActionImmutability && !ignoreNgrxAction(action),\n    state: () => strictStateImmutability\n  }) : reducer;\n}\nfunction ignoreNgrxAction(action) {\n  return action.type.startsWith('@ngrx');\n}\nfunction createInNgZoneCheckMetaReducer({\n  strictActionWithinNgZone\n}) {\n  return reducer => strictActionWithinNgZone ? inNgZoneAssertMetaReducer(reducer, {\n    action: action => strictActionWithinNgZone && !ignoreNgrxAction(action)\n  }) : reducer;\n}\nfunction provideRuntimeChecks(runtimeChecks) {\n  return [{\n    provide: _USER_RUNTIME_CHECKS,\n    useValue: runtimeChecks\n  }, {\n    provide: USER_RUNTIME_CHECKS,\n    useFactory: _runtimeChecksFactory,\n    deps: [_USER_RUNTIME_CHECKS]\n  }, {\n    provide: ACTIVE_RUNTIME_CHECKS,\n    deps: [USER_RUNTIME_CHECKS],\n    useFactory: createActiveRuntimeChecks\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createImmutabilityCheckMetaReducer\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createSerializationCheckMetaReducer\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createInNgZoneCheckMetaReducer\n  }];\n}\nfunction checkForActionTypeUniqueness() {\n  return [{\n    provide: _ACTION_TYPE_UNIQUENESS_CHECK,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: _actionTypeUniquenessCheck\n  }];\n}\nfunction _runtimeChecksFactory(runtimeChecks) {\n  return runtimeChecks;\n}\nfunction _actionTypeUniquenessCheck(config) {\n  if (!config.strictActionTypeUniqueness) {\n    return;\n  }\n  const duplicates = Object.entries(REGISTERED_ACTION_TYPES).filter(([, registrations]) => registrations > 1).map(([type]) => type);\n  if (duplicates.length) {\n    throw new Error(`Action types are registered more than once, ${duplicates.map(type => `\"${type}\"`).join(', ')}. ${RUNTIME_CHECK_URL}#strictactiontypeuniqueness`);\n  }\n}\n\n/**\n * Provides additional slices of state in the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing Store Features\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideState('books', booksReducer)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\nfunction provideState(featureNameOrSlice, reducers, config = {}) {\n  return makeEnvironmentProviders([..._provideState(featureNameOrSlice, reducers, config), ENVIRONMENT_STATE_PROVIDER]);\n}\nfunction _provideStore(reducers = {}, config = {}) {\n  return [{\n    provide: _ROOT_STORE_GUARD,\n    useFactory: _provideForRootGuard\n  }, {\n    provide: _INITIAL_STATE,\n    useValue: config.initialState\n  }, {\n    provide: INITIAL_STATE,\n    useFactory: _initialStateFactory,\n    deps: [_INITIAL_STATE]\n  }, {\n    provide: _INITIAL_REDUCERS,\n    useValue: reducers\n  }, {\n    provide: _STORE_REDUCERS,\n    useExisting: reducers instanceof InjectionToken ? reducers : _INITIAL_REDUCERS\n  }, {\n    provide: INITIAL_REDUCERS,\n    deps: [_INITIAL_REDUCERS, [new Inject(_STORE_REDUCERS)]],\n    useFactory: _createStoreReducers\n  }, {\n    provide: USER_PROVIDED_META_REDUCERS,\n    useValue: config.metaReducers ? config.metaReducers : []\n  }, {\n    provide: _RESOLVED_META_REDUCERS,\n    deps: [META_REDUCERS, USER_PROVIDED_META_REDUCERS],\n    useFactory: _concatMetaReducers\n  }, {\n    provide: _REDUCER_FACTORY,\n    useValue: config.reducerFactory ? config.reducerFactory : combineReducers\n  }, {\n    provide: REDUCER_FACTORY,\n    deps: [_REDUCER_FACTORY, _RESOLVED_META_REDUCERS],\n    useFactory: createReducerFactory\n  }, ACTIONS_SUBJECT_PROVIDERS, REDUCER_MANAGER_PROVIDERS, SCANNED_ACTIONS_SUBJECT_PROVIDERS, STATE_PROVIDERS, STORE_PROVIDERS, provideRuntimeChecks(config.runtimeChecks), checkForActionTypeUniqueness()];\n}\nfunction rootStoreProviderFactory() {\n  inject(ActionsSubject);\n  inject(ReducerObservable);\n  inject(ScannedActionsSubject);\n  inject(Store);\n  inject(_ROOT_STORE_GUARD, {\n    optional: true\n  });\n  inject(_ACTION_TYPE_UNIQUENESS_CHECK, {\n    optional: true\n  });\n}\n/**\n * Environment Initializer used in the root\n * providers to initialize the Store\n */\nconst ENVIRONMENT_STORE_PROVIDER = [{\n  provide: ROOT_STORE_PROVIDER,\n  useFactory: rootStoreProviderFactory\n}, {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => inject(ROOT_STORE_PROVIDER);\n  }\n}];\n/**\n * Provides the global Store providers and initializes\n * the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing the Global Store\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideStore()],\n * });\n * ```\n */\nfunction provideStore(reducers, config) {\n  return makeEnvironmentProviders([..._provideStore(reducers, config), ENVIRONMENT_STORE_PROVIDER]);\n}\nfunction featureStateProviderFactory() {\n  inject(ROOT_STORE_PROVIDER);\n  const features = inject(_STORE_FEATURES);\n  const featureReducers = inject(FEATURE_REDUCERS);\n  const reducerManager = inject(ReducerManager);\n  inject(_ACTION_TYPE_UNIQUENESS_CHECK, {\n    optional: true\n  });\n  const feats = features.map((feature, index) => {\n    const featureReducerCollection = featureReducers.shift();\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const reducers = featureReducerCollection /*TODO(#823)*/[index];\n    return {\n      ...feature,\n      reducers,\n      initialState: _initialStateFactory(feature.initialState)\n    };\n  });\n  reducerManager.addFeatures(feats);\n}\n/**\n * Environment Initializer used in the feature\n * providers to register state features\n */\nconst ENVIRONMENT_STATE_PROVIDER = [{\n  provide: FEATURE_STATE_PROVIDER,\n  useFactory: featureStateProviderFactory\n}, {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => inject(FEATURE_STATE_PROVIDER);\n  }\n}];\nfunction _provideState(featureNameOrSlice, reducers, config = {}) {\n  return [{\n    provide: _FEATURE_CONFIGS,\n    multi: true,\n    useValue: featureNameOrSlice instanceof Object ? {} : config\n  }, {\n    provide: STORE_FEATURES,\n    multi: true,\n    useValue: {\n      key: featureNameOrSlice instanceof Object ? featureNameOrSlice.name : featureNameOrSlice,\n      reducerFactory: !(config instanceof InjectionToken) && config.reducerFactory ? config.reducerFactory : combineReducers,\n      metaReducers: !(config instanceof InjectionToken) && config.metaReducers ? config.metaReducers : [],\n      initialState: !(config instanceof InjectionToken) && config.initialState ? config.initialState : undefined\n    }\n  }, {\n    provide: _STORE_FEATURES,\n    deps: [_FEATURE_CONFIGS, STORE_FEATURES],\n    useFactory: _createFeatureStore\n  }, {\n    provide: _FEATURE_REDUCERS,\n    multi: true,\n    useValue: featureNameOrSlice instanceof Object ? featureNameOrSlice.reducer : reducers\n  }, {\n    provide: _FEATURE_REDUCERS_TOKEN,\n    multi: true,\n    useExisting: reducers instanceof InjectionToken ? reducers : _FEATURE_REDUCERS\n  }, {\n    provide: FEATURE_REDUCERS,\n    multi: true,\n    deps: [_FEATURE_REDUCERS, [new Inject(_FEATURE_REDUCERS_TOKEN)]],\n    useFactory: _createFeatureReducers\n  }, checkForActionTypeUniqueness()];\n}\nclass StoreRootModule {\n  constructor(actions$, reducer$, scannedActions$, store, guard, actionCheck) {}\n  /** @nocollapse */\n  static {\n    this.ɵfac = function StoreRootModule_Factory(t) {\n      return new (t || StoreRootModule)(i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerObservable), i0.ɵɵinject(ScannedActionsSubject), i0.ɵɵinject(Store), i0.ɵɵinject(_ROOT_STORE_GUARD, 8), i0.ɵɵinject(_ACTION_TYPE_UNIQUENESS_CHECK, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: StoreRootModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreRootModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: ActionsSubject\n  }, {\n    type: ReducerObservable\n  }, {\n    type: ScannedActionsSubject\n  }, {\n    type: Store\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [_ROOT_STORE_GUARD]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n    }]\n  }], null);\n})();\nclass StoreFeatureModule {\n  constructor(features, featureReducers, reducerManager, root, actionCheck) {\n    this.features = features;\n    this.featureReducers = featureReducers;\n    this.reducerManager = reducerManager;\n    const feats = features.map((feature, index) => {\n      const featureReducerCollection = featureReducers.shift();\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const reducers = featureReducerCollection /*TODO(#823)*/[index];\n      return {\n        ...feature,\n        reducers,\n        initialState: _initialStateFactory(feature.initialState)\n      };\n    });\n    reducerManager.addFeatures(feats);\n  }\n  // eslint-disable-next-line @angular-eslint/contextual-lifecycle\n  ngOnDestroy() {\n    this.reducerManager.removeFeatures(this.features);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function StoreFeatureModule_Factory(t) {\n      return new (t || StoreFeatureModule)(i0.ɵɵinject(_STORE_FEATURES), i0.ɵɵinject(FEATURE_REDUCERS), i0.ɵɵinject(ReducerManager), i0.ɵɵinject(StoreRootModule), i0.ɵɵinject(_ACTION_TYPE_UNIQUENESS_CHECK, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: StoreFeatureModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreFeatureModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_STORE_FEATURES]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [FEATURE_REDUCERS]\n    }]\n  }, {\n    type: ReducerManager\n  }, {\n    type: StoreRootModule\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n    }]\n  }], null);\n})();\nclass StoreModule {\n  static forRoot(reducers, config) {\n    return {\n      ngModule: StoreRootModule,\n      providers: [..._provideStore(reducers, config)]\n    };\n  }\n  static forFeature(featureNameOrSlice, reducers, config = {}) {\n    return {\n      ngModule: StoreFeatureModule,\n      providers: [..._provideState(featureNameOrSlice, reducers, config)]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function StoreModule_Factory(t) {\n      return new (t || StoreModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: StoreModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/**\n * @description\n * Associates actions with a given state change function.\n * A state change function must be provided as the last parameter.\n *\n * @param args `ActionCreator`'s followed by a state change function.\n *\n * @returns an association of action types with a state change function.\n *\n * @usageNotes\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n */\nfunction on(...args) {\n  const reducer = args.pop();\n  const types = args.map(creator => creator.type);\n  return {\n    reducer,\n    types\n  };\n}\n/**\n * @description\n * Creates a reducer function to handle state transitions.\n *\n * Reducer creators reduce the explicitness of reducer functions with switch statements.\n *\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @param ons Associations between actions and state changes.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * - Must be used with `ActionCreator`'s (returned by `createAction`). Cannot be used with class-based action creators.\n * - The returned `ActionReducer` does not require being wrapped with another function.\n *\n * **Declaring a reducer creator**\n *\n * ```ts\n * export const reducer = createReducer(\n *   initialState,\n *   on(\n *     featureActions.actionOne,\n *     featureActions.actionTwo,\n *     (state, { updatedValue }) => ({ ...state, prop: updatedValue })\n *   ),\n *   on(featureActions.actionThree, () => initialState);\n * );\n * ```\n */\nfunction createReducer(initialState, ...ons) {\n  const map = new Map();\n  for (const on of ons) {\n    for (const type of on.types) {\n      const existingReducer = map.get(type);\n      if (existingReducer) {\n        const newReducer = (state, action) => on.reducer(existingReducer(state, action), action);\n        map.set(type, newReducer);\n      } else {\n        map.set(type, on.reducer);\n      }\n    }\n  }\n  return function (state = initialState, action) {\n    const reducer = map.get(action.type);\n    return reducer ? reducer(state, action) : state;\n  };\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ACTIVE_RUNTIME_CHECKS, ActionsSubject, FEATURE_REDUCERS, FEATURE_STATE_PROVIDER, INIT, INITIAL_REDUCERS, INITIAL_STATE, META_REDUCERS, REDUCER_FACTORY, ROOT_STORE_PROVIDER, ReducerManager, ReducerManagerDispatcher, ReducerObservable, STORE_FEATURES, ScannedActionsSubject, State, StateObservable, Store, StoreFeatureModule, StoreModule, StoreRootModule, UPDATE, USER_PROVIDED_META_REDUCERS, USER_RUNTIME_CHECKS, combineReducers, compose, createAction, createActionGroup, createFeature, createFeatureSelector, createReducer, createReducerFactory, createSelector, createSelectorFactory, defaultMemoize, defaultStateFn, emptyProps, isNgrxMockEnvironment, on, props, provideState, provideStore, reduceState, resultMemoize, select, setNgrxMockEnvironment, union };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,0BAA0B,CAAC;AA+EjC,SAAS,aAAa,MAAM,QAAQ;AAClC,0BAAwB,IAAI,KAAK,wBAAwB,IAAI,KAAK,KAAK;AACvE,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,WAAW,MAAM,IAAI,SAAU,iCACjC,OAAO,GAAG,IAAI,IADmB;AAAA,MAEpC;AAAA,IACF,EAAE;AAAA,EACJ;AACA,QAAM,KAAK,SAAS,OAAO,MAAM;AACjC,UAAQ,IAAI;AAAA,IACV,KAAK;AACH,aAAO,WAAW,MAAM,OAAO;AAAA,QAC7B;AAAA,MACF,EAAE;AAAA,IACJ,KAAK;AACH,aAAO,WAAW,MAAM,CAAAA,WAAU,iCAC7BA,SAD6B;AAAA,QAEhC;AAAA,MACF,EAAE;AAAA,IACJ;AACE,YAAM,IAAI,MAAM,oBAAoB;AAAA,EACxC;AACF;AACA,SAAS,QAAQ;AAEf,SAAO;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,EACN;AACF;AACA,SAAS,MAAM,UAAU;AAEvB,SAAO;AACT;AACA,SAAS,WAAW,MAAM,SAAS;AACjC,SAAO,OAAO,eAAe,SAAS,QAAQ;AAAA,IAC5C,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACH;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,UAAU,CAAC;AACxD;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,UAAU,CAAC;AACxD;AA2CA,SAAS,kBAAkB,QAAQ;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,aAAa,cAAe,iCAC1D,cAD0D;AAAA,IAE7D,CAAC,aAAa,SAAS,CAAC,GAAG,aAAa,aAAa,QAAQ,SAAS,GAAG,OAAO,SAAS,CAAC;AAAA,EAC5F,IAAI,CAAC,CAAC;AACR;AACA,SAAS,aAAa;AACpB,SAAO,MAAM;AACf;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,aAAa,IAAI,IAAI,WAAW,IAAI,CAAC,EAAE,KAAK,EAAE;AAC9G;AACA,SAAS,aAAa,QAAQ,WAAW;AACvC,SAAO,IAAI,MAAM,KAAK,SAAS;AACjC;AACA,IAAM,OAAO;AACb,IAAM,iBAAN,MAAM,wBAAuB,gBAAgB;AAAA,EAC3C,cAAc;AACZ,UAAM;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,KAAK,QAAQ;AACX,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,IAAI,UAAU;AAAA;AAAA;AAAA,uFAG6D;AAAA,IACnF,WAAW,OAAO,WAAW,aAAa;AACxC,YAAM,IAAI,UAAU,yBAAyB;AAAA,IAC/C,WAAW,OAAO,OAAO,SAAS,aAAa;AAC7C,YAAM,IAAI,UAAU,mCAAmC;AAAA,IACzD;AACA,UAAM,KAAK,MAAM;AAAA,EACnB;AAAA,EACA,WAAW;AAAA,EAEX;AAAA,EACA,cAAc;AACZ,UAAM,SAAS;AAAA,EACjB;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,4BAA4B,CAAC,cAAc;AACjD,IAAM,oBAAoB,IAAI,eAAe,iCAAiC;AAC9E,IAAM,iBAAiB,IAAI,eAAe,oCAAoC;AAC9E,IAAM,gBAAgB,IAAI,eAAe,2BAA2B;AACpE,IAAM,kBAAkB,IAAI,eAAe,6BAA6B;AACxE,IAAM,mBAAmB,IAAI,eAAe,+CAA+C;AAC3F,IAAM,mBAAmB,IAAI,eAAe,8BAA8B;AAC1E,IAAM,oBAAoB,IAAI,eAAe,uCAAuC;AACpF,IAAM,iBAAiB,IAAI,eAAe,4BAA4B;AACtE,IAAM,kBAAkB,IAAI,eAAe,qCAAqC;AAChF,IAAM,oBAAoB,IAAI,eAAe,uCAAuC;AACpF,IAAM,mBAAmB,IAAI,eAAe,sCAAsC;AAClF,IAAM,kBAAkB,IAAI,eAAe,qCAAqC;AAChF,IAAM,0BAA0B,IAAI,eAAe,6CAA6C;AAChG,IAAM,mBAAmB,IAAI,eAAe,8BAA8B;AAI1E,IAAM,8BAA8B,IAAI,eAAe,yCAAyC;AAIhG,IAAM,gBAAgB,IAAI,eAAe,2BAA2B;AAKpE,IAAM,0BAA0B,IAAI,eAAe,6CAA6C;AAKhG,IAAM,sBAAsB,IAAI,eAAe,wCAAwC;AAIvF,IAAM,uBAAuB,IAAI,eAAe,iDAAiD;AAIjG,IAAM,wBAAwB,IAAI,eAAe,qCAAqC;AACtF,IAAM,gCAAgC,IAAI,eAAe,8CAA8C;AAOvG,IAAM,sBAAsB,IAAI,eAAe,iCAAiC;AAOhF,IAAM,yBAAyB,IAAI,eAAe,oCAAoC;AAmCtF,SAAS,gBAAgB,UAAU,eAAe,CAAC,GAAG;AACpD,QAAM,cAAc,OAAO,KAAK,QAAQ;AACxC,QAAM,gBAAgB,CAAC;AACvB,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,MAAM,YAAY,CAAC;AACzB,QAAI,OAAO,SAAS,GAAG,MAAM,YAAY;AACvC,oBAAc,GAAG,IAAI,SAAS,GAAG;AAAA,IACnC;AAAA,EACF;AACA,QAAM,mBAAmB,OAAO,KAAK,aAAa;AAClD,SAAO,SAAS,YAAY,OAAO,QAAQ;AACzC,YAAQ,UAAU,SAAY,eAAe;AAC7C,QAAI,aAAa;AACjB,UAAM,YAAY,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,YAAM,MAAM,iBAAiB,CAAC;AAC9B,YAAM,UAAU,cAAc,GAAG;AACjC,YAAM,sBAAsB,MAAM,GAAG;AACrC,YAAM,kBAAkB,QAAQ,qBAAqB,MAAM;AAC3D,gBAAU,GAAG,IAAI;AACjB,mBAAa,cAAc,oBAAoB;AAAA,IACjD;AACA,WAAO,aAAa,YAAY;AAAA,EAClC;AACF;AACA,SAAS,KAAK,QAAQ,aAAa;AACjC,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAO,QAAQ,WAAW,EAAE,OAAO,CAAC,QAAQ,QAAQ,OAAO,OAAO,QAAQ;AAAA,IAC1G,CAAC,GAAG,GAAG,OAAO,GAAG;AAAA,EACnB,CAAC,GAAG,CAAC,CAAC;AACR;AACA,SAAS,WAAW,WAAW;AAC7B,SAAO,SAAU,KAAK;AACpB,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,UAAU,UAAU,SAAS,CAAC;AAC3C,UAAM,OAAO,UAAU,MAAM,GAAG,EAAE;AAClC,WAAO,KAAK,YAAY,CAAC,UAAU,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,CAAC;AAAA,EACnE;AACF;AACA,SAAS,qBAAqB,gBAAgB,cAAc;AAC1D,MAAI,MAAM,QAAQ,YAAY,KAAK,aAAa,SAAS,GAAG;AAC1D,qBAAiB,QAAQ,MAAM,MAAM,CAAC,GAAG,cAAc,cAAc,CAAC;AAAA,EACxE;AACA,SAAO,CAAC,UAAU,iBAAiB;AACjC,UAAM,UAAU,eAAe,QAAQ;AACvC,WAAO,CAAC,OAAO,WAAW;AACxB,cAAQ,UAAU,SAAY,eAAe;AAC7C,aAAO,QAAQ,OAAO,MAAM;AAAA,IAC9B;AAAA,EACF;AACF;AACA,SAAS,4BAA4B,cAAc;AACjD,QAAM,iBAAiB,MAAM,QAAQ,YAAY,KAAK,aAAa,SAAS,IAAI,QAAQ,GAAG,YAAY,IAAI,OAAK;AAChH,SAAO,CAAC,SAAS,iBAAiB;AAChC,cAAU,eAAe,OAAO;AAChC,WAAO,CAAC,OAAO,WAAW;AACxB,cAAQ,UAAU,SAAY,eAAe;AAC7C,aAAO,QAAQ,OAAO,MAAM;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,oBAAN,cAAgC,WAAW;AAAC;AAC5C,IAAM,2BAAN,cAAuC,eAAe;AAAC;AACvD,IAAM,SAAS;AACf,IAAM,iBAAN,MAAM,wBAAuB,gBAAgB;AAAA,EAC3C,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,YAAY,cAAc,UAAU,gBAAgB;AAC9D,UAAM,eAAe,UAAU,YAAY,CAAC;AAC5C,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,YAAY,CAAC,OAAO,CAAC;AAAA,EAC5B;AAAA,EACA,YAAY,UAAU;AACpB,UAAM,WAAW,SAAS,OAAO,CAAC,aAAa;AAAA,MAC7C,UAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,UAAU,OAAOA,cAAa,aAAa,4BAA4B,YAAY,EAAEA,WAAU,YAAY,IAAI,qBAAqB,gBAAgB,YAAY,EAAEA,WAAU,YAAY;AAC9L,kBAAY,GAAG,IAAI;AACnB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,eAAe,CAAC,OAAO,CAAC;AAAA,EAC/B;AAAA,EACA,eAAe,UAAU;AACvB,SAAK,eAAe,SAAS,IAAI,OAAK,EAAE,GAAG,CAAC;AAAA,EAC9C;AAAA,EACA,WAAW,KAAK,SAAS;AACvB,SAAK,YAAY;AAAA,MACf,CAAC,GAAG,GAAG;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW,kCACX,KAAK,WACL;AAEL,SAAK,eAAe,OAAO,KAAK,QAAQ,CAAC;AAAA,EAC3C;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,eAAe,CAAC,UAAU,CAAC;AAAA,EAClC;AAAA,EACA,eAAe,aAAa;AAC1B,gBAAY,QAAQ,SAAO;AACzB,WAAK,WAAW,KAAK,KAAK,UAAU,GAAG;AAAA,IACzC,CAAC;AACD,SAAK,eAAe,WAAW;AAAA,EACjC;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,KAAK,KAAK,eAAe,KAAK,UAAU,KAAK,YAAY,CAAC;AAC/D,SAAK,WAAW,KAAK;AAAA,MACnB,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,SAAS,wBAAwB,GAAM,SAAS,aAAa,GAAM,SAAS,gBAAgB,GAAM,SAAS,eAAe,CAAC;AAAA,IACjK;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAA4B,CAAC,gBAAgB;AAAA,EACjD,SAAS;AAAA,EACT,aAAa;AACf,GAAG;AAAA,EACD,SAAS;AAAA,EACT,aAAa;AACf,CAAC;AACD,IAAM,wBAAN,MAAM,+BAA8B,QAAQ;AAAA,EAC1C,cAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,8BAA8B,GAAG;AAC/C,gBAAQ,uCAAuC,qCAAwC,sBAAsB,sBAAqB,IAAI,KAAK,sBAAqB;AAAA,MAClK;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,IACjC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oCAAoC,CAAC,qBAAqB;AAChE,IAAM,kBAAN,cAA8B,WAAW;AAAC;AAC1C,IAAM,QAAN,MAAM,eAAc,gBAAgB;AAAA,EAClC,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,UAAU,UAAU,gBAAgB,cAAc;AAC5D,UAAM,YAAY;AAClB,UAAM,kBAAkB,SAAS,KAAK,UAAU,cAAc,CAAC;AAC/D,UAAM,qBAAqB,gBAAgB,KAAK,eAAe,QAAQ,CAAC;AACxE,UAAM,OAAO;AAAA,MACX,OAAO;AAAA,IACT;AACA,UAAM,kBAAkB,mBAAmB,KAAK,KAAK,aAAa,IAAI,CAAC;AACvE,SAAK,oBAAoB,gBAAgB,UAAU,CAAC;AAAA,MAClD;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,KAAK,KAAK;AACf,qBAAe,KAAK,MAAM;AAAA,IAC5B,CAAC;AACD,SAAK,QAAQ,SAAS,MAAM;AAAA,MAC1B,eAAe;AAAA,MACf,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,YAAY;AACnC,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,cAAc,GAAG;AACpC,aAAO,KAAK,KAAK,QAAU,SAAS,cAAc,GAAM,SAAS,iBAAiB,GAAM,SAAS,qBAAqB,GAAM,SAAS,aAAa,CAAC;AAAA,IACrJ;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAM;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,YAAY,kBAAkB;AAAA,EACrC,OAAO;AACT,GAAG,CAAC,QAAQ,OAAO,GAAG;AACpB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,OAAO,QAAQ,OAAO,MAAM;AAAA,IAC5B;AAAA,EACF;AACF;AACA,IAAM,kBAAkB,CAAC,OAAO;AAAA,EAC9B,SAAS;AAAA,EACT,aAAa;AACf,CAAC;AAGD,IAAM,QAAN,MAAM,eAAc,WAAW;AAAA,EAC7B,YAAY,QAAQ,iBAAiB,gBAAgB;AACnD,UAAM;AACN,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,QAAQ,OAAO;AAAA,EACtB;AAAA,EACA,OAAO,gBAAgB,OAAO;AAC5B,WAAO,OAAO,KAAK,MAAM,aAAa,GAAG,KAAK,EAAE,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,UAAU,SAAS;AAC9B,WAAO,SAAS,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,OAAO;AAAA,EACvD;AAAA,EACA,KAAK,UAAU;AACb,UAAM,QAAQ,IAAI,OAAM,MAAM,KAAK,iBAAiB,KAAK,cAAc;AACvE,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,QAAQ;AACf,SAAK,gBAAgB,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,KAAK,QAAQ;AACX,SAAK,gBAAgB,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,MAAM,KAAK;AACT,SAAK,gBAAgB,MAAM,GAAG;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA,EACA,WAAW,KAAK,SAAS;AACvB,SAAK,eAAe,WAAW,KAAK,OAAO;AAAA,EAC7C;AAAA,EACA,cAAc,KAAK;AACjB,SAAK,eAAe,cAAc,GAAG;AAAA,EACvC;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,cAAc,GAAG;AACpC,aAAO,KAAK,KAAK,QAAU,SAAS,eAAe,GAAM,SAAS,cAAc,GAAM,SAAS,cAAc,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,OAAM;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAkB,CAAC,KAAK;AAC9B,SAAS,OAAO,aAAa,gBAAgB,OAAO;AAClD,SAAO,SAAS,eAAe,SAAS;AACtC,QAAI;AACJ,QAAI,OAAO,gBAAgB,UAAU;AACnC,YAAM,aAAa,CAAC,aAAa,GAAG,KAAK,EAAE,OAAO,OAAO;AACzD,gBAAU,QAAQ,KAAK,MAAM,aAAa,GAAG,UAAU,CAAC;AAAA,IAC1D,WAAW,OAAO,gBAAgB,YAAY;AAC5C,gBAAU,QAAQ,KAAK,IAAI,YAAU,YAAY,QAAQ,WAAW,CAAC,CAAC;AAAA,IACxE,OAAO;AACL,YAAM,IAAI,UAAU,oBAAoB,OAAO,WAAW,uDAA4D;AAAA,IACxH;AACA,WAAO,QAAQ,KAAK,qBAAqB,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,oBAAoB;AAC1B,SAAS,YAAY,QAAQ;AAC3B,SAAO,WAAW;AACpB;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,WAAW;AACpB;AACA,SAAS,QAAQ,QAAQ;AACvB,SAAO,MAAM,QAAQ,MAAM;AAC7B;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,aAAa,QAAQ;AAC5B,SAAO,OAAO,WAAW,YAAY,WAAW;AAClD;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,aAAa,MAAM,KAAK,CAAC,QAAQ,MAAM;AAChD;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,CAAC,SAAS,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,OAAO,eAAe,MAAM;AACpD,SAAO,oBAAoB,OAAO,aAAa,oBAAoB;AACrE;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,WAAW,MAAM,KAAK,OAAO,eAAe,MAAM;AAC3D;AACA,SAAS,eAAe,QAAQ,cAAc;AAC5C,SAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY;AAClE;AACA,IAAI,uBAAuB;AAC3B,SAAS,uBAAuB,OAAO;AACrC,yBAAuB;AACzB;AACA,SAAS,wBAAwB;AAC/B,SAAO;AACT;AACA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,MAAM;AACf;AACA,SAAS,mBAAmB,MAAM,eAAe,YAAY;AAC3D,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,CAAC,WAAW,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;AAC1C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,cAAc,eAAe;AAClD,SAAO,eAAe,cAAc,cAAc,aAAa;AACjE;AACA,SAAS,eAAe,cAAc,mBAAmB,cAAc,gBAAgB,cAAc;AACnG,MAAI,gBAAgB;AAEpB,MAAI,aAAa;AACjB,MAAI;AACJ,WAAS,QAAQ;AACf,oBAAgB;AAChB,iBAAa;AAAA,EACf;AACA,WAAS,UAAU,SAAS,QAAW;AACrC,qBAAiB;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,WAAS,cAAc;AACrB,qBAAiB;AAAA,EACnB;AAGA,WAAS,WAAW;AAClB,QAAI,mBAAmB,QAAW;AAChC,aAAO,eAAe;AAAA,IACxB;AACA,QAAI,CAAC,eAAe;AAClB,mBAAa,aAAa,MAAM,MAAM,SAAS;AAC/C,sBAAgB;AAChB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,mBAAmB,WAAW,eAAe,gBAAgB,GAAG;AACnE,aAAO;AAAA,IACT;AACA,UAAM,YAAY,aAAa,MAAM,MAAM,SAAS;AACpD,oBAAgB;AAChB,QAAI,cAAc,YAAY,SAAS,GAAG;AACxC,aAAO;AAAA,IACT;AACA,iBAAa;AACb,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,sBAAsB,cAAc,EAAE,GAAG,KAAK;AACvD;AACA,SAAS,eAAe,OAAO,WAAWD,QAAO,mBAAmB;AAClE,MAAIA,WAAU,QAAW;AACvB,UAAME,QAAO,UAAU,IAAI,QAAM,GAAG,KAAK,CAAC;AAC1C,WAAO,kBAAkB,SAAS,MAAM,MAAMA,KAAI;AAAA,EACpD;AACA,QAAM,OAAO,UAAU,IAAI,QAAM,GAAG,OAAOF,MAAK,CAAC;AACjD,SAAO,kBAAkB,SAAS,MAAM,MAAM,CAAC,GAAG,MAAMA,MAAK,CAAC;AAChE;AA6EA,SAAS,sBAAsB,SAAS,UAAU;AAAA,EAChD,SAAS;AACX,GAAG;AACD,SAAO,YAAa,OAAO;AACzB,QAAI,OAAO;AACX,QAAI,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AAC1B,YAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,aAAO,CAAC,GAAG,MAAM,GAAG,IAAI;AAAA,IAC1B,WAAW,KAAK,WAAW,KAAK,sBAAsB,KAAK,CAAC,CAAC,GAAG;AAC9D,aAAO,mCAAmC,KAAK,CAAC,CAAC;AAAA,IACnD;AACA,UAAM,YAAY,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AAC/C,UAAM,YAAY,KAAK,KAAK,SAAS,CAAC;AACtC,UAAM,oBAAoB,UAAU,OAAO,cAAY,SAAS,WAAW,OAAO,SAAS,YAAY,UAAU;AACjH,UAAM,oBAAoB,QAAQ,YAAaG,YAAW;AACxD,aAAO,UAAU,MAAM,MAAMA,UAAS;AAAA,IACxC,CAAC;AACD,UAAM,gBAAgB,eAAe,SAAU,OAAOH,QAAO;AAC3D,aAAO,QAAQ,QAAQ,MAAM,MAAM,CAAC,OAAO,WAAWA,QAAO,iBAAiB,CAAC;AAAA,IACjF,CAAC;AACD,aAAS,UAAU;AACjB,oBAAc,MAAM;AACpB,wBAAkB,MAAM;AACxB,wBAAkB,QAAQ,cAAY,SAAS,QAAQ,CAAC;AAAA,IAC1D;AACA,WAAO,OAAO,OAAO,cAAc,UAAU;AAAA,MAC3C;AAAA,MACA,WAAW,kBAAkB;AAAA,MAC7B,WAAW,cAAc;AAAA,MACzB,aAAa,cAAc;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AACA,SAAS,sBAAsB,aAAa;AAC1C,SAAO,eAAe,WAAS;AAC7B,UAAM,eAAe,MAAM,WAAW;AACtC,QAAI,CAAC,sBAAsB,KAAK,UAAU,KAAK,EAAE,eAAe,QAAQ;AACtE,cAAQ,KAAK,kCAAkC,WAAW,0JAAyK,WAAW,sCAA2C,WAAW,qJAA+J;AAAA,IACrc;AACA,WAAO;AAAA,EACT,GAAG,kBAAgB,YAAY;AACjC;AACA,SAAS,sBAAsB,WAAW;AACxC,SAAO,CAAC,CAAC,aAAa,OAAO,cAAc,YAAY,OAAO,OAAO,SAAS,EAAE,MAAM,cAAY,OAAO,aAAa,UAAU;AAClI;AACA,SAAS,mCAAmC,qBAAqB;AAC/D,QAAM,YAAY,OAAO,OAAO,mBAAmB;AACnD,QAAM,aAAa,OAAO,KAAK,mBAAmB;AAClD,QAAM,YAAY,IAAI,oBAAoB,WAAW,OAAO,CAAC,QAAQ,KAAK,UAAW,iCAChF,SADgF;AAAA,IAEnF,CAAC,GAAG,GAAG,gBAAgB,KAAK;AAAA,EAC9B,IAAI,CAAC,CAAC;AACN,SAAO,CAAC,GAAG,WAAW,SAAS;AACjC;AA4FA,SAAS,cAAc,eAAe;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,kBAAkB,sBAAsB,IAAI;AAClD,QAAM,kBAAkB,sBAAsB,iBAAiB,OAAO;AACtE,QAAM,gBAAgB;AAAA,IACpB,CAAC,SAAS,WAAW,IAAI,CAAC,OAAO,GAAG;AAAA,KACjC;AAEL,QAAM,iBAAiB,wBAAwB,sBAAsB,aAAa,IAAI,CAAC;AACvF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,KACG,gBACA;AAEP;AACA,SAAS,sBAAsB,iBAAiB,SAAS;AACvD,QAAM,eAAe,gBAAgB,OAAO;AAC5C,QAAM,aAAa,cAAc,YAAY,IAAI,OAAO,KAAK,YAAY,IAAI,CAAC;AAC9E,SAAO,WAAW,OAAO,CAAC,iBAAiB,cAAe,iCACrD,kBADqD;AAAA,IAExD,CAAC,SAAS,WAAW,SAAS,CAAC,EAAE,GAAG,eAAe,iBAAiB,iBAAe,cAAc,SAAS,CAAC;AAAA,EAC7G,IAAI,CAAC,CAAC;AACR;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,QAAW;AAAA,IACxB,MAAM;AAAA,EACR,CAAC;AACH;AACA,SAAS,qBAAqB,UAAU;AACtC,SAAO,oBAAoB,iBAAiB,OAAO,QAAQ,IAAI;AACjE;AACA,SAAS,oBAAoB,SAAS,eAAe;AACnD,SAAO,cAAc,IAAI,CAAC,MAAM,UAAU;AACxC,QAAI,QAAQ,KAAK,aAAa,gBAAgB;AAC5C,YAAM,OAAO,OAAO,QAAQ,KAAK,CAAC;AAClC,aAAO;AAAA,QACL,KAAK,KAAK;AAAA,QACV,gBAAgB,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,QAC5D,cAAc,KAAK,eAAe,KAAK,eAAe,CAAC;AAAA,QACvD,cAAc,KAAK;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,uBAAuB,mBAAmB;AACjD,SAAO,kBAAkB,IAAI,aAAW;AACtC,WAAO,mBAAmB,iBAAiB,OAAO,OAAO,IAAI;AAAA,EAC/D,CAAC;AACH;AACA,SAAS,qBAAqB,cAAc;AAC1C,MAAI,OAAO,iBAAiB,YAAY;AACtC,WAAO,aAAa;AAAA,EACtB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,cAAc,0BAA0B;AACnE,SAAO,aAAa,OAAO,wBAAwB;AACrD;AACA,SAAS,uBAAuB;AAC9B,QAAM,QAAQ,OAAO,OAAO;AAAA,IAC1B,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,OAAO;AACT,UAAM,IAAI,UAAU,yGAAyG;AAAA,EAC/H;AACA,SAAO;AACT;AACA,SAAS,6BAA6B,SAAS,QAAQ;AACrD,SAAO,SAAU,OAAO,QAAQ;AAC9B,UAAM,MAAM,OAAO,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI;AACrD,UAAM,YAAY,QAAQ,OAAO,GAAG;AACpC,WAAO,OAAO,MAAM,IAAI,OAAO,SAAS,IAAI;AAAA,EAC9C;AACF;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,OAAO,MAAM;AACpB,QAAM,mBAAmB,WAAW,MAAM;AAC1C,SAAO,oBAAoB,MAAM,EAAE,QAAQ,UAAQ;AAEjD,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB;AAAA,IACF;AACA,QAAI,eAAe,QAAQ,IAAI,MAAM,mBAAmB,SAAS,YAAY,SAAS,YAAY,SAAS,cAAc,OAAO;AAC9H,YAAM,YAAY,OAAO,IAAI;AAC7B,WAAK,aAAa,SAAS,KAAK,WAAW,SAAS,MAAM,CAAC,OAAO,SAAS,SAAS,GAAG;AACrF,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,8BAA8B,SAAS,QAAQ;AACtD,SAAO,SAAU,OAAO,QAAQ;AAC9B,QAAI,OAAO,OAAO,MAAM,GAAG;AACzB,YAAM,uBAAuB,kBAAkB,MAAM;AACrD,4BAAsB,sBAAsB,QAAQ;AAAA,IACtD;AACA,UAAM,YAAY,QAAQ,OAAO,MAAM;AACvC,QAAI,OAAO,MAAM,GAAG;AAClB,YAAM,sBAAsB,kBAAkB,SAAS;AACvD,4BAAsB,qBAAqB,OAAO;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO,CAAC,GAAG;AAE5C,OAAK,YAAY,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,WAAW,GAAG;AAChE,WAAO;AAAA,MACL,MAAM,CAAC,MAAM;AAAA,MACb,OAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,SAAO,KAAK,OAAO,CAAC,QAAQ,QAAQ;AAClC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,OAAO,GAAG;AAExB,QAAI,YAAY,KAAK,GAAG;AACtB,aAAO;AAAA,IACT;AACA,QAAI,YAAY,KAAK,KAAK,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,GAAG;AACnH,aAAO;AAAA,IACT;AACA,QAAI,cAAc,KAAK,GAAG;AACxB,aAAO,kBAAkB,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC;AAAA,IAChD;AACA,WAAO;AAAA,MACL,MAAM,CAAC,GAAG,MAAM,GAAG;AAAA,MACnB;AAAA,IACF;AAAA,EACF,GAAG,KAAK;AACV;AACA,SAAS,sBAAsB,gBAAgB,SAAS;AACtD,MAAI,mBAAmB,OAAO;AAC5B;AAAA,EACF;AACA,QAAM,qBAAqB,eAAe,KAAK,KAAK,GAAG;AACvD,QAAM,QAAQ,IAAI,MAAM,2BAA2B,OAAO,QAAQ,kBAAkB,MAAM,iBAAiB,UAAU,OAAO,iBAAiB;AAC7I,QAAM,QAAQ,eAAe;AAC7B,QAAM,qBAAqB;AAC3B,QAAM;AACR;AACA,SAAS,0BAA0B,SAAS,QAAQ;AAClD,SAAO,SAAU,OAAO,QAAQ;AAC9B,QAAI,OAAO,OAAO,MAAM,KAAK,CAAI,OAAO,gBAAgB,GAAG;AACzD,YAAM,IAAI,MAAM,WAAW,OAAO,IAAI,6BAA6B,iBAAiB,2BAA2B;AAAA,IACjH;AACA,WAAO,QAAQ,OAAO,MAAM;AAAA,EAC9B;AACF;AACA,SAAS,0BAA0B,eAAe;AAChD,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,MACL,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,yBAAyB;AAAA,MACzB,0BAA0B;AAAA,MAC1B,0BAA0B;AAAA,MAC1B,4BAA4B;AAAA,OACzB;AAAA,EAEP;AACA,SAAO;AAAA,IACL,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,EAC9B;AACF;AACA,SAAS,oCAAoC;AAAA,EAC3C;AAAA,EACA;AACF,GAAG;AACD,SAAO,aAAW,+BAA+B,6BAA6B,8BAA8B,SAAS;AAAA,IACnH,QAAQ,YAAU,+BAA+B,CAAC,iBAAiB,MAAM;AAAA,IACzE,OAAO,MAAM;AAAA,EACf,CAAC,IAAI;AACP;AACA,SAAS,mCAAmC;AAAA,EAC1C;AAAA,EACA;AACF,GAAG;AACD,SAAO,aAAW,4BAA4B,0BAA0B,6BAA6B,SAAS;AAAA,IAC5G,QAAQ,YAAU,4BAA4B,CAAC,iBAAiB,MAAM;AAAA,IACtE,OAAO,MAAM;AAAA,EACf,CAAC,IAAI;AACP;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,OAAO,KAAK,WAAW,OAAO;AACvC;AACA,SAAS,+BAA+B;AAAA,EACtC;AACF,GAAG;AACD,SAAO,aAAW,2BAA2B,0BAA0B,SAAS;AAAA,IAC9E,QAAQ,YAAU,4BAA4B,CAAC,iBAAiB,MAAM;AAAA,EACxE,CAAC,IAAI;AACP;AACA,SAAS,qBAAqB,eAAe;AAC3C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,oBAAoB;AAAA,EAC7B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,mBAAmB;AAAA,IAC1B,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,qBAAqB;AAAA,IAC5B,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,qBAAqB;AAAA,IAC5B,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,qBAAqB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AACA,SAAS,+BAA+B;AACtC,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,qBAAqB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AACA,SAAS,sBAAsB,eAAe;AAC5C,SAAO;AACT;AACA,SAAS,2BAA2B,QAAQ;AAC1C,MAAI,CAAC,OAAO,4BAA4B;AACtC;AAAA,EACF;AACA,QAAM,aAAa,OAAO,QAAQ,uBAAuB,EAAE,OAAO,CAAC,CAAC,EAAE,aAAa,MAAM,gBAAgB,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI;AAChI,MAAI,WAAW,QAAQ;AACrB,UAAM,IAAI,MAAM,+CAA+C,WAAW,IAAI,UAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,iBAAiB,6BAA6B;AAAA,EAClK;AACF;AAuBA,SAAS,aAAa,oBAAoB,UAAU,SAAS,CAAC,GAAG;AAC/D,SAAO,yBAAyB,CAAC,GAAG,cAAc,oBAAoB,UAAU,MAAM,GAAG,0BAA0B,CAAC;AACtH;AACA,SAAS,cAAc,WAAW,CAAC,GAAG,SAAS,CAAC,GAAG;AACjD,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,cAAc;AAAA,EACvB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa,oBAAoB,iBAAiB,WAAW;AAAA,EAC/D,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,mBAAmB,CAAC,IAAI,OAAO,eAAe,CAAC,CAAC;AAAA,IACvD,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO,eAAe,OAAO,eAAe,CAAC;AAAA,EACzD,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,eAAe,2BAA2B;AAAA,IACjD,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO,iBAAiB,OAAO,iBAAiB;AAAA,EAC5D,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,kBAAkB,uBAAuB;AAAA,IAChD,YAAY;AAAA,EACd,GAAG,2BAA2B,2BAA2B,mCAAmC,iBAAiB,iBAAiB,qBAAqB,OAAO,aAAa,GAAG,6BAA6B,CAAC;AAC1M;AACA,SAAS,2BAA2B;AAClC,SAAO,cAAc;AACrB,SAAO,iBAAiB;AACxB,SAAO,qBAAqB;AAC5B,SAAO,KAAK;AACZ,SAAO,mBAAmB;AAAA,IACxB,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,+BAA+B;AAAA,IACpC,UAAU;AAAA,EACZ,CAAC;AACH;AAKA,IAAM,6BAA6B,CAAC;AAAA,EAClC,SAAS;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS;AAAA,EACT,OAAO;AAAA,EACP,aAAa;AACX,WAAO,MAAM,OAAO,mBAAmB;AAAA,EACzC;AACF,CAAC;AAgBD,SAAS,aAAa,UAAU,QAAQ;AACtC,SAAO,yBAAyB,CAAC,GAAG,cAAc,UAAU,MAAM,GAAG,0BAA0B,CAAC;AAClG;AACA,SAAS,8BAA8B;AACrC,SAAO,mBAAmB;AAC1B,QAAM,WAAW,OAAO,eAAe;AACvC,QAAM,kBAAkB,OAAO,gBAAgB;AAC/C,QAAM,iBAAiB,OAAO,cAAc;AAC5C,SAAO,+BAA+B;AAAA,IACpC,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,QAAQ,SAAS,IAAI,CAAC,SAAS,UAAU;AAC7C,UAAM,2BAA2B,gBAAgB,MAAM;AAEvD,UAAM,WAAW,yBAAwC,KAAK;AAC9D,WAAO,iCACF,UADE;AAAA,MAEL;AAAA,MACA,cAAc,qBAAqB,QAAQ,YAAY;AAAA,IACzD;AAAA,EACF,CAAC;AACD,iBAAe,YAAY,KAAK;AAClC;AAKA,IAAM,6BAA6B,CAAC;AAAA,EAClC,SAAS;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS;AAAA,EACT,OAAO;AAAA,EACP,aAAa;AACX,WAAO,MAAM,OAAO,sBAAsB;AAAA,EAC5C;AACF,CAAC;AACD,SAAS,cAAc,oBAAoB,UAAU,SAAS,CAAC,GAAG;AAChE,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,8BAA8B,SAAS,CAAC,IAAI;AAAA,EACxD,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,MACR,KAAK,8BAA8B,SAAS,mBAAmB,OAAO;AAAA,MACtE,gBAAgB,EAAE,kBAAkB,mBAAmB,OAAO,iBAAiB,OAAO,iBAAiB;AAAA,MACvG,cAAc,EAAE,kBAAkB,mBAAmB,OAAO,eAAe,OAAO,eAAe,CAAC;AAAA,MAClG,cAAc,EAAE,kBAAkB,mBAAmB,OAAO,eAAe,OAAO,eAAe;AAAA,IACnG;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,kBAAkB,cAAc;AAAA,IACvC,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,8BAA8B,SAAS,mBAAmB,UAAU;AAAA,EAChF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,aAAa,oBAAoB,iBAAiB,WAAW;AAAA,EAC/D,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,mBAAmB,CAAC,IAAI,OAAO,uBAAuB,CAAC,CAAC;AAAA,IAC/D,YAAY;AAAA,EACd,GAAG,6BAA6B,CAAC;AACnC;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,UAAU,UAAU,iBAAiB,OAAO,OAAO,aAAa;AAAA,EAAC;AAAA,EAE7E,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,SAAS,cAAc,GAAM,SAAS,iBAAiB,GAAM,SAAS,qBAAqB,GAAM,SAAS,KAAK,GAAM,SAAS,mBAAmB,CAAC,GAAM,SAAS,+BAA+B,CAAC,CAAC;AAAA,IACzO;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,UAAU,iBAAiB,gBAAgB,MAAM,aAAa;AACxE,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,UAAM,QAAQ,SAAS,IAAI,CAAC,SAAS,UAAU;AAC7C,YAAM,2BAA2B,gBAAgB,MAAM;AAEvD,YAAM,WAAW,yBAAwC,KAAK;AAC9D,aAAO,iCACF,UADE;AAAA,QAEL;AAAA,QACA,cAAc,qBAAqB,QAAQ,YAAY;AAAA,MACzD;AAAA,IACF,CAAC;AACD,mBAAe,YAAY,KAAK;AAAA,EAClC;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,eAAe,eAAe,KAAK,QAAQ;AAAA,EAClD;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,SAAS,eAAe,GAAM,SAAS,gBAAgB,GAAM,SAAS,cAAc,GAAM,SAAS,eAAe,GAAM,SAAS,+BAA+B,CAAC,CAAC;AAAA,IAC5M;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,QAAQ,UAAU,QAAQ;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,GAAG,cAAc,UAAU,MAAM,CAAC;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO,WAAW,oBAAoB,UAAU,SAAS,CAAC,GAAG;AAC3D,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,GAAG,cAAc,oBAAoB,UAAU,MAAM,CAAC;AAAA,IACpE;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAa;AAAA,IAChC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAgBH,SAAS,MAAM,MAAM;AACnB,QAAM,UAAU,KAAK,IAAI;AACzB,QAAM,QAAQ,KAAK,IAAI,aAAW,QAAQ,IAAI;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AA8BA,SAAS,cAAc,iBAAiB,KAAK;AAC3C,QAAMI,OAAM,oBAAI,IAAI;AACpB,aAAWC,OAAM,KAAK;AACpB,eAAW,QAAQA,IAAG,OAAO;AAC3B,YAAM,kBAAkBD,KAAI,IAAI,IAAI;AACpC,UAAI,iBAAiB;AACnB,cAAM,aAAa,CAAC,OAAO,WAAWC,IAAG,QAAQ,gBAAgB,OAAO,MAAM,GAAG,MAAM;AACvF,QAAAD,KAAI,IAAI,MAAM,UAAU;AAAA,MAC1B,OAAO;AACL,QAAAA,KAAI,IAAI,MAAMC,IAAG,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAU,QAAQ,cAAc,QAAQ;AAC7C,UAAM,UAAUD,KAAI,IAAI,OAAO,IAAI;AACnC,WAAO,UAAU,QAAQ,OAAO,MAAM,IAAI;AAAA,EAC5C;AACF;", "names": ["props", "reducers", "args", "selectors", "map", "on"]}