﻿using Application.Infrastructure.Models.Response.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request
{
    public class UpdateUserRolesRequest
    {
        public required string UserId { get; set; }
        public required IList<UserRolesResponse> UserRoles { get; set; }
    }
}
