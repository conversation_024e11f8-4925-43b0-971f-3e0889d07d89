﻿using Application.Infrastructure.Models.Request.Category.Province;
using Application.Infrastructure.Models.Request.Category.Rank;
using Application.Infrastructure.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class ProvinceController : ControllerBase
    {
        private readonly IProvinceService _provinceService;

        public ProvinceController(IProvinceService provinceervice)
        {
            _provinceService = provinceervice;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchProvinceAsync([FromBody] SearchProvinceRequest request)
        {
            try
            {
                var response = await _provinceService.SearchProvinceAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateProvinceAsync([FromBody] CreateProvinceRequest request)
        {
            var response = await _provinceService.CreateProvinceAsync(request);
            return Ok(response);
        }



        [HttpPut("Update")]
        public async Task<IActionResult> UpdateProvinceAsync([FromBody] UpdateProvinceRequest request)
        {
            try
            {
                var response = await _provinceService.UpdateProvinceAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteProvinceAsync([FromBody] DeleteProvinceRequest request)
        {
            try
            {
                var response = await _provinceService.DeleteProvinceAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
