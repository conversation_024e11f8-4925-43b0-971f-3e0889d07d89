﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Rank
{
    public class UpdateRankRequest : CreateRankRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateRankRequest, RankEntity>> Expression
        {
            get
            {
                return entity => new RankEntity
                {
                    Id = entity.Id,
                    RankCode = entity.RankCode,
                    RankName = entity.RankName,
                    Description = entity.Description,
                    Active = true,
                };
            }
        }

        public static RankEntity Create(UpdateRankRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
