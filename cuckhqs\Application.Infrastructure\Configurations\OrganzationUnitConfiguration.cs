﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.OrganizationUnit;

namespace ql_tb_vk_vt.Infrastructure.Configurations
{
    public class OrganizationUnitConfiguration : IEntityTypeConfiguration<OrganizationUnitEntity>
    {
        public void Configure(EntityTypeBuilder<OrganizationUnitEntity> builder)
        {
            builder.ToTable("OrganizationUnit");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).HasColumnName("Id");
            builder.Property(x => x.ParentId).HasColumnName("ParentId");
            builder.Property(x => x.IsRoot).HasColumnName("IsRoot");
            builder.Property(x => x.OrganizationUnitCode).HasColumnName("OrganizationUnitCode");
            builder.Property(x => x.ParentCode).HasColumnName("ParentCode");
            builder.Property(x => x.Classify).HasColumnName("Classify");
            builder.Property(x => x.ClassifyGroup).HasColumnName("ClassifyGroup");
            builder.Property(x => x.TrainingMaterialCode).HasColumnName("TrainingMaterialCode");
            builder.Property(x => x.Name).HasColumnName("OrganizationUnitName");
            builder.Property(x => x.ShortOrganizationUnitName).HasColumnName("ShortOrganizationUnitName");
            builder.Property(x => x.Tel).HasColumnName("Tel");
            builder.Property(x => x.Fax).HasColumnName("Fax");
            builder.Property(x => x.Email).HasColumnName("Email");
            builder.Property(x => x.Address).HasColumnName("Address");
            builder.Property(x => x.Website).HasColumnName("Website");
            builder.Property(x => x.Director).HasColumnName("Director");
            builder.Property(x => x.AccountNumber).HasColumnName("AccountNumber");
            builder.Property(x => x.BankName).HasColumnName("BankName");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
            builder.Property(x => x.FullOrganizationUnitName).HasColumnName("FullOrganizationUnitName");
        }
    }
}
