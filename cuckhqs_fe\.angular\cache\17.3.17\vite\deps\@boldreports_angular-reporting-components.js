import {
  CommonModule
} from "./chunk-H3UNIER7.js";
import {
  ChangeDetectorRef,
  Component,
  ContentChild,
  ElementRef,
  EventEmitter,
  Injectable,
  Input,
  InputFlags,
  IterableDiffers,
  KeyValueDiffers,
  NgModule,
  Output,
  Type,
  forwardRef,
  setClassMetadata,
  ɵɵInheritDefinitionFeature,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵinject,
  ɵɵinvalidFactory
} from "./chunk-7VXZRWVL.js";
import "./chunk-DARGOXGJ.js";
import "./chunk-HQARRG7I.js";
import "./chunk-4A64JP2N.js";
import "./chunk-EIB7IA3J.js";

// node_modules/@boldreports/angular-reporting-components/fesm2020/boldreports-angular-reporting-components.mjs
var currentTemplateElement;
var firstVal = {};
var Utils = class {
  static IterateAndGetChanges(obj) {
    if (ej.isNullOrUndefined(obj.tags) || obj.tags.length === 0) {
      return null;
    }
    let res = {};
    for (let i = 0, tags = obj.tags; i < tags.length; i++) {
      let tag = tags[i], tagElement = obj["tag_" + tag.replace(/\./g, "_")];
      if (!ej.isNullOrUndefined(tagElement) && tagElement.hasChanges) {
        res[tag] = tagElement.getChangesAndReset();
      }
    }
    return res;
  }
};
Utils.ɵfac = function Utils_Factory(t) {
  return new (t || Utils)();
};
Utils.ɵprov = ɵɵdefineInjectable({
  token: Utils,
  factory: Utils.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Utils, [{
    type: Injectable
  }], null, null);
})();
var BoldReportComponents = class _BoldReportComponents {
  // tslint:disable-next-line:max-line-length
  constructor(controlName, el, cdRef, tags, ejIterableDiffers, _ejKeyValueDiffers) {
    this.controlName = controlName;
    this.el = el;
    this.cdRef = cdRef;
    this.tags = tags;
    this.ejIterableDiffers = ejIterableDiffers;
    this._ejKeyValueDiffers = _ejKeyValueDiffers;
    this.model = {};
    this.outputs = [];
    this.inputs = [];
    this.twoways = [];
    this.firstCheck = true;
    this.ejIterable = this.ejIterableDiffers.find([]).create(null);
    this.ejKeyValueDif = _ejKeyValueDiffers.find([]).create();
  }
  static bindAndRaiseEvent(instance, model, event) {
    if (!event.startsWith("model.")) {
      let originalEvt = event.startsWith("bold") ? event.substr(4) : event;
      model[originalEvt] = function(params) {
        instance[event + "_output"]["emit"](params);
      };
    }
  }
  createTwoways(twoways) {
    if (!twoways) {
      return;
    }
    let model = this.model;
    for (let i = 0; i < twoways.length; i++) {
      let element = twoways[i].replace(/\_/g, ".");
      ej.createObject(element + "Change", this[twoways[i] + "_twoChange"], model);
      ej.createObject(element, this.addTwoways(element), model);
    }
  }
  addTwoways(prop) {
    let model = this.model, value = firstVal;
    return function(newVal, isApp) {
      if (value === firstVal) {
        value = ej.getObject(prop + "_two", model);
        if (value === void 0) {
          value = ej.getObject(prop, this === void 0 || this.defaults === void 0 ? {} : this.defaults);
        }
      }
      if (newVal === void 0) {
        return value;
      }
      if (value === newVal) {
        return;
      }
      value = newVal;
      if (!isApp) {
        ej.createObject(prop + "_two", newVal, model);
        ej.getObject(prop + "Change", model).emit(newVal);
      }
    };
  }
  ngOnInit() {
    for (let key in this) {
      if (key.indexOf("_input") != -1) this.inputs.push(key);
      if (key.indexOf("_output") != -1) this.outputs.push(key);
      if (key.indexOf("_two") != -1 && key.indexOf("_twoChange") == -1) this.twoways.push(key.replace("_two", ""));
    }
    if (ej.isNullOrUndefined(this["options"])) {
      this.createTwoways(this.twoways);
    }
  }
  ngAfterContentInit() {
    this.firstCheck = false;
    let model = this.model, events = this.outputs;
    if (events) {
      for (let i = 0; i < events.length; i++) {
        let event = events[i].replace("_output", "");
        _BoldReportComponents.bindAndRaiseEvent(this, model, event);
      }
    }
    if (ej.isNullOrUndefined(this["options"])) {
      for (let i = 0; i < this.tags.length; i++) {
        let element = this.tags[i], item = this["tag_" + element.replace(/\./g, "_")];
        if (!ej.isNullOrUndefined(item)) {
          ej.createObject(element, item.getList(), this.model);
        }
      }
      for (let i = 0; i < this.inputs.length; i++) {
        let property = this.inputs[i];
        let modelProperty = this.inputs[i].replace("_input", "");
        if (this[property] != null) {
          if (modelProperty.indexOf("_") == -1) {
            if (this.model[modelProperty]) {
              $.extend(true, this.model[modelProperty], this[property]);
            } else {
              this.model[modelProperty] = this[property];
            }
          } else if (modelProperty.indexOf("_two") == -1) {
            let tempObj = {};
            let key = modelProperty.replace(/\_/g, ".");
            ej.createObject(key, this[property], tempObj);
            let rootProp = key.split(".")[0];
            if (this.model[rootProp] == void 0) this.model[rootProp] = {};
            $.extend(true, this.model[rootProp], tempObj[rootProp]);
          }
        }
      }
      for (let i = 0; i < this.twoways.length; i++) {
        let twoway = this.twoways[i];
        let twowayProperty = twoway + "_two";
        if (this[twowayProperty] != null) {
          if (twoway.indexOf("_") == -1) {
            this.model[twowayProperty] = this[twowayProperty];
          } else {
            let tempObj = {};
            let key = twoway.replace(/\_/g, ".") + "_two";
            ej.createObject(key, this[twowayProperty], tempObj);
            let rootProp = twowayProperty.split("_")[0];
            $.extend(true, this.model[rootProp], tempObj[rootProp]);
          }
        }
      }
    } else this.model = jQuery.extend(this.model, this["options"]);
  }
  ngDoCheck() {
    if (ej.isNullOrUndefined(this["options"])) {
      this.twoways.forEach((element) => {
        if (this[element + "_two"] instanceof Array) {
          let changes = this.ejIterable.diff(this[element + "_two"]);
          if (changes) {
            let ngChanges = {};
            if (this.widget != void 0) {
              ngChanges = this.getTwowayChanges(changes.collection, ngChanges, element.replace(/\_/g, "."));
              ej.createObject(element.replace(/\_/g, ".") + ".two", changes.collection, ngChanges);
              this.widget["setModel"](ngChanges, $.isPlainObject(ngChanges));
            }
          }
        }
      });
    } else {
      let changes = this.ejKeyValueDif.diff(this["options"]);
      if (changes) {
        if (this.widget != void 0) {
          var ngchanges = {};
          changes.forEachChangedItem((changedprop) => {
            ej.createObject(changedprop.key, changedprop.currentValue, ngchanges);
          });
          this.widget["setModel"](ngchanges, $.isPlainObject(ngchanges));
        }
      }
    }
  }
  ngAfterViewInit() {
    let nativeElement = this.isEditor ? $(this.el.nativeElement.children) : $(this.el.nativeElement);
    let controlName = this.lowerCaseFirstLetter(this.controlName);
    this.widget = $(nativeElement)[controlName](this.model)[controlName]("instance");
  }
  lowerCaseFirstLetter(string) {
    return string[0].toLowerCase() + string.slice(1);
  }
  ngOnChanges(changes) {
    if (this.firstCheck) {
      return;
    }
    let ngChanges = {};
    if (ej.isNullOrUndefined(this["options"])) {
      for (let key in changes) {
        let element = changes[key];
        if (element.previousValue === element.currentValue) {
          break;
        }
        key = key.replace("_input", "").replace(/\_/g, ".");
        if (key.endsWith(".two")) {
          let oKey = key.replace(".two", "");
          ngChanges = this.getTwowayChanges(element.currentValue, ngChanges, oKey);
        }
        ej.createObject(key, element.currentValue, ngChanges);
      }
      this.widget["setModel"](ngChanges, $.isPlainObject(ngChanges));
    }
  }
  getTwowayChanges(value, ngChanges, prop) {
    let valFn = ej.getObject(prop, this.widget["model"]);
    valFn(value, true);
    ej.createObject(prop, valFn, ngChanges);
    return ngChanges;
  }
  ngAfterContentChecked() {
    let changes = Utils.IterateAndGetChanges(this);
    for (let key in changes) {
      if (changes.hasOwnProperty(key)) {
        let element = changes[key];
        this.widget["_" + key](element);
      }
    }
  }
  ngOnDestroy() {
    this.widget["destroy"]();
  }
};
BoldReportComponents.ɵfac = function BoldReportComponents_Factory(t) {
  ɵɵinvalidFactory();
};
BoldReportComponents.ɵprov = ɵɵdefineInjectable({
  token: BoldReportComponents,
  factory: BoldReportComponents.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportComponents, [{
    type: Injectable
  }], function() {
    return [{
      type: void 0
    }, {
      type: ElementRef
    }, {
      type: ChangeDetectorRef
    }, {
      type: Array
    }, {
      type: IterableDiffers
    }, {
      type: KeyValueDiffers
    }];
  }, null);
})();
var ComplexTagElement = class {
  // tags: Array<string>;
  constructor(tags) {
    this.tags = tags;
    this.valueChange = new EventEmitter();
    this.complexes = [];
    this.hasChanges = false;
    this.firstChange = true;
    Object.defineProperty(this, "parent", {
      enumerable: false,
      writable: true,
      value: null
    });
    currentTemplateElement = this;
  }
  ngOnInit() {
    this.firstChange = false;
    for (let key in this) {
      if (key.indexOf("_") != -1 && key.indexOf("tag_") == -1) this.complexes.push(key);
    }
    for (let i = 0; i < this.complexes.length; i++) {
      let property = this.complexes[i];
      if (property.indexOf("_") != -1) {
        let tempObj = {};
        let key = property.replace(/\_/g, ".");
        ej.createObject(key, this[property], tempObj);
        let rootProp = key.split(".")[0];
        if (this[rootProp] == void 0) this[rootProp] = {};
        $.extend(true, this[rootProp], tempObj[rootProp]);
      }
    }
  }
  ensureCleanObject() {
    let tags = this.tags;
    for (let i = 0; i < tags.length; i++) {
      let element = tags[i], tagElement = this["tag_" + element.replace(/\./g, "_")];
      if (i === 0 && this[element]) {
        return;
      }
      if (ej.isNullOrUndefined(tagElement)) {
        continue;
      }
      ej.createObject(element, tagElement.getList(), this);
    }
  }
  ngOnChanges(changes) {
    if (this.firstChange) {
      return;
    }
    this.recentChanges = changes;
    this.hasChanges = true;
  }
  getChangesAndReset() {
    if (this.hasChanges === false) {
      return;
    }
    let changes = this.recentChanges || {};
    let contentChanges = Utils.IterateAndGetChanges(this);
    if (!$.isEmptyObject(contentChanges)) {
      for (let key in contentChanges) {
        if (contentChanges.hasOwnProperty(key)) {
          let element = contentChanges[key];
          if (!ej.isNullOrUndefined(this.parent.widget["_" + this.property.replace(/\./g, "_") + "_" + key])) this.parent.widget["_" + this.property.replace(/\./g, "_") + "_" + key](element);
        }
      }
    }
    this.hasChanges = false;
    return changes;
  }
  ngAfterContentChecked() {
    let tags = this.tags;
    for (let i = 0, len = tags.length; i < len; i++) {
      let element = tags[i], tagElement = this["tag_" + element.replace(/\./g, "_")];
      if (tagElement && tagElement.hasChanges) {
        this.hasChanges = true;
      }
    }
  }
};
ComplexTagElement.ɵfac = function ComplexTagElement_Factory(t) {
  return new (t || ComplexTagElement)(ɵɵinject(Array));
};
ComplexTagElement.ɵprov = ɵɵdefineInjectable({
  token: ComplexTagElement,
  factory: ComplexTagElement.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ComplexTagElement, [{
    type: Injectable
  }], function() {
    return [{
      type: Array
    }];
  }, null);
})();
var ArrayTagElement = class {
  constructor(propertyName) {
    this.propertyName = propertyName;
    this.hasChanges = false;
  }
  // TODO: Need to consider dynamic child change
  ngAfterContentInit() {
    let index = 0;
    this.list = this.children.map((child) => {
      child.index = index++;
      child.property = this.propertyName;
      return child;
    });
  }
  getList() {
    let list = this.list;
    for (let i = 0; i < list.length; i++) {
      list[i].ensureCleanObject();
    }
    return list;
  }
  getChangesAndReset() {
    this.hasChanges = false;
    return this.recentChanges;
  }
  ngAfterContentChecked() {
    let changes = {}, res = changes[this.propertyName] = [], childChange;
    for (let i = 0, list = this.list; i < list.length; i++) {
      let child = list[i];
      if (child.hasChanges) {
        childChange = child.getChangesAndReset();
        if (!ej.isNullOrUndefined(childChange)) {
          res.push({
            index: child.index,
            change: childChange
          });
        }
      }
    }
    if (res.length > 0) {
      this.recentChanges = res;
      this.hasChanges = true;
    }
  }
};
ArrayTagElement.ɵfac = function ArrayTagElement_Factory(t) {
  ɵɵinvalidFactory();
};
ArrayTagElement.ɵprov = ɵɵdefineInjectable({
  token: ArrayTagElement,
  factory: ArrayTagElement.ɵfac
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ArrayTagElement, [{
    type: Injectable
  }], function() {
    return [{
      type: void 0
    }];
  }, null);
})();
var BoldReportViewerComponent = class extends BoldReportComponents {
  // tslint:disable-next-line:max-line-length
  constructor(el, cdRef, _ejIterableDiffers, _ejkeyvaluediffers) {
    super("BoldReportViewer", el, cdRef, [], _ejIterableDiffers, _ejkeyvaluediffers);
    this.el = el;
    this.cdRef = cdRef;
    this._ejIterableDiffers = _ejIterableDiffers;
    this._ejkeyvaluediffers = _ejkeyvaluediffers;
    this.drillThrough_output = new EventEmitter();
    this.renderingBegin_output = new EventEmitter();
    this.renderingComplete_output = new EventEmitter();
    this.reportError_output = new EventEmitter();
    this.reportExport_output = new EventEmitter();
    this.reportLoaded_output = new EventEmitter();
    this.reportCanceled_output = new EventEmitter();
    this.showError_output = new EventEmitter();
    this.viewReportClick_output = new EventEmitter();
    this.ajaxBeforeLoad_output = new EventEmitter();
    this.ajaxSuccess_output = new EventEmitter();
    this.ajaxError_output = new EventEmitter();
    this.toolbarRendering_output = new EventEmitter();
    this.exportProgressChanged_output = new EventEmitter();
    this.printProgressChanged_output = new EventEmitter();
    this.exportItemClick_output = new EventEmitter();
    this.toolBarItemClick_output = new EventEmitter();
    this.hyperlink_output = new EventEmitter();
    this.reportPrint_output = new EventEmitter();
    this.beforeParameterAdd_output = new EventEmitter();
  }
};
BoldReportViewerComponent.ɵfac = function BoldReportViewerComponent_Factory(t) {
  return new (t || BoldReportViewerComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(IterableDiffers), ɵɵdirectiveInject(KeyValueDiffers));
};
BoldReportViewerComponent.ɵcmp = ɵɵdefineComponent({
  type: BoldReportViewerComponent,
  selectors: [["bold-reportviewer"]],
  inputs: {
    dataSources_input: [InputFlags.None, "dataSources", "dataSources_input"],
    exportSettings_input: [InputFlags.None, "exportSettings", "exportSettings_input"],
    isResponsive_input: [InputFlags.None, "isResponsive", "isResponsive_input"],
    locale_input: [InputFlags.None, "locale", "locale_input"],
    pageSettings_input: [InputFlags.None, "pageSettings", "pageSettings_input"],
    parameters_input: [InputFlags.None, "parameters", "parameters_input"],
    extendedAttributes_input: [InputFlags.None, "extendedAttributes", "extendedAttributes_input"],
    toolbarSettings_input: [InputFlags.None, "toolbarSettings", "toolbarSettings_input"],
    parameterSettings_input: [InputFlags.None, "parameterSettings", "parameterSettings_input"],
    printMode_input: [InputFlags.None, "printMode", "printMode_input"],
    printOption_input: [InputFlags.None, "printOption", "printOption_input"],
    processingMode_input: [InputFlags.None, "processingMode", "processingMode_input"],
    renderMode_input: [InputFlags.None, "renderMode", "renderMode_input"],
    reportPath_input: [InputFlags.None, "reportPath", "reportPath_input"],
    reportServerUrl_input: [InputFlags.None, "reportServerUrl", "reportServerUrl_input"],
    reportServiceUrl_input: [InputFlags.None, "reportServiceUrl", "reportServiceUrl_input"],
    zoomFactor_input: [InputFlags.None, "zoomFactor", "zoomFactor_input"],
    serviceAuthorizationToken_input: [InputFlags.None, "serviceAuthorizationToken", "serviceAuthorizationToken_input"],
    embedToken_input: [InputFlags.None, "embedToken", "embedToken_input"],
    toolbarRenderMode_input: [InputFlags.None, "toolbarRenderMode", "toolbarRenderMode_input"],
    enableParameterBlockScroller_input: [InputFlags.None, "enableParameterBlockScroller", "enableParameterBlockScroller_input"],
    enableDatasourceBlockScroller_input: [InputFlags.None, "enableDatasourceBlockScroller", "enableDatasourceBlockScroller_input"],
    sizeToReportContent_input: [InputFlags.None, "sizeToReportContent", "sizeToReportContent_input"],
    autoRender_input: [InputFlags.None, "autoRender", "autoRender_input"],
    enableNotificationBar_input: [InputFlags.None, "enableNotificationBar", "enableNotificationBar_input"],
    enableDropDownSearch_input: [InputFlags.None, "enableDropDownSearch", "enableDropDownSearch_input"],
    enableVirtualEvaluation_input: [InputFlags.None, "enableVirtualEvaluation", "enableVirtualEvaluation_input"],
    smartRendering_input: [InputFlags.None, "smartRendering", "smartRendering_input"],
    waitingPopupTemplate_input: [InputFlags.None, "waitingPopupTemplate", "waitingPopupTemplate_input"],
    enableOnScrollNavigation_input: [InputFlags.None, "enableOnScrollNavigation", "enableOnScrollNavigation_input"],
    customBrandSettings_input: [InputFlags.None, "customBrandSettings", "customBrandSettings_input"],
    customBrandSettings_link_input: [InputFlags.None, "customBrandSettings.link", "customBrandSettings_link_input"],
    customBrandSettings_domain_input: [InputFlags.None, "customBrandSettings.domain", "customBrandSettings_domain_input"],
    customBrandSettings_name_input: [InputFlags.None, "customBrandSettings.name", "customBrandSettings_name_input"],
    customBrandSettings_links_input: [InputFlags.None, "customBrandSettings.links", "customBrandSettings_links_input"],
    dataSources_name_input: [InputFlags.None, "dataSources.name", "dataSources_name_input"],
    dataSources_value_input: [InputFlags.None, "dataSources.value", "dataSources_value_input"],
    exportSettings_exportOptions_input: [InputFlags.None, "exportSettings.exportOptions", "exportSettings_exportOptions_input"],
    exportSettings_excelFormat_input: [InputFlags.None, "exportSettings.excelFormat", "exportSettings_excelFormat_input"],
    exportSettings_wordFormat_input: [InputFlags.None, "exportSettings.wordFormat", "exportSettings_wordFormat_input"],
    exportSettings_customItems_input: [InputFlags.None, "exportSettings.customItems", "exportSettings_customItems_input"],
    exportSettings_ImageQuality_input: [InputFlags.None, "exportSettings.ImageQuality", "exportSettings_ImageQuality_input"],
    exportSettings_UsePrintSizes_input: [InputFlags.None, "exportSettings.UsePrintSizes", "exportSettings_UsePrintSizes_input"],
    pageSettings_orientation_input: [InputFlags.None, "pageSettings.orientation", "pageSettings_orientation_input"],
    pageSettings_paperSize_input: [InputFlags.None, "pageSettings.paperSize", "pageSettings_paperSize_input"],
    pageSettings_height_input: [InputFlags.None, "pageSettings.height", "pageSettings_height_input"],
    pageSettings_width_input: [InputFlags.None, "pageSettings.width", "pageSettings_width_input"],
    pageSettings_margins_input: [InputFlags.None, "pageSettings.margins", "pageSettings_margins_input"],
    pageSettings_hidePageOrientation_input: [InputFlags.None, "pageSettings.hidePageOrientation", "pageSettings_hidePageOrientation_input"],
    pageSettings_hidePagePaperSizes_input: [InputFlags.None, "pageSettings.hidePagePaperSizes", "pageSettings_hidePagePaperSizes_input"],
    parameters_labels_input: [InputFlags.None, "parameters.labels", "parameters_labels_input"],
    parameters_name_input: [InputFlags.None, "parameters.name", "parameters_name_input"],
    parameters_nullable_input: [InputFlags.None, "parameters.nullable", "parameters_nullable_input"],
    parameters_prompt_input: [InputFlags.None, "parameters.prompt", "parameters_prompt_input"],
    parameters_values_input: [InputFlags.None, "parameters.values", "parameters_values_input"],
    parameterSettings_delimiterChar_input: [InputFlags.None, "parameterSettings.delimiterChar", "parameterSettings_delimiterChar_input"],
    parameterSettings_position_input: [InputFlags.None, "parameterSettings.position", "parameterSettings_position_input"],
    parameterSettings_popupHeight_input: [InputFlags.None, "parameterSettings.popupHeight", "parameterSettings_popupHeight_input"],
    parameterSettings_popupWidth_input: [InputFlags.None, "parameterSettings.popupWidth", "parameterSettings_popupWidth_input"],
    parameterSettings_itemWidth_input: [InputFlags.None, "parameterSettings.itemWidth", "parameterSettings_itemWidth_input"],
    parameterSettings_labelWidth_input: [InputFlags.None, "parameterSettings.labelWidth", "parameterSettings_labelWidth_input"],
    parameterSettings_minDateTime_input: [InputFlags.None, "parameterSettings.minDateTime", "parameterSettings_minDateTime_input"],
    parameterSettings_maxDateTime_input: [InputFlags.None, "parameterSettings.maxDateTime", "parameterSettings_maxDateTime_input"],
    parameterSettings_hideTooltip_input: [InputFlags.None, "parameterSettings.hideTooltip", "parameterSettings_hideTooltip_input"],
    parameterSettings_enablePopupResize_input: [InputFlags.None, "parameterSettings.enablePopupResize", "parameterSettings_enablePopupResize_input"],
    parameterSettings_hideParameterBlock_input: [InputFlags.None, "parameterSettings.hideParameterBlock", "parameterSettings_hideParameterBlock_input"],
    parameterSettings_dateTimeFormat_input: [InputFlags.None, "parameterSettings.dateTimeFormat", "parameterSettings_dateTimeFormat_input"],
    parameterSettings_timeDisplayFormat_input: [InputFlags.None, "parameterSettings.timeDisplayFormat", "parameterSettings_timeDisplayFormat_input"],
    parameterSettings_timeInterval_input: [InputFlags.None, "parameterSettings.timeInterval", "parameterSettings_timeInterval_input"],
    parameterSettings_accessInternalValue_input: [InputFlags.None, "parameterSettings.accessInternalValue", "parameterSettings_accessInternalValue_input"],
    toolbarSettings_click_input: [InputFlags.None, "toolbarSettings.click", "toolbarSettings_click_input"],
    toolbarSettings_items_input: [InputFlags.None, "toolbarSettings.items", "toolbarSettings_items_input"],
    toolbarSettings_toolbars_input: [InputFlags.None, "toolbarSettings.toolbars", "toolbarSettings_toolbars_input"],
    toolbarSettings_showToolbar_input: [InputFlags.None, "toolbarSettings.showToolbar", "toolbarSettings_showToolbar_input"],
    toolbarSettings_showTooltip_input: [InputFlags.None, "toolbarSettings.showTooltip", "toolbarSettings_showTooltip_input"],
    toolbarSettings_autoHide_input: [InputFlags.None, "toolbarSettings.autoHide", "toolbarSettings_autoHide_input"],
    toolbarSettings_autoHideDelay_input: [InputFlags.None, "toolbarSettings.autoHideDelay", "toolbarSettings_autoHideDelay_input"],
    toolbarSettings_templateId_input: [InputFlags.None, "toolbarSettings.templateId", "toolbarSettings_templateId_input"],
    toolbarSettings_customItems_input: [InputFlags.None, "toolbarSettings.customItems", "toolbarSettings_customItems_input"],
    toolbarSettings_customGroups_input: [InputFlags.None, "toolbarSettings.customGroups", "toolbarSettings_customGroups_input"]
  },
  outputs: {
    drillThrough_output: "drillThrough",
    renderingBegin_output: "renderingBegin",
    renderingComplete_output: "renderingComplete",
    reportError_output: "reportError",
    reportExport_output: "reportExport",
    reportLoaded_output: "reportLoaded",
    reportCanceled_output: "reportCanceled",
    showError_output: "showError",
    viewReportClick_output: "viewReportClick",
    ajaxBeforeLoad_output: "ajaxBeforeLoad",
    ajaxSuccess_output: "ajaxSuccess",
    ajaxError_output: "ajaxError",
    toolbarRendering_output: "toolbarRendering",
    exportProgressChanged_output: "exportProgressChanged",
    printProgressChanged_output: "printProgressChanged",
    exportItemClick_output: "exportItemClick",
    toolBarItemClick_output: "toolBarItemClick",
    hyperlink_output: "hyperlink",
    reportPrint_output: "reportPrint",
    beforeParameterAdd_output: "beforeParameterAdd"
  },
  features: [ɵɵInheritDefinitionFeature],
  decls: 0,
  vars: 0,
  template: function BoldReportViewerComponent_Template(rf, ctx) {
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportViewerComponent, [{
    type: Component,
    args: [{
      selector: "bold-reportviewer",
      template: ""
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: ChangeDetectorRef
    }, {
      type: IterableDiffers
    }, {
      type: KeyValueDiffers
    }];
  }, {
    dataSources_input: [{
      type: Input,
      args: ["dataSources"]
    }],
    exportSettings_input: [{
      type: Input,
      args: ["exportSettings"]
    }],
    isResponsive_input: [{
      type: Input,
      args: ["isResponsive"]
    }],
    locale_input: [{
      type: Input,
      args: ["locale"]
    }],
    pageSettings_input: [{
      type: Input,
      args: ["pageSettings"]
    }],
    parameters_input: [{
      type: Input,
      args: ["parameters"]
    }],
    extendedAttributes_input: [{
      type: Input,
      args: ["extendedAttributes"]
    }],
    toolbarSettings_input: [{
      type: Input,
      args: ["toolbarSettings"]
    }],
    parameterSettings_input: [{
      type: Input,
      args: ["parameterSettings"]
    }],
    printMode_input: [{
      type: Input,
      args: ["printMode"]
    }],
    printOption_input: [{
      type: Input,
      args: ["printOption"]
    }],
    processingMode_input: [{
      type: Input,
      args: ["processingMode"]
    }],
    renderMode_input: [{
      type: Input,
      args: ["renderMode"]
    }],
    reportPath_input: [{
      type: Input,
      args: ["reportPath"]
    }],
    reportServerUrl_input: [{
      type: Input,
      args: ["reportServerUrl"]
    }],
    reportServiceUrl_input: [{
      type: Input,
      args: ["reportServiceUrl"]
    }],
    zoomFactor_input: [{
      type: Input,
      args: ["zoomFactor"]
    }],
    serviceAuthorizationToken_input: [{
      type: Input,
      args: ["serviceAuthorizationToken"]
    }],
    embedToken_input: [{
      type: Input,
      args: ["embedToken"]
    }],
    toolbarRenderMode_input: [{
      type: Input,
      args: ["toolbarRenderMode"]
    }],
    enableParameterBlockScroller_input: [{
      type: Input,
      args: ["enableParameterBlockScroller"]
    }],
    enableDatasourceBlockScroller_input: [{
      type: Input,
      args: ["enableDatasourceBlockScroller"]
    }],
    sizeToReportContent_input: [{
      type: Input,
      args: ["sizeToReportContent"]
    }],
    autoRender_input: [{
      type: Input,
      args: ["autoRender"]
    }],
    enableNotificationBar_input: [{
      type: Input,
      args: ["enableNotificationBar"]
    }],
    enableDropDownSearch_input: [{
      type: Input,
      args: ["enableDropDownSearch"]
    }],
    enableVirtualEvaluation_input: [{
      type: Input,
      args: ["enableVirtualEvaluation"]
    }],
    smartRendering_input: [{
      type: Input,
      args: ["smartRendering"]
    }],
    waitingPopupTemplate_input: [{
      type: Input,
      args: ["waitingPopupTemplate"]
    }],
    enableOnScrollNavigation_input: [{
      type: Input,
      args: ["enableOnScrollNavigation"]
    }],
    customBrandSettings_input: [{
      type: Input,
      args: ["customBrandSettings"]
    }],
    customBrandSettings_link_input: [{
      type: Input,
      args: ["customBrandSettings.link"]
    }],
    customBrandSettings_domain_input: [{
      type: Input,
      args: ["customBrandSettings.domain"]
    }],
    customBrandSettings_name_input: [{
      type: Input,
      args: ["customBrandSettings.name"]
    }],
    customBrandSettings_links_input: [{
      type: Input,
      args: ["customBrandSettings.links"]
    }],
    dataSources_name_input: [{
      type: Input,
      args: ["dataSources.name"]
    }],
    dataSources_value_input: [{
      type: Input,
      args: ["dataSources.value"]
    }],
    exportSettings_exportOptions_input: [{
      type: Input,
      args: ["exportSettings.exportOptions"]
    }],
    exportSettings_excelFormat_input: [{
      type: Input,
      args: ["exportSettings.excelFormat"]
    }],
    exportSettings_wordFormat_input: [{
      type: Input,
      args: ["exportSettings.wordFormat"]
    }],
    exportSettings_customItems_input: [{
      type: Input,
      args: ["exportSettings.customItems"]
    }],
    exportSettings_ImageQuality_input: [{
      type: Input,
      args: ["exportSettings.ImageQuality"]
    }],
    exportSettings_UsePrintSizes_input: [{
      type: Input,
      args: ["exportSettings.UsePrintSizes"]
    }],
    pageSettings_orientation_input: [{
      type: Input,
      args: ["pageSettings.orientation"]
    }],
    pageSettings_paperSize_input: [{
      type: Input,
      args: ["pageSettings.paperSize"]
    }],
    pageSettings_height_input: [{
      type: Input,
      args: ["pageSettings.height"]
    }],
    pageSettings_width_input: [{
      type: Input,
      args: ["pageSettings.width"]
    }],
    pageSettings_margins_input: [{
      type: Input,
      args: ["pageSettings.margins"]
    }],
    pageSettings_hidePageOrientation_input: [{
      type: Input,
      args: ["pageSettings.hidePageOrientation"]
    }],
    pageSettings_hidePagePaperSizes_input: [{
      type: Input,
      args: ["pageSettings.hidePagePaperSizes"]
    }],
    parameters_labels_input: [{
      type: Input,
      args: ["parameters.labels"]
    }],
    parameters_name_input: [{
      type: Input,
      args: ["parameters.name"]
    }],
    parameters_nullable_input: [{
      type: Input,
      args: ["parameters.nullable"]
    }],
    parameters_prompt_input: [{
      type: Input,
      args: ["parameters.prompt"]
    }],
    parameters_values_input: [{
      type: Input,
      args: ["parameters.values"]
    }],
    parameterSettings_delimiterChar_input: [{
      type: Input,
      args: ["parameterSettings.delimiterChar"]
    }],
    parameterSettings_position_input: [{
      type: Input,
      args: ["parameterSettings.position"]
    }],
    parameterSettings_popupHeight_input: [{
      type: Input,
      args: ["parameterSettings.popupHeight"]
    }],
    parameterSettings_popupWidth_input: [{
      type: Input,
      args: ["parameterSettings.popupWidth"]
    }],
    parameterSettings_itemWidth_input: [{
      type: Input,
      args: ["parameterSettings.itemWidth"]
    }],
    parameterSettings_labelWidth_input: [{
      type: Input,
      args: ["parameterSettings.labelWidth"]
    }],
    parameterSettings_minDateTime_input: [{
      type: Input,
      args: ["parameterSettings.minDateTime"]
    }],
    parameterSettings_maxDateTime_input: [{
      type: Input,
      args: ["parameterSettings.maxDateTime"]
    }],
    parameterSettings_hideTooltip_input: [{
      type: Input,
      args: ["parameterSettings.hideTooltip"]
    }],
    parameterSettings_enablePopupResize_input: [{
      type: Input,
      args: ["parameterSettings.enablePopupResize"]
    }],
    parameterSettings_hideParameterBlock_input: [{
      type: Input,
      args: ["parameterSettings.hideParameterBlock"]
    }],
    parameterSettings_dateTimeFormat_input: [{
      type: Input,
      args: ["parameterSettings.dateTimeFormat"]
    }],
    parameterSettings_timeDisplayFormat_input: [{
      type: Input,
      args: ["parameterSettings.timeDisplayFormat"]
    }],
    parameterSettings_timeInterval_input: [{
      type: Input,
      args: ["parameterSettings.timeInterval"]
    }],
    parameterSettings_accessInternalValue_input: [{
      type: Input,
      args: ["parameterSettings.accessInternalValue"]
    }],
    toolbarSettings_click_input: [{
      type: Input,
      args: ["toolbarSettings.click"]
    }],
    toolbarSettings_items_input: [{
      type: Input,
      args: ["toolbarSettings.items"]
    }],
    toolbarSettings_toolbars_input: [{
      type: Input,
      args: ["toolbarSettings.toolbars"]
    }],
    toolbarSettings_showToolbar_input: [{
      type: Input,
      args: ["toolbarSettings.showToolbar"]
    }],
    toolbarSettings_showTooltip_input: [{
      type: Input,
      args: ["toolbarSettings.showTooltip"]
    }],
    toolbarSettings_autoHide_input: [{
      type: Input,
      args: ["toolbarSettings.autoHide"]
    }],
    toolbarSettings_autoHideDelay_input: [{
      type: Input,
      args: ["toolbarSettings.autoHideDelay"]
    }],
    toolbarSettings_templateId_input: [{
      type: Input,
      args: ["toolbarSettings.templateId"]
    }],
    toolbarSettings_customItems_input: [{
      type: Input,
      args: ["toolbarSettings.customItems"]
    }],
    toolbarSettings_customGroups_input: [{
      type: Input,
      args: ["toolbarSettings.customGroups"]
    }],
    drillThrough_output: [{
      type: Output,
      args: ["drillThrough"]
    }],
    renderingBegin_output: [{
      type: Output,
      args: ["renderingBegin"]
    }],
    renderingComplete_output: [{
      type: Output,
      args: ["renderingComplete"]
    }],
    reportError_output: [{
      type: Output,
      args: ["reportError"]
    }],
    reportExport_output: [{
      type: Output,
      args: ["reportExport"]
    }],
    reportLoaded_output: [{
      type: Output,
      args: ["reportLoaded"]
    }],
    reportCanceled_output: [{
      type: Output,
      args: ["reportCanceled"]
    }],
    showError_output: [{
      type: Output,
      args: ["showError"]
    }],
    viewReportClick_output: [{
      type: Output,
      args: ["viewReportClick"]
    }],
    ajaxBeforeLoad_output: [{
      type: Output,
      args: ["ajaxBeforeLoad"]
    }],
    ajaxSuccess_output: [{
      type: Output,
      args: ["ajaxSuccess"]
    }],
    ajaxError_output: [{
      type: Output,
      args: ["ajaxError"]
    }],
    toolbarRendering_output: [{
      type: Output,
      args: ["toolbarRendering"]
    }],
    exportProgressChanged_output: [{
      type: Output,
      args: ["exportProgressChanged"]
    }],
    printProgressChanged_output: [{
      type: Output,
      args: ["printProgressChanged"]
    }],
    exportItemClick_output: [{
      type: Output,
      args: ["exportItemClick"]
    }],
    toolBarItemClick_output: [{
      type: Output,
      args: ["toolBarItemClick"]
    }],
    hyperlink_output: [{
      type: Output,
      args: ["hyperlink"]
    }],
    reportPrint_output: [{
      type: Output,
      args: ["reportPrint"]
    }],
    beforeParameterAdd_output: [{
      type: Output,
      args: ["beforeParameterAdd"]
    }]
  });
})();
var BoldReportViewerModule = class {
};
BoldReportViewerModule.ɵfac = function BoldReportViewerModule_Factory(t) {
  return new (t || BoldReportViewerModule)();
};
BoldReportViewerModule.ɵmod = ɵɵdefineNgModule({
  type: BoldReportViewerModule,
  declarations: [BoldReportViewerComponent],
  imports: [CommonModule],
  exports: [BoldReportViewerComponent]
});
BoldReportViewerModule.ɵinj = ɵɵdefineInjector({
  imports: [[CommonModule]]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportViewerModule, [{
    type: NgModule,
    args: [{
      imports: [CommonModule],
      declarations: [BoldReportViewerComponent],
      exports: [BoldReportViewerComponent]
    }]
  }], null, null);
})();
var BoldReportDesignerComponent = class extends BoldReportComponents {
  // tslint:disable-next-line:max-line-length
  constructor(el, cdRef, _ejIterableDiffers, _ejkeyvaluediffers) {
    super("BoldReportDesigner", el, cdRef, [], _ejIterableDiffers, _ejkeyvaluediffers);
    this.el = el;
    this.cdRef = cdRef;
    this._ejIterableDiffers = _ejIterableDiffers;
    this._ejkeyvaluediffers = _ejkeyvaluediffers;
    this.ajaxBeforeLoad_output = new EventEmitter();
    this.ajaxError_output = new EventEmitter();
    this.ajaxSuccess_output = new EventEmitter();
    this.create_output = new EventEmitter();
    this.destroy_output = new EventEmitter();
    this.newDataClick_output = new EventEmitter();
    this.openReportClick_output = new EventEmitter();
    this.previewReport_output = new EventEmitter();
    this.reportModified_output = new EventEmitter();
    this.reportOpened_output = new EventEmitter();
    this.reportSaved_output = new EventEmitter();
    this.saveReportClick_output = new EventEmitter();
    this.toolbarClick_output = new EventEmitter();
    this.toolbarRendering_output = new EventEmitter();
    this.encryptData_output = new EventEmitter();
    this.decryptData_output = new EventEmitter();
  }
};
BoldReportDesignerComponent.ɵfac = function BoldReportDesignerComponent_Factory(t) {
  return new (t || BoldReportDesignerComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(IterableDiffers), ɵɵdirectiveInject(KeyValueDiffers));
};
BoldReportDesignerComponent.ɵcmp = ɵɵdefineComponent({
  type: BoldReportDesignerComponent,
  selectors: [["bold-reportdesigner"]],
  inputs: {
    configurePaneSettings_input: [InputFlags.None, "configurePaneSettings", "configurePaneSettings_input"],
    dataTabIndex_input: [InputFlags.None, "dataTabIndex", "dataTabIndex_input"],
    disableCodeModule_input: [InputFlags.None, "disableCodeModule", "disableCodeModule_input"],
    enableRuler_input: [InputFlags.None, "enableRuler", "enableRuler_input"],
    enablePageMargin_input: [InputFlags.None, "enablePageMargin", "enablePageMargin_input"],
    embedToken_input: [InputFlags.None, "embedToken", "embedToken_input"],
    enableImpersonate_input: [InputFlags.None, "enableImpersonate", "enableImpersonate_input"],
    enableImageBlobing_input: [InputFlags.None, "enableImageBlobing", "enableImageBlobing_input"],
    enableTableMultipleDataAssign_input: [InputFlags.None, "enableTableMultipleDataAssign", "enableTableMultipleDataAssign_input"],
    filterDataConnectors_input: [InputFlags.None, "filterDataConnectors", "filterDataConnectors_input"],
    filterReportItems_input: [InputFlags.None, "filterReportItems", "filterReportItems_input"],
    fontNames_input: [InputFlags.None, "fontNames", "fontNames_input"],
    locale_input: [InputFlags.None, "locale", "locale_input"],
    permissionSettings_input: [InputFlags.None, "permissionSettings", "permissionSettings_input"],
    previewOptions_input: [InputFlags.None, "previewOptions", "previewOptions_input"],
    reportDataExtensions_input: [InputFlags.None, "reportDataExtensions", "reportDataExtensions_input"],
    reportItemExtensions_input: [InputFlags.None, "reportItemExtensions", "reportItemExtensions_input"],
    reportPath_input: [InputFlags.None, "reportPath", "reportPath_input"],
    reportVersion_input: [InputFlags.None, "reportVersion", "reportVersion_input"],
    reportType_input: [InputFlags.None, "reportType", "reportType_input"],
    reportServerUrl_input: [InputFlags.None, "reportServerUrl", "reportServerUrl_input"],
    serviceAuthorizationToken_input: [InputFlags.None, "serviceAuthorizationToken", "serviceAuthorizationToken_input"],
    serviceUrl_input: [InputFlags.None, "serviceUrl", "serviceUrl_input"],
    toolbarSettings_input: [InputFlags.None, "toolbarSettings", "toolbarSettings_input"],
    waitingPopupTemplate_input: [InputFlags.None, "waitingPopupTemplate", "waitingPopupTemplate_input"],
    zoomFactor_input: [InputFlags.None, "zoomFactor", "zoomFactor_input"],
    configurePaneSettings_items_input: [InputFlags.None, "configurePaneSettings.items", "configurePaneSettings_items_input"],
    configurePaneSettings_showPane_input: [InputFlags.None, "configurePaneSettings.showPane", "configurePaneSettings_showPane_input"],
    dataSources_name_input: [InputFlags.None, "dataSources.name", "dataSources_name_input"],
    dataSources_value_input: [InputFlags.None, "dataSources.value", "dataSources_value_input"],
    exportSettings_exportOptions_input: [InputFlags.None, "exportSettings.exportOptions", "exportSettings_exportOptions_input"],
    exportSettings_excelFormat_input: [InputFlags.None, "exportSettings.excelFormat", "exportSettings_excelFormat_input"],
    exportSettings_wordFormat_input: [InputFlags.None, "exportSettings.wordFormat", "exportSettings_wordFormat_input"],
    exportSettings_customItems_input: [InputFlags.None, "exportSettings.customItems", "exportSettings_customItems_input"],
    pageSettings_orientation_input: [InputFlags.None, "pageSettings.orientation", "pageSettings_orientation_input"],
    pageSettings_paperSize_input: [InputFlags.None, "pageSettings.paperSize", "pageSettings_paperSize_input"],
    pageSettings_height_input: [InputFlags.None, "pageSettings.height", "pageSettings_height_input"],
    pageSettings_width_input: [InputFlags.None, "pageSettings.width", "pageSettings_width_input"],
    pageSettings_margins_input: [InputFlags.None, "pageSettings.margins", "pageSettings_margins_input"],
    pageSettings_hidePageOrientation_input: [InputFlags.None, "pageSettings.hidePageOrientation", "pageSettings_hidePageOrientation_input"],
    parameters_labels_input: [InputFlags.None, "parameters.labels", "parameters_labels_input"],
    parameters_name_input: [InputFlags.None, "parameters.name", "parameters_name_input"],
    parameters_nullable_input: [InputFlags.None, "parameters.nullable", "parameters_nullable_input"],
    parameters_prompt_input: [InputFlags.None, "parameters.prompt", "parameters_prompt_input"],
    parameters_values_input: [InputFlags.None, "parameters.values", "parameters_values_input"],
    parameterSettings_delimiterChar_input: [InputFlags.None, "parameterSettings.delimiterChar", "parameterSettings_delimiterChar_input"],
    parameterSettings_popupHeight_input: [InputFlags.None, "parameterSettings.popupHeight", "parameterSettings_popupHeight_input"],
    parameterSettings_popupWidth_input: [InputFlags.None, "parameterSettings.popupWidth", "parameterSettings_popupWidth_input"],
    parameterSettings_itemWidth_input: [InputFlags.None, "parameterSettings.itemWidth", "parameterSettings_itemWidth_input"],
    parameterSettings_labelWidth_input: [InputFlags.None, "parameterSettings.labelWidth", "parameterSettings_labelWidth_input"],
    permissionSettings_dataSet_input: [InputFlags.None, "permissionSettings.dataSet", "permissionSettings_dataSet_input"],
    permissionSettings_dataSource_input: [InputFlags.None, "permissionSettings.dataSource", "permissionSettings_dataSource_input"],
    previewOptions_autoRender_input: [InputFlags.None, "previewOptions.autoRender", "previewOptions_autoRender_input"],
    previewOptions_dataSources_input: [InputFlags.None, "previewOptions.dataSources", "previewOptions_dataSources_input"],
    previewOptions_enableNotificationBar_input: [InputFlags.None, "previewOptions.enableNotificationBar", "previewOptions_enableNotificationBar_input"],
    previewOptions_enableVirtualEvaluation_input: [InputFlags.None, "previewOptions.enableVirtualEvaluation", "previewOptions_enableVirtualEvaluation_input"],
    previewOptions_enableParameterBlockScroller_input: [InputFlags.None, "previewOptions.enableParameterBlockScroller", "previewOptions_enableParameterBlockScroller_input"],
    previewOptions_enableDatasourceBlockScroller_input: [InputFlags.None, "previewOptions.enableDatasourceBlockScroller", "previewOptions_enableDatasourceBlockScroller_input"],
    previewOptions_enableDropDownSearch_input: [InputFlags.None, "previewOptions.enableDropDownSearch", "previewOptions_enableDropDownSearch_input"],
    previewOptions_exportSettings_input: [InputFlags.None, "previewOptions.exportSettings", "previewOptions_exportSettings_input"],
    previewOptions_pageSettings_input: [InputFlags.None, "previewOptions.pageSettings", "previewOptions_pageSettings_input"],
    previewOptions_parameters_input: [InputFlags.None, "previewOptions.parameters", "previewOptions_parameters_input"],
    previewOptions_parameterSettings_input: [InputFlags.None, "previewOptions.parameterSettings", "previewOptions_parameterSettings_input"],
    previewOptions_printMode_input: [InputFlags.None, "previewOptions.printMode", "previewOptions_printMode_input"],
    previewOptions_printOption_input: [InputFlags.None, "previewOptions.printOption", "previewOptions_printOption_input"],
    previewOptions_sizeToReportContent_input: [InputFlags.None, "previewOptions.sizeToReportContent", "previewOptions_sizeToReportContent_input"],
    previewOptions_toolbarSettings_input: [InputFlags.None, "previewOptions.toolbarSettings", "previewOptions_toolbarSettings_input"],
    previewOptions_zoomFactor_input: [InputFlags.None, "previewOptions.zoomFactor", "previewOptions_zoomFactor_input"],
    reportDataExtensions_name_input: [InputFlags.None, "reportDataExtensions.name", "reportDataExtensions_name_input"],
    reportDataExtensions_className_input: [InputFlags.None, "reportDataExtensions.className", "reportDataExtensions_className_input"],
    reportDataExtensions_imageClass_input: [InputFlags.None, "reportDataExtensions.imageClass", "reportDataExtensions_imageClass_input"],
    reportDataExtensions_displayName_input: [InputFlags.None, "reportDataExtensions.displayName", "reportDataExtensions_displayName_input"],
    reportItemExtensions_name_input: [InputFlags.None, "reportItemExtensions.name", "reportItemExtensions_name_input"],
    reportItemExtensions_className_input: [InputFlags.None, "reportItemExtensions.className", "reportItemExtensions_className_input"],
    reportItemExtensions_imageClass_input: [InputFlags.None, "reportItemExtensions.imageClass", "reportItemExtensions_imageClass_input"],
    reportItemExtensions_displayName_input: [InputFlags.None, "reportItemExtensions.displayName", "reportItemExtensions_displayName_input"],
    reportItemExtensions_category_input: [InputFlags.None, "reportItemExtensions.category", "reportItemExtensions_category_input"],
    reportItemExtensions_allowHeaderFooter_input: [InputFlags.None, "reportItemExtensions.allowHeaderFooter", "reportItemExtensions_allowHeaderFooter_input"],
    toolbarSettings_items_input: [InputFlags.None, "toolbarSettings.items", "toolbarSettings_items_input"],
    toolbarSettings_showToolbar_input: [InputFlags.None, "toolbarSettings.showToolbar", "toolbarSettings_showToolbar_input"],
    toolbarSettings_templateId_input: [InputFlags.None, "toolbarSettings.templateId", "toolbarSettings_templateId_input"]
  },
  outputs: {
    ajaxBeforeLoad_output: "ajaxBeforeLoad",
    ajaxError_output: "ajaxError",
    ajaxSuccess_output: "ajaxSuccess",
    create_output: "create",
    destroy_output: "destroy",
    newDataClick_output: "newDataClick",
    openReportClick_output: "openReportClick",
    previewReport_output: "previewReport",
    reportModified_output: "reportModified",
    reportOpened_output: "reportOpened",
    reportSaved_output: "reportSaved",
    saveReportClick_output: "saveReportClick",
    toolbarClick_output: "toolbarClick",
    toolbarRendering_output: "toolbarRendering",
    encryptData_output: "encryptData",
    decryptData_output: "decryptData"
  },
  features: [ɵɵInheritDefinitionFeature],
  decls: 0,
  vars: 0,
  template: function BoldReportDesignerComponent_Template(rf, ctx) {
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportDesignerComponent, [{
    type: Component,
    args: [{
      selector: "bold-reportdesigner",
      template: ""
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: ChangeDetectorRef
    }, {
      type: IterableDiffers
    }, {
      type: KeyValueDiffers
    }];
  }, {
    configurePaneSettings_input: [{
      type: Input,
      args: ["configurePaneSettings"]
    }],
    dataTabIndex_input: [{
      type: Input,
      args: ["dataTabIndex"]
    }],
    disableCodeModule_input: [{
      type: Input,
      args: ["disableCodeModule"]
    }],
    enableRuler_input: [{
      type: Input,
      args: ["enableRuler"]
    }],
    enablePageMargin_input: [{
      type: Input,
      args: ["enablePageMargin"]
    }],
    embedToken_input: [{
      type: Input,
      args: ["embedToken"]
    }],
    enableImpersonate_input: [{
      type: Input,
      args: ["enableImpersonate"]
    }],
    enableImageBlobing_input: [{
      type: Input,
      args: ["enableImageBlobing"]
    }],
    enableTableMultipleDataAssign_input: [{
      type: Input,
      args: ["enableTableMultipleDataAssign"]
    }],
    filterDataConnectors_input: [{
      type: Input,
      args: ["filterDataConnectors"]
    }],
    filterReportItems_input: [{
      type: Input,
      args: ["filterReportItems"]
    }],
    fontNames_input: [{
      type: Input,
      args: ["fontNames"]
    }],
    locale_input: [{
      type: Input,
      args: ["locale"]
    }],
    permissionSettings_input: [{
      type: Input,
      args: ["permissionSettings"]
    }],
    previewOptions_input: [{
      type: Input,
      args: ["previewOptions"]
    }],
    reportDataExtensions_input: [{
      type: Input,
      args: ["reportDataExtensions"]
    }],
    reportItemExtensions_input: [{
      type: Input,
      args: ["reportItemExtensions"]
    }],
    reportPath_input: [{
      type: Input,
      args: ["reportPath"]
    }],
    reportVersion_input: [{
      type: Input,
      args: ["reportVersion"]
    }],
    reportType_input: [{
      type: Input,
      args: ["reportType"]
    }],
    reportServerUrl_input: [{
      type: Input,
      args: ["reportServerUrl"]
    }],
    serviceAuthorizationToken_input: [{
      type: Input,
      args: ["serviceAuthorizationToken"]
    }],
    serviceUrl_input: [{
      type: Input,
      args: ["serviceUrl"]
    }],
    toolbarSettings_input: [{
      type: Input,
      args: ["toolbarSettings"]
    }],
    waitingPopupTemplate_input: [{
      type: Input,
      args: ["waitingPopupTemplate"]
    }],
    zoomFactor_input: [{
      type: Input,
      args: ["zoomFactor"]
    }],
    configurePaneSettings_items_input: [{
      type: Input,
      args: ["configurePaneSettings.items"]
    }],
    configurePaneSettings_showPane_input: [{
      type: Input,
      args: ["configurePaneSettings.showPane"]
    }],
    dataSources_name_input: [{
      type: Input,
      args: ["dataSources.name"]
    }],
    dataSources_value_input: [{
      type: Input,
      args: ["dataSources.value"]
    }],
    exportSettings_exportOptions_input: [{
      type: Input,
      args: ["exportSettings.exportOptions"]
    }],
    exportSettings_excelFormat_input: [{
      type: Input,
      args: ["exportSettings.excelFormat"]
    }],
    exportSettings_wordFormat_input: [{
      type: Input,
      args: ["exportSettings.wordFormat"]
    }],
    exportSettings_customItems_input: [{
      type: Input,
      args: ["exportSettings.customItems"]
    }],
    pageSettings_orientation_input: [{
      type: Input,
      args: ["pageSettings.orientation"]
    }],
    pageSettings_paperSize_input: [{
      type: Input,
      args: ["pageSettings.paperSize"]
    }],
    pageSettings_height_input: [{
      type: Input,
      args: ["pageSettings.height"]
    }],
    pageSettings_width_input: [{
      type: Input,
      args: ["pageSettings.width"]
    }],
    pageSettings_margins_input: [{
      type: Input,
      args: ["pageSettings.margins"]
    }],
    pageSettings_hidePageOrientation_input: [{
      type: Input,
      args: ["pageSettings.hidePageOrientation"]
    }],
    parameters_labels_input: [{
      type: Input,
      args: ["parameters.labels"]
    }],
    parameters_name_input: [{
      type: Input,
      args: ["parameters.name"]
    }],
    parameters_nullable_input: [{
      type: Input,
      args: ["parameters.nullable"]
    }],
    parameters_prompt_input: [{
      type: Input,
      args: ["parameters.prompt"]
    }],
    parameters_values_input: [{
      type: Input,
      args: ["parameters.values"]
    }],
    parameterSettings_delimiterChar_input: [{
      type: Input,
      args: ["parameterSettings.delimiterChar"]
    }],
    parameterSettings_popupHeight_input: [{
      type: Input,
      args: ["parameterSettings.popupHeight"]
    }],
    parameterSettings_popupWidth_input: [{
      type: Input,
      args: ["parameterSettings.popupWidth"]
    }],
    parameterSettings_itemWidth_input: [{
      type: Input,
      args: ["parameterSettings.itemWidth"]
    }],
    parameterSettings_labelWidth_input: [{
      type: Input,
      args: ["parameterSettings.labelWidth"]
    }],
    permissionSettings_dataSet_input: [{
      type: Input,
      args: ["permissionSettings.dataSet"]
    }],
    permissionSettings_dataSource_input: [{
      type: Input,
      args: ["permissionSettings.dataSource"]
    }],
    previewOptions_autoRender_input: [{
      type: Input,
      args: ["previewOptions.autoRender"]
    }],
    previewOptions_dataSources_input: [{
      type: Input,
      args: ["previewOptions.dataSources"]
    }],
    previewOptions_enableNotificationBar_input: [{
      type: Input,
      args: ["previewOptions.enableNotificationBar"]
    }],
    previewOptions_enableVirtualEvaluation_input: [{
      type: Input,
      args: ["previewOptions.enableVirtualEvaluation"]
    }],
    previewOptions_enableParameterBlockScroller_input: [{
      type: Input,
      args: ["previewOptions.enableParameterBlockScroller"]
    }],
    previewOptions_enableDatasourceBlockScroller_input: [{
      type: Input,
      args: ["previewOptions.enableDatasourceBlockScroller"]
    }],
    previewOptions_enableDropDownSearch_input: [{
      type: Input,
      args: ["previewOptions.enableDropDownSearch"]
    }],
    previewOptions_exportSettings_input: [{
      type: Input,
      args: ["previewOptions.exportSettings"]
    }],
    previewOptions_pageSettings_input: [{
      type: Input,
      args: ["previewOptions.pageSettings"]
    }],
    previewOptions_parameters_input: [{
      type: Input,
      args: ["previewOptions.parameters"]
    }],
    previewOptions_parameterSettings_input: [{
      type: Input,
      args: ["previewOptions.parameterSettings"]
    }],
    previewOptions_printMode_input: [{
      type: Input,
      args: ["previewOptions.printMode"]
    }],
    previewOptions_printOption_input: [{
      type: Input,
      args: ["previewOptions.printOption"]
    }],
    previewOptions_sizeToReportContent_input: [{
      type: Input,
      args: ["previewOptions.sizeToReportContent"]
    }],
    previewOptions_toolbarSettings_input: [{
      type: Input,
      args: ["previewOptions.toolbarSettings"]
    }],
    previewOptions_zoomFactor_input: [{
      type: Input,
      args: ["previewOptions.zoomFactor"]
    }],
    reportDataExtensions_name_input: [{
      type: Input,
      args: ["reportDataExtensions.name"]
    }],
    reportDataExtensions_className_input: [{
      type: Input,
      args: ["reportDataExtensions.className"]
    }],
    reportDataExtensions_imageClass_input: [{
      type: Input,
      args: ["reportDataExtensions.imageClass"]
    }],
    reportDataExtensions_displayName_input: [{
      type: Input,
      args: ["reportDataExtensions.displayName"]
    }],
    reportItemExtensions_name_input: [{
      type: Input,
      args: ["reportItemExtensions.name"]
    }],
    reportItemExtensions_className_input: [{
      type: Input,
      args: ["reportItemExtensions.className"]
    }],
    reportItemExtensions_imageClass_input: [{
      type: Input,
      args: ["reportItemExtensions.imageClass"]
    }],
    reportItemExtensions_displayName_input: [{
      type: Input,
      args: ["reportItemExtensions.displayName"]
    }],
    reportItemExtensions_category_input: [{
      type: Input,
      args: ["reportItemExtensions.category"]
    }],
    reportItemExtensions_allowHeaderFooter_input: [{
      type: Input,
      args: ["reportItemExtensions.allowHeaderFooter"]
    }],
    toolbarSettings_items_input: [{
      type: Input,
      args: ["toolbarSettings.items"]
    }],
    toolbarSettings_showToolbar_input: [{
      type: Input,
      args: ["toolbarSettings.showToolbar"]
    }],
    toolbarSettings_templateId_input: [{
      type: Input,
      args: ["toolbarSettings.templateId"]
    }],
    ajaxBeforeLoad_output: [{
      type: Output,
      args: ["ajaxBeforeLoad"]
    }],
    ajaxError_output: [{
      type: Output,
      args: ["ajaxError"]
    }],
    ajaxSuccess_output: [{
      type: Output,
      args: ["ajaxSuccess"]
    }],
    create_output: [{
      type: Output,
      args: ["create"]
    }],
    destroy_output: [{
      type: Output,
      args: ["destroy"]
    }],
    newDataClick_output: [{
      type: Output,
      args: ["newDataClick"]
    }],
    openReportClick_output: [{
      type: Output,
      args: ["openReportClick"]
    }],
    previewReport_output: [{
      type: Output,
      args: ["previewReport"]
    }],
    reportModified_output: [{
      type: Output,
      args: ["reportModified"]
    }],
    reportOpened_output: [{
      type: Output,
      args: ["reportOpened"]
    }],
    reportSaved_output: [{
      type: Output,
      args: ["reportSaved"]
    }],
    saveReportClick_output: [{
      type: Output,
      args: ["saveReportClick"]
    }],
    toolbarClick_output: [{
      type: Output,
      args: ["toolbarClick"]
    }],
    toolbarRendering_output: [{
      type: Output,
      args: ["toolbarRendering"]
    }],
    encryptData_output: [{
      type: Output,
      args: ["encryptData"]
    }],
    decryptData_output: [{
      type: Output,
      args: ["decryptData"]
    }]
  });
})();
var BoldReportDesignerModule = class {
};
BoldReportDesignerModule.ɵfac = function BoldReportDesignerModule_Factory(t) {
  return new (t || BoldReportDesignerModule)();
};
BoldReportDesignerModule.ɵmod = ɵɵdefineNgModule({
  type: BoldReportDesignerModule,
  declarations: [BoldReportDesignerComponent],
  imports: [CommonModule],
  exports: [BoldReportDesignerComponent]
});
BoldReportDesignerModule.ɵinj = ɵɵdefineInjector({
  imports: [[CommonModule]]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportDesignerModule, [{
    type: NgModule,
    args: [{
      imports: [CommonModule],
      declarations: [BoldReportDesignerComponent],
      exports: [BoldReportDesignerComponent]
    }]
  }], null, null);
})();
var BoldReportsModule = class {
};
BoldReportsModule.ɵfac = function BoldReportsModule_Factory(t) {
  return new (t || BoldReportsModule)();
};
BoldReportsModule.ɵmod = ɵɵdefineNgModule({
  type: BoldReportsModule,
  imports: [CommonModule, BoldReportViewerModule, BoldReportDesignerModule],
  exports: [BoldReportViewerModule, BoldReportDesignerModule]
});
BoldReportsModule.ɵinj = ɵɵdefineInjector({
  imports: [[CommonModule, BoldReportViewerModule, BoldReportDesignerModule], BoldReportViewerModule, BoldReportDesignerModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BoldReportsModule, [{
    type: NgModule,
    args: [{
      imports: [CommonModule, BoldReportViewerModule, BoldReportDesignerModule],
      declarations: [],
      exports: [BoldReportViewerModule, BoldReportDesignerModule]
    }]
  }], null, null);
})();
export {
  ArrayTagElement,
  BoldReportComponents,
  BoldReportDesignerComponent,
  BoldReportDesignerModule,
  BoldReportViewerComponent,
  BoldReportViewerModule,
  BoldReportsModule,
  ComplexTagElement,
  ContentChild,
  Type,
  Utils,
  currentTemplateElement,
  forwardRef
};
//# sourceMappingURL=@boldreports_angular-reporting-components.js.map
