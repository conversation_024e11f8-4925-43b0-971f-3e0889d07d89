﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class DisciplineTypeRepository : GenericRepository<DisciplineTypeEntity, int>, IDisciplineTypeRepository
    {
        public AppDbContext Context { get; set; }

        public DisciplineTypeRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(DisciplineTypeEntity requestObject, DisciplineTypeEntity targetObject)
        {
            targetObject.DisciplineTypeCode = requestObject.DisciplineTypeCode;
            targetObject.DisciplineTypeName = requestObject.DisciplineTypeName;
            targetObject.Active = requestObject.Active;
            targetObject.Class = requestObject.Class;
        }
    }
}
