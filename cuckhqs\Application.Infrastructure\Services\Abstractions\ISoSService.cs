﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Request.Category.Position;
using Application.Infrastructure.Models.Request.Category.SoS;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface ISoSService
    {
        Task<List<SoSResponse>> GetAllSoSAsync();
        Task<List<SoSResponse>> GetAllSoSBuildTreeAsync();
        Task<BaseSearchResponse<SoSResponse>> SearchSoSAsync(SearchSoSRequest request);
        Task<List<SoSResponse>> GetSoSById(int Id);
        Task<SoSResponse> CreateSoSAsync(CreateSoSRequest request);

        Task<string> DeleteSoSAsync(DeleteSoSRequest request);
        Task<bool> UpdateSoSAsync(UpdateSoSRequest request);
    }
}
