﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using Application.Infrastructure.Repositories.Abstractions;

namespace Application.Infrastructure.Models.Request.WorkingSchedule
{
    public class CreateWorkingScheduleRequest
    {
        public string? Classify { get; set; }
        public string? CoChair { get; set; }
        public string? Contents { get; set; }
        public string? Date { get; set; }
        public int? Id { get; set; }
        public string? MTEntityState { get; set; }
        public string? Member { get; set; }
        public string? Message { get; set; }
        public string? Note { get; set; }
        public string? OrganizationUnitId { get; set; }
        public string? OrganizationUnitId_Chair { get; set; }
        public string? Place { get; set; }
        public string? Register { get; set; }
        public string? Time { get; set; }
        public string? TimeFrom { get; set; }
        public string? TimeTo { get; set; }
        public string? Week { get; set; }
        public string? Year { get; set; }
        public List<string>? WorkingScheduleC { get; set; }
        public List<string>? WorkingScheduleEP { get; set; }
        public List<string>? WorkingScheduleEPH { get; set; }
        public List<string>? WorkingScheduleOU { get; set; }
    }
}
