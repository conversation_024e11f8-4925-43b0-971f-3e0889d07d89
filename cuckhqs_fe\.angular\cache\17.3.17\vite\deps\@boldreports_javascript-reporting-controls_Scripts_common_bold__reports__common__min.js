// node_modules/@boldreports/javascript-reporting-controls/Scripts/common/bold.reports.common.min.js
(function(n) {
  typeof define == "function" && define.amd ? define(["jquery"], n) : n();
})(function() {
  window.ej = window.Syncfusion = window.Syncfusion || {}, function(n, t, i) {
    "use strict";
    var e, u, f, o;
    t.version = "********";
    t.consts = { NamespaceJoin: "-" };
    t.TextAlign = { Center: "center", Justify: "justify", Left: "left", Right: "right" };
    t.Orientation = { Horizontal: "horizontal", Vertical: "vertical" };
    t.serverTimezoneOffset = 0;
    t.parseDateInUTC = false;
    t.persistStateVersion = null;
    t.locales = t.locales || [];
    Object.prototype.hasOwnProperty || (Object.prototype.hasOwnProperty = function(n2, t2) {
      return n2[t2] !== i;
    });
    Date.prototype.toISOString || function() {
      function n2(n3) {
        var t2 = String(n3);
        return t2.length === 1 && (t2 = "0" + t2), t2;
      }
      Date.prototype.toISOString = function() {
        return this.getUTCFullYear() + "-" + n2(this.getUTCMonth() + 1) + "-" + n2(this.getUTCDate()) + "T" + n2(this.getUTCHours()) + ":" + n2(this.getUTCMinutes()) + ":" + n2(this.getUTCSeconds()) + "." + String((this.getUTCMilliseconds() / 1e3).toFixed(3)).slice(2, 5) + "Z";
      };
    }();
    String.format = function() {
      for (var t2 = arguments[0], n2 = 0; n2 < arguments.length - 1; n2++) t2 = t2.replace(new RegExp("\\{" + n2 + "\\}", "gm"), arguments[n2 + 1]);
      return t2.replace(/\{[0-9]\}/g, "");
    };
    jQuery.uaMatch = function(n2) {
      n2 = n2.toLowerCase();
      var t2 = /(chrome)[ \/]([\w.]+)/.exec(n2) || /(webkit)[ \/]([\w.]+)/.exec(n2) || /(opera)(?:.*version|)[ \/]([\w.]+)/.exec(n2) || /(msie) ([\w.]+)/.exec(n2) || n2.indexOf("compatible") < 0 && /(mozilla)(?:.*? rv:([\w.]+)|)/.exec(n2) || [];
      return { browser: t2[1] || "", version: t2[2] || "0" };
    };
    t.defineClass = function(n2, r2, u2, f2) {
      if (!n2 || !u2) return i;
      for (var o2 = n2.split("."), s2 = window, e2 = 0; e2 < o2.length - 1; e2++) t.isNullOrUndefined(s2[o2[e2]]) && (s2[o2[e2]] = {}), s2 = s2[o2[e2]];
      return (f2 || t.isNullOrUndefined(s2[o2[e2]])) && (r2 = typeof r2 == "function" ? r2 : function() {
      }, s2[o2[e2]] = r2, s2[o2[e2]].prototype = u2), s2[o2[e2]];
    };
    t.util = { getNameSpace: function(n2) {
      var i2 = n2.toLowerCase().split(".");
      return i2[0] === "ej" && (i2[0] = "e"), i2.join(t.consts.NamespaceJoin);
    }, getObject: function(n2, r2) {
      var u2, e2, f2;
      if (!r2 || !n2) return i;
      for (typeof n2 != "string" && (n2 = JSON.stringify(n2)), u2 = r2, e2 = n2.split("."), f2 = 0; f2 < e2.length; f2++) {
        if (t.util.isNullOrUndefined(u2)) break;
        u2 = u2[e2[f2]];
      }
      return u2;
    }, createObject: function(n2, i2, r2) {
      for (var o2 = n2.split("."), s2 = r2 || window, u2 = s2, e2, h2 = o2.length, f2 = 0; f2 < h2; f2++) e2 = o2[f2], f2 + 1 == h2 ? u2[e2] = i2 : t.isNullOrUndefined(u2[e2]) && (u2[e2] = {}), u2 = u2[e2];
      return s2;
    }, isNullOrUndefined: function(n2) {
      return n2 === i || n2 === null;
    }, exportAll: function(i2, r2) {
      var h2 = [], o2, u2 = [], c2, l, a, f2, v, p = { action: i2, method: "post", "data-ajax": "false" }, s2 = t.buildTag("form", "", null, p), e2, y;
      if (r2.length != 0) {
        for (e2 = 0; e2 < r2.length; e2++) c2 = e2, l = n("#" + r2[e2]), a = n("#" + r2[e2]).data(), o2 = a.ejWidgets, f2 = n(l).data(o2[0]), u2.push({ id: f2._id, locale: f2.model.locale }), t.isNullOrUndefined(f2) || (v = f2._getExportModel(f2.model), h2.push({ name: o2[0], type: "hidden", value: f2.stringify(v) }), y = t.buildTag("input", "", null, h2[c2]), s2.append(y));
        n("body").append(s2);
        s2.submit();
        setTimeout(function() {
          var r3, f3, i3;
          if (u2.length) for (i3 = 0; i3 < u2.length; i3++) t.isNullOrUndefined(u2[i3].locale) || (r3 = n("#" + u2[i3].id).data(), o2 = r3.ejWidgets, f3 = n("#" + u2[i3].id).data(o2[0]), f3.model.locale = u2[i3].locale);
        }, 0);
        s2.remove();
      }
      return true;
    }, print: function(i2, r2) {
      var f2 = t.buildTag("div"), o2 = i2.clone(), r2, e2, u2;
      f2.append(o2);
      r2 || (r2 = window.open("", "print", "height=452,width=1024,tabbar=no", "noopener"));
      r2.document.write("<!DOCTYPE html>");
      e2 = n("head").find("link").add("style");
      t.browserInfo().name === "msie" ? (u2 = "", e2.each(function(t2, i3) {
        i3.tagName == "LINK" && n(i3).attr("href", i3.href);
        u2 += i3.outerHTML;
      }), r2.document.write("<html><head></head><body>" + u2 + f2[0].innerHTML + "</body></html>")) : (u2 = "", r2.document.write("<html><head>"), e2.each(function(t2, i3) {
        i3.tagName == "LINK" && n(i3).attr("href", i3.href);
        u2 += i3.outerHTML;
      }), r2.document.writeln(u2 + "</head><body>"), r2.document.writeln(f2[0].innerHTML + "</body></html>"));
      r2.document.close();
      r2.focus();
      setTimeout(function() {
        t.isNullOrUndefined(r2.window) || (r2.print(), setTimeout(function() {
          r2.close();
        }, 1e3));
      }, 1e3);
    }, ieClearRemover: function(t2) {
      var i2 = n(t2).height();
      t2.style.paddingTop = parseFloat(i2 / 2) + "px";
      t2.style.paddingBottom = parseFloat(i2 / 2) + "px";
      t2.style.height = "1px";
      t2.style.lineHeight = "1px";
    }, sendAjaxRequest: function(t2) {
      n.ajax({ type: t2.type, cache: t2.cache, url: t2.url, dataType: t2.dataType, data: t2.data, contentType: t2.contentType, async: t2.async, success: t2.successHandler, error: t2.errorHandler, beforeSend: t2.beforeSendHandler, complete: t2.completeHandler });
    }, buildTag: function(t2, r2, u2, f2) {
      var s2 = /^[a-z]*[0-9a-z]+/ig.exec(t2)[0], e2 = /#([_a-z0-9-&@\/\\,+()$~%:*?<>{}\[\]]+\S)/ig.exec(t2), o2;
      return e2 = e2 ? e2[e2.length - 1].replace(/[&@\/\\,+()$~%.:*?<>{}\[\]]/g, "") : i, o2 = /\.([a-z]+[-_0-9a-z ]+)/ig.exec(t2), o2 = o2 ? o2[o2.length - 1] : i, n(document.createElement(s2)).attr(e2 ? { id: e2 } : {}).addClass(o2 || "").css(u2 || {}).attr(f2 || {}).html(r2 || "");
    }, _preventDefaultException: function(n2, t2) {
      if (n2) {
        for (var i2 in t2) if (t2[i2].test(n2[i2])) return true;
      }
      return false;
    }, getMaxZindex: function() {
      var t2 = 1;
      return t2 = Math.max.apply(null, n.map(n("body *"), function(t3) {
        if (n(t3).css("position") == "absolute" || n(t3).css("position") == "fixed") return parseInt(n(t3).css("z-index")) || 1;
      })), (t2 == i || t2 == null) && (t2 = 1), t2;
    }, blockDefaultActions: function(n2) {
      n2.cancelBubble = true;
      n2.returnValue = false;
      n2.preventDefault && n2.preventDefault();
      n2.stopPropagation && n2.stopPropagation();
    }, getDimension: function(t2, i2) {
      var e2, u2 = n(t2).parents().andSelf().filter(":hidden"), r2, f2;
      return u2 && (r2 = { visibility: "hidden", display: "block" }, f2 = [], u2.each(function() {
        var t3 = {};
        for (var n2 in r2) t3[n2] = this.style[n2], this.style[n2] = r2[n2];
        f2.push(t3);
      }), e2 = /(outer)/g.test(i2) ? n(t2)[i2](true) : n(t2)[i2](), u2.each(function(n2) {
        var i3 = f2[n2];
        for (var t3 in r2) this.style[t3] = i3[t3];
      })), e2;
    }, transitionEndEvent: function() {
      return { "": "transitionend", webkit: "webkitTransitionEnd", Moz: "transitionend", O: "otransitionend", ms: "MSTransitionEnd" }[t.userAgent()];
    }, animationEndEvent: function() {
      return { "": "animationend", webkit: "webkitAnimationEnd", Moz: "animationend", O: "webkitAnimationEnd", ms: "animationend" }[t.userAgent()];
    }, startEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? "touchstart" : "mousedown";
    }, endEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? "touchend" : "mouseup";
    }, moveEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? n.support.hasPointer && !t.isMobile() ? "ejtouchmove" : "touchmove" : "mousemove";
    }, cancelEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? "touchcancel" : "mousecancel";
    }, tapEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? "tap" : "click";
    }, tapHoldEvent: function() {
      return t.isTouchDevice() || n.support.hasPointer ? "taphold" : "click";
    }, isDevice: function() {
      return t.getBooleanVal(n("head"), "data-ej-forceset", false) ? t.getBooleanVal(n("head"), "data-ej-device", this._device()) : this._device();
    }, isPortrait: function() {
      var n2 = document.documentElement;
      return n2 && n2.clientWidth / n2.clientHeight < 1.1;
    }, isLowerResolution: function() {
      return window.innerWidth <= 640 && t.isPortrait() && t.isDevice() || window.innerWidth <= 800 && !t.isDevice() || window.innerWidth <= 800 && !t.isPortrait() && t.isWindows() && t.isDevice() || t.isMobile();
    }, isIOSWebView: function() {
      return /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent);
    }, isAndroidWebView: function() {
      return !(typeof Android == "undefined");
    }, isWindowsWebView: function() {
      return location.href.indexOf("x-wmapp") != -1;
    }, _device: function() {
      return /Android|BlackBerry|iPhone|iPad|iPod|IEMobile|kindle|windows\sce|palm|smartphone|iemobile|mobile|pad|xoom|sch-i800|playbook/i.test(navigator.userAgent.toLowerCase());
    }, isMobile: function() {
      return /iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(navigator.userAgent.toLowerCase()) && /mobile/i.test(navigator.userAgent.toLowerCase()) || t.getBooleanVal(n("head"), "data-ej-mobile", false) === true;
    }, isTablet: function() {
      return /ipad|xoom|sch-i800|playbook|tablet|kindle/i.test(navigator.userAgent.toLowerCase()) || t.getBooleanVal(n("head"), "data-ej-tablet", false) === true || !t.isMobile() && t.isDevice();
    }, isTouchDevice: function() {
      return ("ontouchstart" in window || window.navigator.msPointerEnabled && t.isMobile()) && this.isDevice();
    }, getClearString: function(t2) {
      return n.trim(t2.replace(/\s+/g, " ").replace(/(\r\n|\n|\r)/gm, "").replace(new RegExp(">[\n	 ]+<", "g"), "><"));
    }, getBooleanVal: function(t2, i2, r2) {
      var u2 = n(t2).attr(i2);
      return u2 != null ? u2.toLowerCase() == "true" : r2;
    }, _getSkewClass: function(n2, t2, i2) {
      var h2 = n2.width(), c2 = n2.height(), f2 = n2.offset().left, e2 = n2.offset().left + h2, o2 = n2.offset().top, s2 = n2.offset().top + c2, r2 = h2 * 0.3, u2 = c2 * 0.3;
      return t2 < f2 + r2 && i2 < o2 + u2 ? "e-m-skew-topleft" : t2 > e2 - r2 && i2 < o2 + u2 ? "e-m-skew-topright" : t2 > e2 - r2 && i2 > s2 - u2 ? "e-m-skew-bottomright" : t2 < f2 + r2 && i2 > s2 - u2 ? "e-m-skew-bottomleft" : t2 > f2 + r2 && i2 < o2 + u2 && t2 < e2 - r2 ? "e-m-skew-top" : t2 < f2 + r2 ? "e-m-skew-left" : t2 > e2 - r2 ? "e-m-skew-right" : i2 > s2 - u2 ? "e-m-skew-bottom" : "e-m-skew-center";
    }, _removeSkewClass: function(t2) {
      n(t2).removeClass("e-m-skew-top e-m-skew-bottom e-m-skew-left e-m-skew-right e-m-skew-topleft e-m-skew-topright e-m-skew-bottomleft e-m-skew-bottomright e-m-skew-center e-skew-top e-skew-bottom e-skew-left e-skew-right e-skew-topleft e-skew-topright e-skew-bottomleft e-skew-bottomright e-skew-center");
    }, _getObjectKeys: function(n2) {
      var t2, i2 = [];
      if (n2 = Object.prototype.toString.call(n2) === Object.prototype.toString() ? n2 : {}, !Object.keys) {
        for (t2 in n2) n2.hasOwnProperty(t2) && i2.push(t2);
        return i2;
      }
      if (Object.keys) return Object.keys(n2);
    }, _touchStartPoints: function(n2, t2) {
      if (n2) {
        var i2 = n2.touches ? n2.touches[0] : n2;
        t2._distX = 0;
        t2._distY = 0;
        t2._moved = false;
        t2._pointX = i2.pageX;
        t2._pointY = i2.pageY;
      }
    }, _isTouchMoved: function(n2, t2) {
      if (n2) {
        var i2 = n2.touches ? n2.touches[0] : n2, f2 = i2.pageX - t2._pointX, e2 = i2.pageY - t2._pointY, o2 = Date.now(), r2, u2;
        return t2._pointX = i2.pageX, t2._pointY = i2.pageY, t2._distX += f2, t2._distY += e2, r2 = Math.abs(t2._distX), u2 = Math.abs(t2._distY), !(r2 < 5 && u2 < 5);
      }
    }, listenEvents: function(n2, i2, r2, u2, f2, e2) {
      for (var o2 = 0; o2 < n2.length; o2++) t.listenTouchEvent(n2[o2], i2[o2], r2[o2], u2, f2, e2);
    }, listenTouchEvent: function(i2, r2, u2, f2, e2, o2) {
      for (var s2, h2 = f2 ? "removeEventListener" : "addEventListener", a = f2 ? "off" : "on", l = n(i2), c2 = 0; c2 < l.length; c2++) {
        s2 = l[c2];
        switch (r2) {
          case "touchstart":
            t._bindEvent(s2, h2, r2, u2, "mousedown", "MSPointerDown", "pointerdown", o2);
            break;
          case "touchmove":
            t._bindEvent(s2, h2, r2, u2, "mousemove", "MSPointerMove", "pointermove", o2);
            break;
          case "touchend":
            t._bindEvent(s2, h2, r2, u2, "mouseup", "MSPointerUp", "pointerup", o2);
            break;
          case "touchcancel":
            t._bindEvent(s2, h2, r2, u2, "mousecancel", "MSPointerCancel", "pointercancel", o2);
            break;
          case "tap":
          case "taphold":
          case "ejtouchmove":
          case "click":
            n(s2)[a](r2, u2);
            break;
          default:
            t.browserInfo().name == "msie" && t.browserInfo().version < 9 ? e2._on(n(s2), r2, u2) : s2[h2](r2, u2, true);
        }
      }
    }, _bindEvent: function(t2, i2, r2, u2, f2, e2, o2) {
      n.support.hasPointer ? t2[i2](window.navigator.pointerEnabled ? o2 : e2, u2, true) : t2[i2](r2, u2, true);
    }, _browser: function() {
      return /webkit/i.test(navigator.appVersion) ? "webkit" : /firefox/i.test(navigator.userAgent) ? "Moz" : /trident/i.test(navigator.userAgent) ? "ms" : "opera" in window ? "O" : "";
    }, styles: document.createElement("div").style, userAgent: function() {
      for (var i2 = "webkitT,t,MozT,msT,OT".split(","), r2, n2 = 0, u2 = i2.length; n2 < u2; n2++) if (r2 = i2[n2] + "ransform", r2 in t.styles) return i2[n2].substr(0, i2[n2].length - 1);
      return false;
    }, addPrefix: function(n2) {
      return t.userAgent() === "" ? n2 : (n2 = n2.charAt(0).toUpperCase() + n2.substr(1), t.userAgent() + n2);
    }, destroyWidgets: function(t2) {
      var i2 = n(t2).find("[data-role *= ejm]");
      i2.each(function(t3, i3) {
        var r2 = n(i3), u2 = r2.data("ejWidgets");
        u2 && r2[u2]("destroy");
      });
    }, getAttrVal: function(t2, i2, r2) {
      var u2 = n(t2).attr(i2);
      return u2 != null ? u2 : r2;
    }, getOffset: function(t2) {
      var i2 = {}, u2 = t2.offset() || { left: 0, top: 0 }, r2;
      return n.extend(true, i2, u2), n("body").css("position") != "static" && (r2 = n("body").offset(), i2.left -= r2.left, i2.top -= r2.top), i2;
    }, getZindexPartial: function(i2, r2) {
      var e2, f2, u2;
      if (!t.isNullOrUndefined(i2) && i2.length > 0) return e2 = i2.parents(), f2 = n("body").children(), !t.isNullOrUndefined(i2) && i2.length > 0 && f2.splice(f2.index(r2), 1), n(f2).each(function(n2, t2) {
        e2.push(t2);
      }), u2 = Math.max.apply(u2, n.map(e2, function(t2) {
        if (n(t2).css("position") != "static") return parseInt(n(t2).css("z-index")) || 1;
      })), !u2 || u2 < 1e4 ? u2 = 1e4 : u2 += 1, u2;
    }, isValidAttr: function(t2, i2) {
      var t2 = n(t2)[0], r2;
      return typeof t2[i2] != "undefined" ? true : (r2 = false, n.each(t2, function(n2) {
        if (n2.toLowerCase() == i2.toLowerCase()) return r2 = true, false;
      }), r2);
    } };
    n.extend(t, t.util);
    t.widgetBase = { droppables: { "default": [] }, resizables: { "default": [] }, _renderEjTemplate: function(i2, r2, u2, f2, e2) {
      var o2 = null;
      if ((typeof i2 == "object" || i2.startsWith("#") || i2.startsWith(".")) && (o2 = n(i2).attr("type")), o2) {
        if (o2 = o2.toLowerCase(), t.template[o2]) return t.template[o2](this, i2, r2, u2, f2);
      } else if (!t.isNullOrUndefined(e2)) return t.template["text/x-" + e2](this, i2, r2, u2, f2);
      return t.template.render(this, i2, r2, u2, f2);
    }, destroy: function() {
      var u2, r2, f2, i2;
      if (!this._trigger("destroy")) {
        this.model.enablePersistence && (this.persistState(), n(window).off("unload", this._persistHandler));
        try {
          this._destroy();
        } catch (e2) {
        }
        for (u2 = this.element.data("ejWidgets") || [], i2 = 0; i2 < u2.length; i2++) u2[i2] == this.pluginName && u2.splice(i2, 1);
        for (u2.length || this.element.removeData("ejWidgets"); this._events; ) {
          if (r2 = this._events.pop(), f2 = [], !r2) break;
          for (i2 = 0; i2 < r2[1].length; i2++) n.isPlainObject(r2[1][i2]) || f2.push(r2[1][i2]);
          n.fn.off.apply(r2[0], f2);
        }
        this._events = null;
        this.element.removeClass(t.util.getNameSpace(this.sfType)).removeClass("e-js").removeData(this.pluginName);
        this.element = null;
        this.model = null;
      }
    }, _on: function(i2) {
      this._events || (this._events = []);
      for (var r2 = [].splice.call(arguments, 1, arguments.length - 1), u2 = {}, f2 = r2.length; u2 && typeof u2 != "function"; ) u2 = r2[--f2];
      return r2[f2] = t.proxy(r2[f2], this), this._events.push([i2, r2, u2, r2[f2]]), n.fn.on.apply(i2, r2), this;
    }, _off: function(t2, i2, r2, u2) {
      var e2 = this._events, s2, h2, o2, f2, c2;
      if (!e2 || !e2.length) return this;
      for (typeof r2 == "function" && (s2 = u2, u2 = r2, r2 = s2), h2 = i2.match(/\S+/g) || [""], o2 = 0; o2 < e2.length; o2++) if (f2 = e2[o2], c2 = f2[0].length && (!u2 || f2[2] === u2) && (f2[1][0] === i2 || h2[0]) && (!r2 || f2[1][1] === r2) && n.inArray(t2[0], f2[0]) > -1, c2) {
        n.fn.off.apply(t2, u2 ? [i2, r2, f2[3]] : [i2, r2]);
        e2.splice(o2, 1);
        break;
      }
      return this;
    }, _trigger: function(i2, r2) {
      var f2 = null, e2, u2, o2 = {}, s2;
      return (n.extend(o2, r2), i2 in this.model && (f2 = this.model[i2]), f2 && (typeof f2 == "string" && (f2 = t.util.getObject(f2, window)), n.isFunction(f2) && (u2 = t.event(i2, this.model, r2), e2 = f2.call(this, u2), r2 && n.extend(r2, u2), u2.cancel || !t.isNullOrUndefined(e2)))) ? e2 === false || u2.cancel : (s2 = Boolean(r2), r2 = r2 || {}, r2.originalEventType = i2, r2.type = this.pluginName + i2, u2 = n.Event(r2.type, t.event(r2.type, this.model, r2)), this.element && this.element.trigger(u2), s2 && n.extend(r2, u2), t.isOnWebForms && u2.cancel == false && this.model.serverEvents && this.model.serverEvents.length && t.raiseWebFormsServerEvents(i2, r2, o2), u2.cancel);
    }, setModel: function(t2, i2) {
      var r2, f2, o2, u2;
      if (!this._trigger("modelChange", { changes: t2 })) {
        for (r2 in t2) {
          if (!i2) {
            if (this.model[r2] === t2[r2]) {
              delete t2[r2];
              continue;
            }
            if (n.isPlainObject(t2[r2]) && (e(this.model[r2], t2[r2]), n.isEmptyObject(t2[r2]))) {
              delete t2[r2];
              continue;
            }
          }
          if (this.dataTypes && (f2 = this._isValidModelValue(r2, this.dataTypes, t2), f2 !== true)) throw "setModel - Invalid input for property :" + r2 + " - " + f2;
          this.model.notifyOnEachPropertyChanges && this.model[r2] !== t2[r2] && (o2 = { oldValue: this.model[r2], newValue: t2[r2] }, t2[r2] = this._trigger(r2 + "Change", o2) ? this.model[r2] : o2.newValue);
        }
        n.isEmptyObject(t2) || (this._setFirst ? (u2 = t2.dataSource, u2 && delete t2.dataSource, n.extend(true, this.model, t2), u2 && (this.model.dataSource = u2 instanceof Array ? u2.slice() : u2, t2.dataSource = this.model.dataSource), this._setModel && this._setModel(t2)) : this._setModel && this._setModel(t2) === false || n.extend(true, this.model, t2), "enablePersistence" in t2 && this._setState(t2.enablePersistence));
      }
    }, option: function(r2, u2, f2) {
      if (!r2) return this.model;
      if (n.isPlainObject(r2)) return this.setModel(r2, f2);
      if (typeof r2 == "string") {
        r2 = r2.replace(/^model\./, "");
        var e2 = t.getObject(r2, this.model);
        if (u2 === i && !f2) return e2;
        if (r2 === "enablePersistence") return this._setState(u2);
        if (f2 && u2 === t.extensions.modelGUID) return this._setModel(t.createObject(r2, t.getObject(r2, this.model), {}));
        if (f2 || t.getObject(r2, this.model) !== u2) return this.setModel(t.createObject(r2, u2, {}), f2);
      }
      return i;
    }, _isValidModelValue: function(n2, t2, i2) {
      var r2 = t2[n2], u2 = i2[n2], f2, e2, o2;
      if (!r2) return true;
      if (typeof r2 == "string") {
        if (r2 == "enum" && (i2[n2] = u2 ? u2.toString().toLowerCase() : u2, r2 = "string"), r2 === "array") {
          if (Object.prototype.toString.call(u2) === "[object Array]") return true;
        } else if (r2 === "data" || r2 === "parent" || typeof u2 === r2) return true;
        return "Expected type - " + r2;
      }
      if (u2 instanceof Array) {
        for (e2 = 0; e2 < u2.length; e2++) if (f2 = this._isValidModelValue(n2, t2, u2[e2]), f2 !== true) return " [" + e2 + "] - " + f2;
        return true;
      }
      for (o2 in u2) if (f2 = this._isValidModelValue(o2, r2, u2), f2 !== true) return o2 + " : " + f2;
      return true;
    }, _returnFn: function(n2, t2) {
      t2.indexOf(".") != -1 ? this._returnFn(n2[t2.split(".")[0]], t2.split(".").slice(1).join(".")) : n2[t2] = n2[t2].call(n2.propName);
    }, _removeCircularRef: function(n2) {
      function i2(n3, r2, u2) {
        if (typeof n3 == "object") {
          if (Array.prototype.indexOf || (Array.prototype.indexOf = function(n4) {
            return jQuery.inArray(n4, this);
          }), t2.indexOf(n3) >= 0) {
            delete u2[r2];
            return;
          }
          t2.push(n3);
          for (var f2 in n3) n3.hasOwnProperty(f2) && i2(n3[f2], f2, n3);
          t2.pop();
          return;
        }
      }
      var t2 = [];
      return i2(n2, "obj", null), n2;
    }, stringify: function(n2, i2) {
      for (var f2, u2 = this.observables, r2 = 0; r2 < u2.length; r2++) f2 = t.getObject(u2[r2], n2), t.isNullOrUndefined(f2) || typeof f2 != "function" || this._returnFn(n2, u2[r2]);
      return i2 && (n2 = this._removeCircularRef(n2)), JSON.stringify(n2);
    }, _setState: function(i2) {
      if (i2 === true) {
        this._persistHandler = t.proxy(this.persistState, this);
        n(window).on("unload", this._persistHandler);
      } else this.deleteState(), n(window).off("unload", this._persistHandler);
    }, _removeProp: function(n2, i2) {
      t.isNullOrUndefined(n2) || (i2.indexOf(".") != -1 ? this._removeProp(n2[i2.split(".")[0]], i2.split(".").slice(1).join(".")) : delete n2[i2]);
    }, persistState: function() {
      var n2, i2;
      if (this._ignoreOnPersist) {
        for (n2 = r({}, this.model), i2 = 0; i2 < this._ignoreOnPersist.length; i2++) this._removeProp(n2, this._ignoreOnPersist[i2]);
        n2.ignoreOnPersist = this._ignoreOnPersist;
      } else if (this._addToPersist) {
        for (n2 = {}, i2 = 0; i2 < this._addToPersist.length; i2++) t.createObject(this._addToPersist[i2], t.getObject(this._addToPersist[i2], this.model), n2);
        n2.addToPersist = this._addToPersist;
      } else n2 = r({}, this.model);
      this._persistState && (n2.customPersists = {}, this._persistState(n2.customPersists));
      window.localStorage ? (t.isNullOrUndefined(t.persistStateVersion) || window.localStorage.getItem("persistKey") != null || window.localStorage.setItem("persistKey", t.persistStateVersion), window.localStorage.setItem("$ej$" + this.pluginName + this._id, JSON.stringify(n2))) : document.cookie && (t.isNullOrUndefined(t.persistStateVersion) || t.cookie.get("persistKey") != null || t.cookie.set("persistKey", t.persistStateVersion), t.cookie.set("$ej$" + this.pluginName + this._id, n2));
    }, deleteState: function() {
      var n2;
      window.localStorage ? window.localStorage.removeItem("$ej$" + this.pluginName + this._id) : document.cookie && t.cookie.set("$ej$" + this.pluginName + this._id, n2, /* @__PURE__ */ new Date());
    }, restoreState: function(i2) {
      var f2 = null, r2, u2, e2;
      if (window.localStorage ? f2 = window.localStorage.getItem("$ej$" + this.pluginName + this._id) : document.cookie && (f2 = t.cookie.get("$ej$" + this.pluginName + this._id)), f2 && (r2 = JSON.parse(f2), this._restoreState && (this._restoreState(r2.customPersists), delete r2.customPersists), t.isNullOrUndefined(r2) === false && (t.isNullOrUndefined(r2.ignoreOnPersist) ? t.isNullOrUndefined(r2.addToPersist) || (this._addToPersist = r2.addToPersist, delete r2.addToPersist) : (this._ignoreOnPersist = r2.ignoreOnPersist, delete r2.ignoreOnPersist))), t.isNullOrUndefined(r2) || t.isNullOrUndefined(this._ignoreOnPersist)) this.model = n.extend(true, this.model, r2);
      else {
        for (u2 = 0, e2 = this._ignoreOnPersist.length; u2 < e2; u2++) this._ignoreOnPersist[u2].indexOf(".") !== -1 ? t.createObject(this._ignoreOnPersist[u2], t.getObject(this._ignoreOnPersist[u2], this.model), r2) : r2[this._ignoreOnPersist[u2]] = this.model[this._ignoreOnPersist[u2]];
        this.model = r2;
      }
      !i2 && f2 && this._setModel && this._setModel(this.model);
    }, ignoreOnPersist: function(n2) {
      var r2 = [], t2, u2;
      if (typeof n2 == "object" ? r2 = n2 : typeof n2 == "string" && r2.push(n2), this._addToPersist === i) for (this._ignoreOnPersist = this._ignoreOnPersist || [], t2 = 0; t2 < r2.length; t2++) this._ignoreOnPersist.push(r2[t2]);
      else for (t2 = 0; t2 < r2.length; t2++) u2 = this._addToPersist.indexOf(r2[t2]), this._addToPersist.splice(u2, 1);
    }, addToPersist: function(t2) {
      var u2 = [], f2, r2;
      if (typeof t2 == "object" ? u2 = t2 : typeof t2 == "string" && u2.push(t2), this._addToPersist === i) for (this._ignoreOnPersist = this._ignoreOnPersist || [], r2 = 0; r2 < u2.length; r2++) f2 = this._ignoreOnPersist.indexOf(u2[r2]), this._ignoreOnPersist.splice(f2, 1);
      else for (r2 = 0; r2 < u2.length; r2++) n.inArray(u2[r2], this._addToPersist) === -1 && this._addToPersist.push(u2[r2]);
    }, formatting: function(i2, r2, u2) {
      var f2, l, h2, c2, a, v, s2, e2, y;
      if (i2 = i2.replace(/%280/g, '"').replace(/&lt;/g, "<").replace(/&gt;/g, ">"), u2 = t.preferredCulture(u2) ? u2 : "en-US", f2 = i2, l = i2.split("{0:"), a = i2.split("}"), h2 = l[0], c2 = a[1], typeof r2 == "string" && n.isNumeric(r2) && (r2 = Number(r2)), i2.indexOf("{0:") != -1) return v = new RegExp("\\{0(:([^\\}]+))?\\}", "gm"), s2 = v.exec(i2), s2 != null && r2 != null ? h2 != null && c2 != null ? h2 + t.format(r2, s2[2], u2) + c2 : t.format(r2, s2[2], u2) : r2 != null ? r2 : "";
      if (f2.startsWith("{") && !f2.startsWith("{0:")) {
        var o2 = f2.split(""), r2 = (r2 || "") + "", p = r2.split(""), w = /[0aA\*CN<>\?]/gm;
        for (e2 = 0, y = 0; e2 < o2.length; e2++) o2[e2] = w.test(o2[e2]) ? "{" + y++ + "}" : o2[e2];
        return String.format.apply(String, [o2.join("")].concat(p)).replace("{", "").replace("}", "");
      }
      return this.data != null && this.data.Value == null ? (n.each(this.data, function(n2, t2) {
        f2 = f2.replace(new RegExp("\\{" + n2 + "\\}", "gm"), t2);
      }), f2) : this.data.Value;
    } };
    t.WidgetBase = function() {
    };
    e = function(t2, i2) {
      var u2, f2, r2;
      if (t2 instanceof Array) for (u2 = 0, f2 = t2.length; u2 < f2; u2++) r2 = t2[u2], r2 === i2[r2] && delete i2[r2], n.isPlainObject(i2[r2]) && n.isPlainObject(r2) && e(r2, i2[r2]);
      else for (r2 in t2) t2[r2] === i2[r2] && delete i2[r2], n.isPlainObject(i2[r2]) && n.isPlainObject(t2[r2]) && e(t2[r2], i2[r2]);
    };
    t.widget = function(f2, e2, h2) {
      var a, l, v;
      if (typeof f2 == "object") {
        h2 = e2;
        for (a in f2) l = f2[a], l instanceof Array && (h2._rootCSS = l[1], l = l[0]), t.widget(a, l, h2), f2[a] instanceof Array && (h2._rootCSS = "");
        return;
      }
      v = h2._rootCSS || t.getNameSpace(e2);
      h2 = t.defineClass(e2, function(i2, o2) {
        var y, p, w, b, c2, tt, g, k, d, nt, l2, a2;
        if (this.sfType = e2, this.pluginName = f2, this.instance = s, t.isNullOrUndefined(this._setFirst) && (this._setFirst = true), this["ob.values"] = {}, n.extend(this, t.widgetBase), this.dataTypes) {
          for (y in o2) if (p = this._isValidModelValue(y, this.dataTypes, o2), p !== true) throw "setModel - Invalid input for property :" + y + " - " + p;
        }
        for (w = i2.data("ejWidgets") || [], w.push(f2), i2.data("ejWidgets", w), c2 = 0; t.widget.observables && this.observables && c2 < this.observables.length; c2++) b = t.getObject(this.observables[c2], o2), b && t.createObject(this.observables[c2], t.widget.observables.register(b, this.observables[c2], this, i2), o2);
        if (this.element = i2.jquery ? i2 : n(i2), this.model = r(true, {}, h2.prototype.defaults, o2), this.model.keyConfigs = r(this.keyConfigs), this.element.addClass(v + " e-js").data(f2, this), this._id = i2[0].id, this.model.enablePersistence) {
          if (window.localStorage && !t.isNullOrUndefined(t.persistStateVersion) && window.localStorage.getItem("persistKey") != t.persistStateVersion) for (c2 in window.localStorage) c2.indexOf("$ej$") != -1 && (window.localStorage.removeItem(c2), window.localStorage.setItem("persistKey", t.persistStateVersion));
          else if (document.cookie && !t.isNullOrUndefined(t.persistStateVersion) && t.cookie.get("persistKey") != t.persistStateVersion) {
            g = document.cookie.split(/; */);
            for (k in g) k.indexOf("$ej$") != -1 && (t.cookie.set(k.split("=")[0], tt, /* @__PURE__ */ new Date()), t.cookie.set("persistKey", t.persistStateVersion));
          }
          this._persistHandler = t.proxy(this.persistState, this);
          n(window).on("unload", this._persistHandler);
          this.restoreState(true);
        }
        if (this._init(o2), typeof this.model.keyConfigs == "object" && !(this.model.keyConfigs instanceof Array)) {
          d = false;
          this.model.keyConfigs.focus && this.element.attr("accesskey", this.model.keyConfigs.focus);
          for (nt in this.model.keyConfigs) if (nt !== "focus") {
            d = true;
            break;
          }
          d && this._keyPressed && (l2 = i2, a2 = "keydown", this.keySettings && (l2 = this.keySettings.getElement ? this.keySettings.getElement() || l2 : l2, a2 = this.keySettings.event || a2), this._on(l2, a2, function(n2) {
            if (this.model.keyConfigs) {
              var t2 = u.getActionFromCode(this.model.keyConfigs, n2.which, n2.ctrlKey, n2.shiftKey, n2.altKey), i3 = { code: n2.which, ctrl: n2.ctrlKey, alt: n2.altKey, shift: n2.shiftKey };
              t2 && this._keyPressed(t2, n2.target, i3, n2) === false && n2.preventDefault();
            }
          }));
        }
        this._trigger("create");
      }, h2);
      n.fn[f2] = function(r2) {
        for (var w, p = r2, u2, y = 0; y < this.length; y++) {
          var s2 = n(this[y]), l2 = s2.data(f2), b = l2 && s2.hasClass(v), a2 = null;
          if (this.length > 0 && n.isPlainObject(p) && (r2 = t.copyObject({}, p)), !b) {
            h2.prototype._requiresID !== true || n(this[y]).attr("id") || s2.attr("id", c("ejControl_"));
            r2 && typeof r2 != "object" ? o(f2 + ": methods/properties can be accessed only after plugin creation") : (h2.prototype.defaults && !t.isNullOrUndefined(t.setCulture) && "locale" in h2.prototype.defaults && f2 != "ejChart" && (!r2 || "locale" in r2 ? t.isNullOrUndefined(r2) && (r2 = {}, r2.locale = t.setCulture().name) : r2.locale = t.setCulture().name), new h2(s2, r2));
            continue;
          }
          if (r2) if (u2 = [].slice.call(arguments, 1), this.length > 0 && u2[0] && p === "option" && n.isPlainObject(u2[0]) && (u2[0] = t.copyObject({}, u2[0])), n.isPlainObject(r2)) l2.setModel(r2);
          else if ((r2.indexOf("_") === 0 || t.isNullOrUndefined(a2 = t.getObject(r2, l2))) && r2.indexOf("model.") !== 0) o(e2 + ": function/property - " + r2 + " does not exist");
          else {
            if (!a2 || !n.isFunction(a2)) {
              if (arguments.length == 1) return a2;
              l2.option(r2, arguments[1]);
              continue;
            }
            if (w = a2.apply(l2, u2), w !== i) return w;
          }
        }
        return f2.indexOf("ejm") != -1 && t.widget.registerInstance(s2, f2, e2, h2.prototype), this;
      };
      t.widget.register(f2, e2, h2.prototype);
      t.loadLocale(f2);
    };
    t.loadLocale = function(i2) {
      for (var f2 = t.locales, r2 = 0, u2 = f2.length; r2 < u2; r2++) n.fn["Locale_" + f2[r2]](i2);
    };
    n.extend(t.widget, /* @__PURE__ */ function() {
      var n2 = {}, i2 = [], r2 = function(i3, r3, u3) {
        t.isNullOrUndefined(n2[i3]) || o("ej.widget : The widget named " + i3 + " is trying to register twice.");
        n2[i3] = { name: i3, className: r3, proto: u3 };
        t.widget.extensions && t.widget.extensions.registerWidget(i3);
      }, u2 = function(n3, t2, r3, u3) {
        i2.push({ element: n3, pluginName: t2, className: r3, proto: u3 });
      };
      return { register: r2, registerInstance: u2, registeredWidgets: n2, registeredInstances: i2 };
    }());
    t.widget.destroyAll = function(n2) {
      var u2, r2, t2, i2;
      if (n2 && n2.length) {
        for (u2 = 0; u2 < n2.length; u2++) if (r2 = n2.eq(u2).data(), t2 = r2.ejWidgets, t2 && t2.length) for (i2 = 0; i2 < t2.length; i2++) r2[t2[i2]] && r2[t2[i2]].destroy && r2[t2[i2]].destroy();
      }
    };
    t.cookie = { get: function(n2) {
      var t2 = RegExp(n2 + "=([^;]+)").exec(document.cookie);
      return t2 && t2.length > 1 ? t2[1] : i;
    }, set: function(n2, t2, i2) {
      typeof t2 == "object" && (t2 = JSON.stringify(t2));
      t2 = escape(t2) + (i2 == null ? "" : "; expires=" + i2.toUTCString());
      document.cookie = n2 + "=" + t2;
    } };
    u = { getActionFromCode: function(n2, t2, i2, r2, f2) {
      var s2, o2, e2;
      i2 = i2 || false;
      r2 = r2 || false;
      f2 = f2 || false;
      for (s2 in n2) if (s2 !== "focus") {
        for (o2 = u.getKeyObject(n2[s2]), e2 = 0; e2 < o2.length; e2++) if (t2 === o2[e2].code && i2 == o2[e2].isCtrl && r2 == o2[e2].isShift && f2 == o2[e2].isAlt) return s2;
      }
      return null;
    }, getKeyObject: function(t2) {
      for (var f2, o2, e2, s2 = { isCtrl: false, isShift: false, isAlt: false }, c2 = n.extend(true, {}, s2), r2 = t2.split(","), h2 = [], i2 = 0; i2 < r2.length; i2++) {
        if (f2 = null, r2[i2].indexOf("+") != -1) for (o2 = r2[i2].split("+"), e2 = 0; e2 < o2.length; e2++) f2 = u.getResult(n.trim(o2[e2]), s2);
        else f2 = u.getResult(n.trim(r2[i2]), n.extend(true, {}, c2));
        h2.push(f2);
      }
      return h2;
    }, getResult: function(n2, t2) {
      return n2 === "ctrl" ? t2.isCtrl = true : n2 === "shift" ? t2.isShift = true : n2 === "alt" ? t2.isAlt = true : t2.code = parseInt(n2, 10), t2;
    } };
    t.getScrollableParents = function(t2) {
      return n(t2).parentsUntil("html").filter(function() {
        return n(this).css("overflow") != "visible";
      }).add(n(window));
    };
    t.browserInfo = function() {
      var i2 = {}, r2 = [], e2 = { opera: /(opera|opr)(?:.*version|)[ \/]([\w.]+)/i, edge: /(edge)(?:.*version|)[ \/]([\w.]+)/i, webkit: /(chrome)[ \/]([\w.]+)/i, safari: /(webkit)[ \/]([\w.]+)/i, msie: /(msie|trident) ([\w.]+)/i, mozilla: /(mozilla)(?:.*? rv:([\w.]+)|)/i }, o2, s2, f2, u2;
      for (o2 in e2) if (e2.hasOwnProperty(o2) && (r2 = navigator.userAgent.match(e2[o2]), r2)) {
        if (i2.name = r2[1].toLowerCase() == "opr" ? "opera" : r2[1].toLowerCase(), i2.version = r2[2], i2.culture = {}, i2.culture.name = i2.culture.language = navigator.language || navigator.userLanguage, typeof t.globalize != "undefined") {
          for (s2 = t.preferredCulture().name, f2 = navigator.language || navigator.userLanguage ? t.preferredCulture(navigator.language || navigator.userLanguage) : t.preferredCulture("en-US"), u2 = 0; navigator.languages && u2 < navigator.languages.length; u2++) if (f2 = t.preferredCulture(navigator.languages[u2]), f2.language == navigator.languages[u2]) break;
          t.preferredCulture(s2);
          n.extend(true, i2.culture, f2);
        }
        !navigator.userAgent.match(/Trident\/7\./) || (i2.name = "msie");
        break;
      }
      return i2.isMSPointerEnabled = i2.name == "msie" && i2.version > 9 && window.navigator.msPointerEnabled, i2.pointerEnabled = window.navigator.pointerEnabled, i2;
    };
    t.eventType = { mouseDown: "mousedown touchstart", mouseMove: "mousemove touchmove", mouseUp: "mouseup touchend", mouseLeave: "mouseleave touchcancel", click: "click touchend" };
    t.event = function(t2, i2, r2) {
      return n.extend(r2 || {}, { type: t2, model: i2, cancel: false });
    };
    t.proxy = function(n2, t2, i2) {
      return !n2 || typeof n2 != "function" ? null : "on" in n2 && t2 ? i2 ? n2.on(t2, i2) : n2.on(t2) : function() {
        var r2 = i2 ? [i2] : [];
        return r2.push.apply(r2, arguments), n2.apply(t2 || this, r2);
      };
    };
    t.hasStyle = function(n2) {
      var r2 = document.documentElement.style, i2, t2;
      if (n2 in r2) return true;
      for (i2 = ["ms", "Moz", "Webkit", "O", "Khtml"], n2 = n2[0].toUpperCase() + n2.slice(1), t2 = 0; t2 < i2.length; t2++) if (i2[t2] + n2 in r2) return true;
      return false;
    };
    Array.prototype.indexOf = Array.prototype.indexOf || function(n2) {
      var i2 = this.length, t2;
      if (i2 === 0) return -1;
      for (t2 = 0; t2 < i2; t2++) if (t2 in this && this[t2] === n2) return t2;
      return -1;
    };
    String.prototype.startsWith = String.prototype.startsWith || function(n2) {
      return this.slice(0, n2.length) === n2;
    };
    var r = t.copyObject = function(n2, u2) {
      var h2 = 2, c2, f2, s2, o2, e2, l;
      for (typeof n2 != "boolean" && (h2 = 1), s2 = [].slice.call(arguments, h2), h2 === 1 && (u2 = n2, n2 = i), o2 = 0; o2 < s2.length; o2++) for (e2 in s2[o2]) if (c2 = u2[e2], f2 = s2[o2][e2], f2 !== i && c2 !== f2 && s2[o2] !== f2 && u2 !== f2) if (f2 instanceof Array) if (o2 === 0 && n2) if (e2 === "dataSource" || e2 === "data" || e2 === "predicates") u2[e2] = f2.slice();
      else for (u2[e2] = [], l = 0; l < f2.length; l++) r(true, u2[e2], f2);
      else u2[e2] = f2.slice();
      else t.isPlainObject(f2) ? (u2[e2] = c2 || {}, n2 ? r(n2, u2[e2], f2) : r(u2[e2], f2)) : u2[e2] = f2;
      return u2;
    }, s = function() {
      return this;
    }, h = 0, c = function(n2) {
      return n2 + h++;
    };
    t.template = {};
    t.template.render = t.template["text/x-jsrender"] = function(t2, i2, r2, u2, f2) {
      i2.slice(0, 1) !== "#" && (i2 = ["<div>", i2, "</div>"].join(""));
      var e2 = { prop: f2, index: u2 };
      return n(i2).render(r2, e2);
    };
    t.isPlainObject = function(n2) {
      if (!n2 || t.DataManager !== i && n2 instanceof t.DataManager || typeof n2 != "object" || n2.nodeType || jQuery.isWindow(n2)) return false;
      try {
        if (n2.constructor && !n2.constructor.prototype.hasOwnProperty("isPrototypeOf")) return false;
      } catch (f2) {
        return false;
      }
      var r2, u2 = t.support.isOwnLast;
      for (r2 in n2) if (u2) break;
      return r2 === i || n2.hasOwnProperty(r2);
    };
    f = false;
    t.util.valueFunction = function(n2) {
      return function(r2, u2) {
        var e2 = t.getObject(n2, this.model);
        if (f === false && (f = t.getObject("observables.getValue", t.widget)), r2 === i) return t.isNullOrUndefined(f) ? typeof e2 == "function" ? e2.call(this) : e2 : f(e2, u2);
        typeof e2 == "function" ? (this["ob.values"][n2] = r2, e2.call(this, r2)) : t.createObject(n2, r2, this.model);
      };
    };
    t.util.getVal = function(n2) {
      return typeof n2 == "function" ? n2() : n2;
    };
    t.support = { isOwnLast: function() {
      var n2 = function() {
        this.a = 1;
      }, t2;
      n2.prototype.b = 1;
      for (t2 in new n2()) return t2 === "b";
    }(), outerHTML: function() {
      return document.createElement("div").outerHTML !== i;
    }() };
    o = t.throwError = function(n2) {
      try {
        throw new Error(n2);
      } catch (t2) {
        throw t2.message + "\n" + t2.stack;
      }
    };
    t.getRandomValue = function(n2, r2) {
      var u2, f2;
      return n2 === i || r2 === i ? t.throwError("Min and Max values are required for generating a random number") : ("crypto" in window && "getRandomValues" in crypto ? (f2 = new Uint16Array(1), window.crypto.getRandomValues(f2), u2 = f2[0] % (r2 - n2) + n2) : u2 = Math.random() * (r2 - n2) + n2, u2 | 0);
    };
    t.extensions = {};
    t.extensions.modelGUID = "{0B1051BA-1CCB-42C2-A3B5-635389B92A50}";
  }(window.jQuery, window.Syncfusion), function() {
    $.fn.addEleAttrs = function(n2) {
      var t = $(this);
      $.each(n2, function(n3, i) {
        i && i.specified && t.attr(i.name, i.value);
      });
    };
    $.fn.removeEleAttrs = function(n2) {
      return this.each(function() {
        var t = $(this), i = $(this.attributes).clone();
        $.each(i, function(i2, r) {
          r && r.specified && n2.test(r.name) && t.removeAttr(r.name);
        });
      });
    };
    $.fn.attrNotStartsWith = function(n2) {
      var u = this, r = [], i, t;
      for (this.each(function() {
        i = $(this.attributes).clone();
      }), t = 0; t < i.length; t++) if (i[t] && i[t].specified && n2.test(i[t].name)) continue;
      else r.push(i[t]);
      return r;
    };
    $.fn.removeEleEmptyAttrs = function() {
      return this.each(function() {
        var n2 = $(this), t = $(this.attributes).clone();
        $.each(t, function(t2, i) {
          i && i.specified && i.value === "" && n2.removeAttr(i.name);
        });
      });
    };
    $.extend($.support, { has3d: ej.addPrefix("perspective") in ej.styles, hasTouch: "ontouchstart" in window, hasPointer: navigator.msPointerEnabled, hasTransform: ej.userAgent() !== false, pushstate: "pushState" in history && "replaceState" in history, hasTransition: ej.addPrefix("transition") in ej.styles });
    $.extend($.expr[":"], { attrNotStartsWith: function(n2, t, i) {
      for (var u = n2.attributes, r = 0; r < u.length; r++) if (u[r].nodeName.indexOf(i[3]) === 0) return false;
      return true;
    } });
    var n = $.fn.andSelf || $.fn.addBack;
    $.fn.andSelf = $.fn.addBack = function() {
      return n.apply(this, arguments);
    };
  }();
  window.ej = window.Syncfusion = window.Syncfusion || {}, function(n, t, i, r) {
    "use strict";
    var e, nt, tt, y, d, w, h, c, f;
    t.DataManager = function(i2, u2, f2) {
      if (!(this instanceof t.DataManager)) return new t.DataManager(i2, u2, f2);
      i2 || (i2 = []);
      f2 = f2 || i2.adaptor;
      typeof f2 == "string" && (f2 = new t[f2]());
      var e2 = [], o2 = this;
      return i2 instanceof Array ? e2 = { json: i2, offline: true } : typeof i2 == "object" ? n.isPlainObject(i2) ? (i2.json || (i2.json = []), i2.table && (i2.json = this._getJsonFromElement(i2.table, i2.headerOption)), e2 = { url: i2.url, insertUrl: i2.insertUrl, removeUrl: i2.removeUrl, updateUrl: i2.updateUrl, crudUrl: i2.crudUrl, batchUrl: i2.batchUrl, json: i2.json, headers: i2.headers, accept: i2.accept, data: i2.data, async: i2.async, timeTillExpiration: i2.timeTillExpiration, cachingPageSize: i2.cachingPageSize, enableCaching: i2.enableCaching, requestType: i2.requestType, key: i2.key, crossDomain: i2.crossDomain, antiForgery: i2.antiForgery, jsonp: i2.jsonp, dataType: i2.dataType, enableAjaxCache: i2.enableAjaxCache, offline: i2.offline !== r ? i2.offline : i2.adaptor == "remoteSaveAdaptor" || i2.adaptor instanceof t.remoteSaveAdaptor ? false : i2.url ? false : true, requiresFormat: i2.requiresFormat }) : (i2.jquery || pt(i2)) && (e2 = { json: this._getJsonFromElement(i2), offline: true, table: i2 }) : typeof i2 == "string" && (e2 = { url: i2, offline: false, dataType: "json", json: [] }), e2.requiresFormat !== r || t.support.cors || (e2.requiresFormat = c(e2.crossDomain) ? true : e2.crossDomain), e2.antiForgery && this.antiForgeryToken(), e2.dataType === r && (e2.dataType = "json"), this.dataSource = e2, this.defaultQuery = u2, e2.url && e2.offline && !e2.json.length ? (this.isDataAvailable = false, this.adaptor = f2 || new t.ODataAdaptor(), this.dataSource.offline = false, this.ready = this.executeQuery(u2 || t.Query()).done(function(n2) {
        o2.dataSource.offline = true;
        o2.isDataAvailable = true;
        e2.json = n2.result;
        o2.adaptor = new t.JsonAdaptor();
      })) : this.adaptor = e2.offline ? new t.JsonAdaptor() : new t.ODataAdaptor(), !e2.jsonp && this.adaptor instanceof t.ODataAdaptor && (e2.jsonp = "callback"), this.adaptor = f2 || this.adaptor, e2.enableCaching && (this.adaptor = new t.CacheAdaptor(this.adaptor, e2.timeTillExpiration, e2.cachingPageSize)), this;
    };
    t.DataManager.prototype = { setDefaultQuery: function(n2) {
      this.defaultQuery = n2;
    }, executeQuery: function(i2, u2, e2, o2) {
      var s2, h2, c2;
      return typeof i2 == "function" && (o2 = e2, e2 = u2, u2 = i2, i2 = null), i2 || (i2 = this.defaultQuery), i2 instanceof t.Query || f("DataManager - executeQuery() : A query is required to execute"), s2 = n.Deferred(), s2.then(u2, e2, o2), h2 = { query: i2 }, this.dataSource.offline || this.dataSource.url == r ? t.isNullOrUndefined(this.dataSource.async) || this.dataSource.async != false ? d(function() {
        this._localQueryProcess(i2, h2, s2);
      }, this) : this._localQueryProcess(i2, h2, s2) : (c2 = this.adaptor.processQuery(this, i2), t.isNullOrUndefined(c2.url) ? d(function() {
        h2 = this._getDeferedArgs(i2, c2, h2);
        s2.resolveWith(this, [h2]);
      }, this) : this._makeRequest(c2, s2, h2, i2)), s2.promise();
    }, _localQueryProcess: function(n2, t2, i2) {
      var r2 = this.executeLocal(n2);
      t2 = this._getDeferedArgs(n2, r2, t2);
      i2.resolveWith(this, [t2]);
    }, _getDeferedArgs: function(n2, t2, i2) {
      return n2._requiresCount ? (i2.result = t2.result, i2.count = t2.count) : i2.result = t2, i2.getTableModel = rt(n2._fromTable, i2.result, this), i2.getKnockoutModel = ut(i2.result), i2;
    }, executeLocal: function(i2) {
      var e2, r2;
      if (this.defaultQuery || i2 instanceof t.Query || f("DataManager - executeLocal() : A query is required to execute"), this.dataSource.json || f("DataManager - executeLocal() : Json data is required to execute"), i2 = i2 || this.defaultQuery, e2 = this.adaptor.processQuery(this, i2), i2._subQuery) {
        var o2 = i2._subQuery._fromTable, s2 = i2._subQuery._lookup, u2 = i2._requiresCount ? e2.result : e2;
        for (s2 && s2 instanceof Array && k(i2._subQuery._fKey, o2, u2, s2, i2._subQuery._key), r2 = 0; r2 < u2.length; r2++) u2[r2][o2] instanceof Array && (u2[r2] = n.extend({}, u2[r2]), u2[r2][o2] = this.adaptor.processResponse(i2._subQuery.using(t.DataManager(u2[r2][o2].slice(0))).executeLocal(), this, i2));
      }
      return this.adaptor.processResponse(e2, this, i2);
    }, _makeRequest: function(i2, r2, u2, f2) {
      var o2 = !!f2._subQuerySelector, c2 = h(function(n2) {
        u2.error = n2;
        r2.rejectWith(this, [u2]);
      }, this), l2 = h(function(n2, t2, i3, e3, s3, h2, c3) {
        o2 || (u2.xhr = i3, u2.count = parseInt(t2, 10), u2.result = n2, u2.request = e3, u2.aggregates = h2, u2.getTableModel = rt(f2._fromTable, n2, this), u2.getKnockoutModel = ut(n2), u2.actual = s3, u2.virtualSelectRecords = c3, r2.resolveWith(this, [u2]));
      }, this), a2 = h(function(t2, i3) {
        var r3 = n.Deferred(), h2 = { parent: u2 }, e3, s3;
        return f2._subQuery._isChild = true, e3 = this.adaptor.processQuery(this, f2._subQuery, t2 ? this.adaptor.processResponse(t2) : i3), s3 = this._makeRequest(e3, r3, h2, f2._subQuery), o2 || r3.then(function(n2) {
          t2 && (k(f2._subQuery._fKey, f2._subQuery._fromTable, t2, n2, f2._subQuery._key), l2(t2));
        }, c2), s3;
      }, this), v2 = w(function(n2, i3, r3, u3) {
        r3.getResponseHeader("Content-Type").indexOf("xml") == -1 && t.dateParse && (n2 = t.parseJSON(n2));
        var e3 = this.adaptor.processResponse(n2, this, f2, r3, u3), s3 = 0, h2 = null, c3 = n2.virtualSelectRecords;
        if (f2._requiresCount && (s3 = e3.count, h2 = e3.aggregates, e3 = e3.result), !f2._subQuery) {
          l2(e3, s3, r3, u3, n2, h2, c3);
          return;
        }
        o2 || a2(e3);
      }, this), e2 = n.extend({ type: "GET", dataType: this.dataSource.dataType, crossDomain: this.dataSource.crossDomain, jsonp: this.dataSource.jsonp, cache: t.isNullOrUndefined(this.dataSource.enableAjaxCache) ? true : this.dataSource.enableAjaxCache, beforeSend: h(this._beforeSend, this), processData: false, success: v2, error: c2 }, i2), s2;
      return "async" in this.dataSource && (e2.async = this.dataSource.async), e2 = n.ajax(e2), o2 && (s2 = f2._subQuerySelector.call(this, { query: f2._subQuery, parent: f2 }), s2 && s2.length ? (e2 = n.when(e2, a2(null, s2)), e2.then(w(function(n2, t2, i3) {
        var r3 = this.adaptor.processResponse(n2[0], this, f2, n2[2], i3[0]), e3 = 0, u3;
        f2._requiresCount && (e3 = r3.count, r3 = r3.result);
        u3 = this.adaptor.processResponse(t2[0], this, f2._subQuery, t2[2], i3[1]);
        e3 = 0;
        f2._subQuery._requiresCount && (e3 = u3.count, u3 = u3.result);
        k(f2._subQuery._fKey, f2._subQuery._fromTable, r3, u3, f2._subQuery._key);
        o2 = false;
        l2(r3, e3, n2[2]);
      }, this), c2)) : o2 = false), e2;
    }, _beforeSend: function(n2, t2) {
      var i2, f2, r2, u2;
      for (this.adaptor.beforeSend(this, n2, t2), i2 = this.dataSource.headers, r2 = 0; i2 && r2 < i2.length; r2++) {
        f2 = [];
        for (u2 in i2[r2]) f2.push(u2), n2.setRequestHeader(u2, i2[r2][u2]);
      }
    }, saveChanges: function(i2, r2, u2, f2) {
      var s2, o2, e2;
      return (u2 instanceof t.Query && (f2 = u2, u2 = null), s2 = { url: u2, key: r2 || this.dataSource.key }, o2 = this.adaptor.batchRequest(this, i2, s2, f2), this.dataSource.offline) ? o2 : (e2 = n.Deferred(), n.ajax(n.extend({ beforeSend: h(this._beforeSend, this), success: w(function(n2, t2, u3, f3) {
        e2.resolveWith(this, [this.adaptor.processResponse(n2, this, null, u3, f3, i2, r2)]);
      }, this), error: function(n2) {
        e2.rejectWith(this, [{ error: n2 }]);
      } }, o2)), e2.promise());
    }, insert: function(i2, r2, f2) {
      var o2, e2;
      return (i2 = u.replacer(i2, true), r2 instanceof t.Query && (f2 = r2, r2 = null), o2 = this.adaptor.insert(this, i2, r2, f2), this.dataSource.offline) ? o2 : (e2 = n.Deferred(), n.ajax(n.extend({ type: "POST", contentType: "application/json; charset=utf-8", processData: false, beforeSend: h(this._beforeSend, this), success: w(function(n2, i3, r3, f3) {
        try {
          t.isNullOrUndefined(n2) ? n2 = [] : u.parseJson(n2);
        } catch (o3) {
          n2 = [];
        }
        n2 = this.adaptor.processResponse(u.parseJson(n2), this, null, r3, f3);
        e2.resolveWith(this, [{ record: n2, dataManager: this }]);
      }, this), error: function(n2) {
        e2.rejectWith(this, [{ error: n2, dataManager: this }]);
      } }, o2)), e2.promise());
    }, antiForgeryToken: function() {
      var i2;
      return t.isNullOrUndefined(n("input[name='_ejRequestVerifyToken']").val()) ? i2 = t.buildTag("input", "", "", { type: "hidden", name: "_ejRequestVerifyToken", value: t.getGuid() }).appendTo("body") : n("input[name='_ejRequestVerifyToken']").val(t.getGuid()), t.cookie.set("_ejRequestVerifyToken", n("input[name='_ejRequestVerifyToken']").val()), { name: "_ejRequestVerifyToken", value: n("input[name='_ejRequestVerifyToken']").val() };
    }, remove: function(i2, r2, f2, e2) {
      var s2, o2;
      return (typeof r2 == "object" && (r2 = r2[i2]), f2 instanceof t.Query && (e2 = f2, f2 = null), s2 = this.adaptor.remove(this, i2, r2, f2, e2), this.dataSource.offline) ? s2 : (o2 = n.Deferred(), n.ajax(n.extend({ type: "POST", contentType: "application/json; charset=utf-8", beforeSend: h(this._beforeSend, this), success: w(function(n2, i3, r3, f3) {
        try {
          t.isNullOrUndefined(n2) ? n2 = [] : u.parseJson(n2);
        } catch (e3) {
          n2 = [];
        }
        n2 = this.adaptor.processResponse(u.parseJson(n2), this, null, r3, f3);
        o2.resolveWith(this, [{ record: n2, dataManager: this }]);
      }, this), error: function(n2) {
        o2.rejectWith(this, [{ error: n2, dataManager: this }]);
      } }, s2)), o2.promise());
    }, update: function(i2, r2, f2, e2) {
      var s2, o2;
      return (r2 = u.replacer(r2, true), f2 instanceof t.Query && (e2 = f2, f2 = null), s2 = this.adaptor.update(this, i2, r2, f2, e2), this.dataSource.offline) ? s2 : (o2 = n.Deferred(), n.ajax(n.extend({ contentType: "application/json; charset=utf-8", beforeSend: h(this._beforeSend, this), success: w(function(n2, i3, r3, f3) {
        try {
          t.isNullOrUndefined(n2) ? n2 = [] : u.parseJson(n2);
        } catch (e3) {
          n2 = [];
        }
        n2 = this.adaptor.processResponse(u.parseJson(n2), this, null, r3, f3);
        o2.resolveWith(this, [{ record: n2, dataManager: this }]);
      }, this), error: function(n2) {
        o2.rejectWith(this, [{ error: n2, dataManager: this }]);
      } }, s2)), o2.promise());
    }, _getJsonFromElement: function(i2) {
      typeof i2 == "string" && (i2 = n(n(i2).html()));
      i2 = i2.jquery ? i2[0] : i2;
      var r2 = i2.tagName.toLowerCase();
      return r2 !== "table" && f("ej.DataManager : Unsupported htmlElement : " + r2), t.parseTable(i2);
    } };
    var k = function(n2, i2, r2, u2, e2) {
      var o2, s2 = {}, c2, h2;
      for (u2.result && (u2 = u2.result), u2.GROUPGUID && f("ej.DataManager: Do not have support Grouping in hierarchy"), o2 = 0; o2 < u2.length; o2++) h2 = t.getObject(n2, u2[o2]), c2 = s2[h2] || (s2[h2] = []), c2.push(u2[o2]);
      for (o2 = 0; o2 < r2.length; o2++) r2[o2][i2] = s2[t.getObject(e2 || n2, r2[o2])];
    }, l = { accept: "application/json;odata=light;q=1,application/json;odata=verbose;q=0.5", multipartAccept: "multipart/mixed", batch: "$batch", changeSet: "--changeset_", batchPre: "batch_", contentId: "Content-Id: ", batchContent: "Content-Type: multipart/mixed; boundary=", changeSetContent: "Content-Type: application/http\nContent-Transfer-Encoding: binary ", batchChangeSetContentType: "Content-Type: application/json; charset=utf-8 " }, u = { parseJson: function(n2) {
      var t2 = typeof n2;
      return t2 === "string" ? n2 = JSON.parse(n2, u.jsonReviver) : n2 instanceof Array ? u.iterateAndReviveArray(n2) : t2 === "object" && u.iterateAndReviveJson(n2), n2;
    }, iterateAndReviveArray: function(n2) {
      for (var t2 = 0; t2 < n2.length; t2++) typeof n2[t2] == "object" ? u.iterateAndReviveJson(n2[t2]) : n2[t2] = typeof n2[t2] != "string" || /^[\s]*\[|^[\s]*\{|\"/g.test(n2[t2]) ? u.parseJson(n2[t2]) : u.jsonReviver("", n2[t2]);
    }, iterateAndReviveJson: function(n2) {
      var t2;
      for (var i2 in n2) i2.startsWith("__") || (t2 = n2[i2], typeof t2 == "object" ? t2 instanceof Array ? u.iterateAndReviveArray(t2) : u.iterateAndReviveJson(t2) : n2[i2] = u.jsonReviver(i2, t2));
    }, jsonReviver: function(n2, i2) {
      var o2 = i2, e2 = /[\-,/,\,.,:,]+/, f2, r2;
      if (typeof i2 == "string") {
        if (f2 = /^\/Date\(([+-]?[0-9]+)([+-][0-9]{4})?\)\/$/.exec(i2), f2) return t.parseDateInUTC ? u.isValidDate(f2[0]) : u.replacer(new Date(parseInt(f2[1])));
        (t.dateParse ? /^(?:(\d{4}\-\d\d\-\d\d)|(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?)$/.test(i2) : /^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?$/.test(i2)) && (r2 = o2.split(/[^0-9]/), /^(\d{4}\-\d\d\-\d\d)$/.test(i2) ? i2 = new Date(r2[t.dateFormat.split(e2).indexOf("yyyy")], r2[t.dateFormat.split(e2).indexOf("MM")] - 1, r2[t.dateFormat.split(e2).indexOf("dd")]) : (i2 = t.parseDateInUTC ? u.isValidDate(i2) : u.replacer(new Date(i2)), isNaN(i2) && (i2 = u.replacer(new Date(r2[0], r2[1] - 1, r2[2], r2[3], r2[4], r2[5])))));
      }
      return i2;
    }, isValidDate: function(n2) {
      var t2 = n2, i2;
      return (typeof t2 == "string" && t2.indexOf("/Date(") == 0 && (n2 = t2.replace(/\d+/, function(n3) {
        var t3 = new Date(parseInt(n3)).getTimezoneOffset() * 6e4, i3 = parseInt(n3) + t3;
        return u.replacer(new Date(parseInt(i3)));
      })), typeof n2 == "string") ? (n2 = n2.replace("/Date(", function() {
        return "";
      }), n2 = n2.replace(")/", function() {
        return "";
      }), i2 = new Date(n2) instanceof Date, i2 ? new Date(n2) : n2) : n2;
    }, isJson: function(n2) {
      return typeof n2[0] == "string" ? n2 : t.parseJSON(n2);
    }, isGuid: function(n2) {
      var t2 = /[A-Fa-f0-9]{8}(?:-[A-Fa-f0-9]{4}){3}-[A-Fa-f0-9]{12}/i.exec(n2);
      return t2 != null;
    }, replacer: function(n2, i2) {
      return t.isPlainObject(n2) ? u.jsonReplacer(n2, i2) : n2 instanceof Array ? u.arrayReplacer(n2) : n2 instanceof Date ? u.jsonReplacer({ val: n2 }, i2).val : n2;
    }, jsonReplacer: function(n2, i2) {
      var r2, u2, f2;
      for (u2 in n2) (r2 = n2[u2], r2 instanceof Date) && (f2 = t.serverTimezoneOffset * 36e5 * (t.isNullOrUndefined(i2) || i2 === false ? 1 : -1), n2[u2] = new Date(+r2 + f2));
      return n2;
    }, arrayReplacer: function(n2) {
      for (var i2 = 0; i2 < n2.length; i2++) t.isPlainObject(n2[i2]) ? n2[i2] = u.jsonReplacer(n2[i2]) : n2[i2] instanceof Date && (n2[i2] = u.jsonReplacer({ date: n2[i2] }).date);
      return n2;
    } };
    t.isJSON = u.isJson;
    t.parseJSON = u.parseJson;
    t.dateParse = true;
    t.dateFormat = "yyyy-MM-dd";
    t.isGUID = u.isGuid;
    t.Query = function(n2) {
      return this instanceof t.Query ? (this.queries = [], this._key = "", this._fKey = "", typeof n2 == "string" ? this._fromTable = n2 || "" : n2 && n2 instanceof Array && (this._lookup = n2), this._expands = [], this._sortedColumns = [], this._groupedColumns = [], this._subQuery = null, this._isChild = false, this._params = [], this) : new t.Query(n2);
    };
    t.Query.prototype = { key: function(n2) {
      return typeof n2 == "string" && (this._key = n2), this;
    }, using: function(n2) {
      return n2 instanceof t.DataManager ? (this.dataManagar = n2, this) : f("Query - using() : 'using' function should be called with parameter of instance ej.DataManager");
    }, execute: function(n2, i2, r2, u2) {
      return (n2 = n2 || this.dataManagar, n2 && n2 instanceof t.DataManager) ? n2.executeQuery(this, i2, r2, u2) : f("Query - execute() : dataManager needs to be is set using 'using' function or should be passed as argument");
    }, executeLocal: function(n2) {
      return (n2 = n2 || this.dataManagar, n2 && n2 instanceof t.DataManager) ? n2.executeLocal(this) : f("Query - executeLocal() : dataManager needs to be is set using 'using' function or should be passed as argument");
    }, clone: function() {
      var n2 = new t.Query();
      return n2.queries = this.queries.slice(0), n2._key = this._key, n2._isChild = this._isChild, n2.dataManagar = this.dataManager, n2._fromTable = this._fromTable, n2._params = this._params.slice(0), n2._expands = this._expands.slice(0), n2._sortedColumns = this._sortedColumns.slice(0), n2._groupedColumns = this._groupedColumns.slice(0), n2._subQuerySelector = this._subQuerySelector, n2._subQuery = this._subQuery, n2._fKey = this._fKey, n2._requiresCount = this._requiresCount, n2;
    }, from: function(n2) {
      return typeof n2 == "string" && (this._fromTable = n2), this;
    }, addParams: function(n2, i2) {
      return typeof i2 == "function" || t.isPlainObject(i2) ? typeof i2 == "function" && this._params.push({ key: n2, fn: i2 }) : this._params.push({ key: n2, value: i2 }), this;
    }, expand: function(n2) {
      return this._expands = typeof n2 == "string" ? [].slice.call(arguments, 0) : n2.slice(0), this;
    }, where: function(n2, i2, r2, u2, e2) {
      i2 = (i2 || t.FilterOperators.equal).toLowerCase();
      var o2 = null;
      return typeof n2 == "string" ? o2 = new t.Predicate(n2, i2, r2, u2, e2) : n2 instanceof t.Predicate ? o2 = n2 : f("Query - where : Invalid arguments"), this.queries.push({ fn: "onWhere", e: o2 }), this;
    }, search: function(n2, i2, r2, u2, f2) {
      i2 && typeof i2 != "boolean" ? typeof i2 == "string" && (i2 = [i2]) : (i2 = [], u2 = i2);
      typeof r2 == "boolean" && (u2 = r2, r2 = null);
      r2 = r2 || t.FilterOperators.contains;
      r2.length < 3 && (r2 = t.data.operatorSymbols[r2]);
      var e2 = t.data.fnOperators[r2] || t.data.fnOperators.processSymbols(r2);
      return this.queries.push({ fn: "onSearch", e: { fieldNames: i2, operator: r2, searchKey: n2, ignoreCase: u2, ignoreAccent: f2, comparer: e2 } }), this;
    }, sortBy: function(n2, i2, r2) {
      var o2 = t.sortOrder.Ascending, s2, f2, u2, e2;
      if (typeof n2 == "string" && n2.toLowerCase().endsWith(" desc") && (n2 = n2.replace(/ desc$/i, ""), i2 = t.sortOrder.Descending), n2 instanceof Array) {
        for (u2 = 0; u2 < n2.length; u2++) this.sortBy(n2[u2], i2, r2);
        return this;
      }
      if (typeof i2 == "boolean" ? i2 = i2 ? t.sortOrder.Descending : t.sortOrder.Ascending : typeof i2 == "function" && (o2 = "custom"), i2 && typeof i2 != "string" || (o2 = i2 ? i2.toLowerCase() : t.sortOrder.Ascending, i2 = t.pvt.fnSort(i2)), r2) {
        for (s2 = a(this.queries, "onSortBy"), u2 = 0; u2 < s2.length; u2++) if (f2 = s2[u2].e.fieldName, typeof f2 == "string") {
          if (f2 === n2) return this;
        } else if (f2 instanceof Array) {
          for (e2 = 0; e2 < f2.length; e2++) if (f2[e2] === n2 || n2.toLowerCase() === f2[e2] + " desc") return this;
        }
      }
      return this.queries.push({ fn: "onSortBy", e: { fieldName: n2, comparer: i2, direction: o2 } }), this;
    }, sortByDesc: function(n2) {
      return this.sortBy(n2, t.sortOrder.Descending);
    }, group: function(n2, t2) {
      return this.sortBy(n2, null, true), this.queries.push({ fn: "onGroup", e: { fieldName: n2, fn: t2 } }), this;
    }, page: function(n2, t2) {
      return this.queries.push({ fn: "onPage", e: { pageIndex: n2, pageSize: t2 } }), this;
    }, range: function(n2, t2) {
      return (typeof n2 != "number" || typeof t2 != "number") && f("Query() - range : Arguments type should be a number"), this.queries.push({ fn: "onRange", e: { start: n2, end: t2 } }), this;
    }, take: function(n2) {
      return typeof n2 != "number" && f("Query() - Take : Argument type should be a number"), this.queries.push({ fn: "onTake", e: { nos: n2 } }), this;
    }, skip: function(n2) {
      return typeof n2 != "number" && f("Query() - Skip : Argument type should be a number"), this.queries.push({ fn: "onSkip", e: { nos: n2 } }), this;
    }, select: function(n2) {
      return typeof n2 == "string" && (n2 = [].slice.call(arguments, 0)), n2 instanceof Array || f("Query() - Select : Argument type should be String or Array"), this.queries.push({ fn: "onSelect", e: { fieldNames: n2 } }), this;
    }, hierarchy: function(n2, i2) {
      return n2 && n2 instanceof t.Query || f("Query() - hierarchy : query must be instance of ej.Query"), typeof i2 == "function" && (this._subQuerySelector = i2), this._subQuery = n2, this;
    }, foreignKey: function(n2) {
      return typeof n2 == "string" && (this._fKey = n2), this;
    }, requiresCount: function() {
      return this._requiresCount = true, this;
    }, aggregate: function(n2, t2) {
      this.queries.push({ fn: "onAggregates", e: { field: t2, type: n2 } });
    } };
    t.Adaptor = function(n2) {
      this.dataSource = n2;
      this.pvt = {};
      this.init.apply(this, [].slice.call(arguments, 1));
    };
    t.Adaptor.prototype = { options: { from: "table", requestType: "json", sortBy: "sorted", select: "select", skip: "skip", group: "group", take: "take", search: "search", count: "requiresCounts", where: "where", aggregates: "aggregates", antiForgery: "antiForgery" }, init: function() {
    }, extend: function(t2) {
      var i2 = function(t3) {
        this.dataSource = t3;
        this.options && (this.options = n.extend({}, this.options));
        this.init.apply(this, [].slice.call(arguments, 0));
        this.pvt = {};
      }, u2, r2;
      i2.prototype = new this.type();
      i2.prototype.type = i2;
      u2 = i2.prototype.base = {};
      for (r2 in t2) i2.prototype[r2] && (u2[r2] = i2.prototype[r2]);
      return n.extend(true, i2.prototype, t2), i2;
    }, processQuery: function() {
    }, processResponse: function(n2) {
      return n2.d ? n2.d : n2;
    }, convertToQueryString: function(t2) {
      return n.param(t2);
    }, type: t.Adaptor };
    t.UrlAdaptor = new t.Adaptor().extend({ processQuery: function(n2, t2, i2) {
      var w2 = a(t2.queries, "onSortBy"), d2 = a(t2.queries, "onGroup"), nt2 = a(t2.queries, "onWhere"), tt2 = a(t2.queries, "onSearch"), it2 = a(t2.queries, "onAggregates"), l2 = g(t2.queries, ["onSelect", "onPage", "onSkip", "onTake", "onRange"]), rt2 = t2._params, b2 = n2.dataSource.url, u2, y2, p2 = null, h2 = this.options, s2 = { sorted: [], grouped: [], filters: [], searches: [], aggregates: [] }, f2, r2, v2, k2;
      for (l2.onPage ? (u2 = l2.onPage, y2 = e(u2.pageIndex, t2), p2 = e(u2.pageSize, t2), y2 = (y2 - 1) * p2) : l2.onRange && (u2 = l2.onRange, y2 = u2.start, p2 = u2.end - u2.start), f2 = 0; f2 < w2.length; f2++) u2 = e(w2[f2].e.fieldName, t2), s2.sorted.push(o(this, "onEachSort", { name: u2, direction: w2[f2].e.direction }, t2));
      for (i2 && (u2 = this.getFiltersFrom(i2, t2), u2 && s2.filters.push(o(this, "onEachWhere", u2.toJSON(), t2))), f2 = 0; f2 < nt2.length; f2++) {
        s2.filters.push(o(this, "onEachWhere", nt2[f2].e.toJSON(), t2));
        for (v2 in s2.filters[f2]) c(s2[v2]) && delete s2[v2];
      }
      for (f2 = 0; f2 < tt2.length; f2++) u2 = tt2[f2].e, s2.searches.push(o(this, "onEachSearch", { fields: u2.fieldNames, operator: u2.operator, key: u2.searchKey, ignoreCase: u2.ignoreCase }, t2));
      for (f2 = 0; f2 < d2.length; f2++) s2.grouped.push(e(d2[f2].e.fieldName, t2));
      for (f2 = 0; f2 < it2.length; f2++) u2 = it2[f2].e, s2.aggregates.push({ type: u2.type, field: e(u2.field, t2) });
      r2 = {};
      r2[h2.from] = t2._fromTable;
      h2.expand && (r2[h2.expand] = t2._expands);
      r2[h2.select] = l2.onSelect ? o(this, "onSelect", e(l2.onSelect.fieldNames, t2), t2) : "";
      r2[h2.count] = t2._requiresCount ? o(this, "onCount", t2._requiresCount, t2) : "";
      r2[h2.search] = s2.searches.length ? o(this, "onSearch", s2.searches, t2) : "";
      r2[h2.skip] = l2.onSkip ? o(this, "onSkip", e(l2.onSkip.nos, t2), t2) : "";
      r2[h2.take] = l2.onTake ? o(this, "onTake", e(l2.onTake.nos, t2), t2) : "";
      r2[h2.antiForgery] = n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "";
      r2[h2.where] = s2.filters.length || s2.searches.length ? o(this, "onWhere", s2.filters, t2) : "";
      r2[h2.sortBy] = s2.sorted.length ? o(this, "onSortBy", s2.sorted, t2) : "";
      r2[h2.group] = s2.grouped.length ? o(this, "onGroup", s2.grouped, t2) : "";
      r2[h2.aggregates] = s2.aggregates.length ? o(this, "onAggregates", s2.aggregates, t2) : "";
      r2.param = [];
      o(this, "addParams", { dm: n2, query: t2, params: rt2, reqParams: r2 });
      for (v2 in r2) (c(r2[v2]) || r2[v2] === "" || r2[v2].length === 0 || v2 === "params") && delete r2[v2];
      return (h2.skip in r2 && h2.take in r2 || p2 === null || (r2[h2.skip] = o(this, "onSkip", y2, t2), r2[h2.take] = o(this, "onTake", p2, t2)), k2 = this.pvt, this.pvt = {}, this.options.requestType === "json") ? { data: JSON.stringify(r2), url: b2, ejPvtData: k2, type: "POST", contentType: "application/json; charset=utf-8" } : (u2 = this.convertToQueryString(r2, t2, n2), u2 = (n2.dataSource.url.indexOf("?") !== -1 ? "&" : "/") + u2, { type: "GET", url: u2.length ? b2.replace(/\/*$/, u2) : b2, ejPvtData: k2 });
    }, convertToQueryString: function(t2, i2, r2) {
      return r2.dataSource.url && r2.dataSource.url.indexOf("?") !== -1 ? n.param(t2) : "?" + n.param(t2);
    }, processResponse: function(n2, i2, u2, f2, e2, o2) {
      var c2 = e2.ejPvtData || {}, v2 = n2.groupDs, y2, a2, h2, s2, d2, w2;
      if (f2 && f2.getResponseHeader("Content-Type") && f2.getResponseHeader("Content-Type").indexOf("xml") != -1 && n2.nodeType == 9) return u2._requiresCount ? { result: [], count: 0 } : [];
      if (y2 = JSON.parse(e2.data), y2 && y2.action === "batch" && n2.added) return o2.added = n2.added, o2;
      if (n2.d && (n2 = n2.d), c2 && c2.aggregates && c2.aggregates.length) {
        var l2 = c2.aggregates, h2 = {}, p2, k2 = {};
        for (("count" in n2) && (h2.count = n2.count), n2.result && (h2.result = n2.result), n2.aggregate && (n2 = n2.aggregate), s2 = 0; s2 < l2.length; s2++) p2 = t.aggregates[l2[s2].type], p2 && (k2[l2[s2].field + " - " + l2[s2].type] = p2(n2, l2[s2].field));
        h2.aggregates = k2;
        n2 = h2;
      }
      if (c2 && c2.groups && c2.groups.length) {
        for (a2 = c2.groups, h2 = {}, ("count" in n2) && (h2.count = n2.count), n2.aggregates && (h2.aggregates = n2.aggregates), n2.result && (n2 = n2.result), s2 = 0; s2 < a2.length; s2++) d2 = null, w2 = b(a2[s2], u2.queries), t.isNullOrUndefined(v2) || (v2 = t.group(v2, a2[s2], null, w2)), n2 = t.group(n2, a2[s2], c2.aggregates, w2, d2, v2);
        return h2.count != r ? h2.result = n2 : h2 = n2, h2;
      }
      return n2;
    }, onGroup: function(n2) {
      this.pvt.groups = n2;
    }, onAggregates: function(n2) {
      this.pvt.aggregates = n2;
    }, batchRequest: function(n2, t2, i2, r2) {
      var u2 = { changed: t2.changed, added: t2.added, deleted: t2.deleted, action: "batch", table: i2.url, key: i2.key, antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" };
      return r2 && this.addParams({ dm: n2, query: r2, params: r2._params, reqParams: u2 }), { type: "POST", url: n2.dataSource.batchUrl || n2.dataSource.crudUrl || n2.dataSource.removeUrl || n2.dataSource.url, contentType: "application/json; charset=utf-8", dataType: "json", data: JSON.stringify(u2) };
    }, beforeSend: function() {
    }, insert: function(n2, t2, i2, r2) {
      var u2 = { value: t2, table: i2, action: "insert", antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" };
      return r2 && this.addParams({ dm: n2, query: r2, params: r2._params, reqParams: u2 }), { url: n2.dataSource.insertUrl || n2.dataSource.crudUrl || n2.dataSource.url, data: JSON.stringify(u2) };
    }, remove: function(n2, t2, i2, r2, u2) {
      var f2 = { key: i2, keyColumn: t2, table: r2, action: "remove", antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" };
      return u2 && this.addParams({ dm: n2, query: u2, params: u2._params, reqParams: f2 }), { type: "POST", url: n2.dataSource.removeUrl || n2.dataSource.crudUrl || n2.dataSource.url, data: JSON.stringify(f2) };
    }, update: function(n2, t2, i2, r2, u2) {
      var f2 = { value: i2, action: "update", keyColumn: t2, key: i2[t2], table: r2, antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" };
      return u2 && this.addParams({ dm: n2, query: u2, params: u2._params, reqParams: f2 }), { type: "POST", url: n2.dataSource.updateUrl || n2.dataSource.crudUrl || n2.dataSource.url, data: JSON.stringify(f2) };
    }, getFiltersFrom: function(n2, i2) {
      var r2;
      n2 instanceof Array && n2.length || f("ej.SubQuery: Array of key values required");
      var u2 = i2._fKey, e2, o2 = u2, h2 = i2._key, s2 = [], c2 = typeof n2[0] != "object";
      for (typeof n2[0] != "object" && (o2 = null), r2 = 0; r2 < n2.length; r2++) e2 = c2 ? n2[r2] : t.pvt.getObject(h2 || o2, n2[r2]), s2.push(new t.Predicate(u2, "==", e2));
      return t.Predicate.or(s2);
    }, addParams: function(n2) {
      var e2 = n2.dm, u2 = n2.query, o2 = n2.params, i2 = n2.reqParams, r2, t2;
      for (i2.params = {}, r2 = 0; t2 = o2[r2]; r2++) i2[t2.key] && f("ej.Query: Custom Param is conflicting other request arguments"), i2[t2.key] = t2.value, t2.fn && (i2[t2.key] = t2.fn.call(u2, t2.key, u2, e2)), i2.params[t2.key] = i2[t2.key];
    } });
    t.WebMethodAdaptor = new t.UrlAdaptor().extend({ processQuery: function(n2, i2, r2) {
      var u2 = t.UrlAdaptor.prototype.processQuery(n2, i2, r2), e2 = t.parseJSON(u2.data), f2 = {};
      return f2.value = e2, o(this, "addParams", { dm: n2, query: i2, params: i2._params, reqParams: f2 }), { data: JSON.stringify(f2), url: u2.url, ejPvtData: u2.ejPvtData, type: "POST", contentType: "application/json; charset=utf-8" };
    }, addParams: function(n2) {
      var s2 = n2.dm, o2 = n2.query, h2 = n2.params, i2 = n2.reqParams, u2, t2, r2, e2;
      for (i2.params = {}, u2 = 0; t2 = h2[u2]; u2++) i2[t2.key] && f("ej.Query: Custom Param is conflicting other request arguments"), r2 = t2.key, e2 = t2.value, t2.fn && (e2 = t2.fn.call(o2, t2.key, o2, s2)), i2[r2] = e2, i2.params[r2] = i2[r2];
    } });
    t.CacheAdaptor = new t.UrlAdaptor().extend({ init: function(n2, i2, r2) {
      var f2, u2;
      t.isNullOrUndefined(n2) || (this.cacheAdaptor = n2);
      this.pageSize = r2;
      this.guidId = t.getGuid("cacheAdaptor");
      f2 = { keys: [], results: [] };
      window.localStorage && window.localStorage.setItem(this.guidId, JSON.stringify(f2));
      u2 = this.guidId;
      t.isNullOrUndefined(i2) || setInterval(function() {
        for (var e2, r3 = t.parseJSON(window.localStorage.getItem(u2)), f3 = [], n3 = 0; n3 < r3.results.length; n3++) r3.results[n3].timeStamp = /* @__PURE__ */ new Date() - new Date(r3.results[n3].timeStamp), /* @__PURE__ */ new Date() - new Date(r3.results[n3].timeStamp) > i2 && f3.push(n3);
        for (e2 = f3, n3 = 0; n3 < f3.length; n3++) r3.results.splice(f3[n3], 1), r3.keys.splice(f3[n3], 1);
        window.localStorage.removeItem(u2);
        window.localStorage.setItem(u2, JSON.stringify(r3));
      }, i2);
    }, generateKey: function(n2, t2) {
      var h2 = a(t2.queries, "onSortBy"), c2 = a(t2.queries, "onGroup"), o2 = a(t2.queries, "onWhere"), l2 = a(t2.queries, "onSearch"), v2 = a(t2.queries, "onPage"), s2 = g(t2.queries, ["onSelect", "onPage", "onSkip", "onTake", "onRange"]), y2 = t2._params, r2 = n2, u2, i2, f2, e2;
      for (s2.onPage && (r2 += s2.onPage.pageIndex), h2.forEach(function(n3) {
        r2 += n3.e.direction + n3.e.fieldName;
      }), c2.forEach(function(n3) {
        r2 += n3.e.fieldName;
      }), l2.forEach(function(n3) {
        r2 += n3.e.searchKey;
      }), u2 = 0; u2 < o2.length; u2++) if (i2 = o2[u2], i2.e.isComplex) {
        for (f2 = t2.clone(), f2.queries = [], e2 = 0; e2 < i2.e.predicates.length; e2++) f2.queries.push({ fn: "onWhere", e: i2.e.predicates[e2], filter: t2.queries.filter });
        r2 += i2.e.condition + this.generateKey(n2, f2);
      } else r2 += i2.e.field + i2.e.operator + i2.e.value;
      return r2;
    }, processQuery: function(n2, i2) {
      var f2 = this.generateKey(n2.dataSource.url, i2), r2, u2;
      return (window.localStorage && (r2 = t.parseJSON(window.localStorage.getItem(this.guidId))), u2 = r2 ? r2.results[r2.keys.indexOf(f2)] : null, u2 != null && !this._crudAction && !this._insertAction) ? u2 : (this._crudAction = null, this._insertAction = null, this.cacheAdaptor.processQuery.apply(this.cacheAdaptor, [].slice.call(arguments, 0)));
    }, processResponse: function(i2, r2, u2, f2, e2, o2) {
      var h2;
      if (this._insertAction || e2 && this.cacheAdaptor.options.batch && e2.url.endsWith(this.cacheAdaptor.options.batch) && e2.type.toLowerCase() === "post") return this.cacheAdaptor.processResponse(i2, r2, u2, f2, e2, o2);
      var i2 = this.cacheAdaptor.processResponse.apply(this, [].slice.call(arguments, 0)), c2 = this.generateKey(r2.dataSource.url, u2), s2 = {};
      for (window.localStorage && (s2 = t.parseJSON(window.localStorage.getItem(this.guidId))), h2 = n.inArray(c2, s2.keys), h2 != -1 && (s2.results.splice(h2, 1), s2.keys.splice(h2, 1)), s2.results[s2.keys.push(c2) - 1] = { keys: c2, result: i2.result, timeStamp: /* @__PURE__ */ new Date(), count: i2.count }; s2.results.length > this.pageSize; ) s2.results.splice(0, 1), s2.keys.splice(0, 1);
      return window.localStorage.setItem(this.guidId, JSON.stringify(s2)), i2;
    }, update: function(n2, t2, i2, r2) {
      return this._crudAction = true, this.cacheAdaptor.update(n2, t2, i2, r2);
    }, insert: function(n2, t2, i2) {
      return this._insertAction = true, this.cacheAdaptor.insert(n2, t2, i2);
    }, remove: function(n2, t2, i2, r2) {
      return this._crudAction = true, this.cacheAdaptor.remove(n2, t2, i2, r2);
    }, batchRequest: function(n2, t2, i2) {
      return this.cacheAdaptor.batchRequest(n2, t2, i2);
    } });
    var a = function(n2, t2) {
      return n2.filter(function(n3) {
        return n3.fn === t2;
      }) || [];
    }, g = function(n2, t2) {
      for (var r2 = n2.filter(function(n3) {
        return t2.indexOf(n3.fn) !== -1;
      }), u2 = {}, i2 = 0; i2 < r2.length; i2++) u2[r2[i2].fn] || (u2[r2[i2].fn] = r2[i2].e);
      return u2;
    }, o = function(n2, t2, i2, r2) {
      if (n2[t2]) {
        var u2 = n2[t2](i2, r2);
        c(u2) || (i2 = u2);
      }
      return i2;
    };
    t.ODataAdaptor = new t.UrlAdaptor().extend({ options: { requestType: "get", accept: "application/json;odata=light;q=1,application/json;odata=verbose;q=0.5", multipartAccept: "multipart/mixed", sortBy: "$orderby", select: "$select", skip: "$skip", take: "$top", count: "$inlinecount", where: "$filter", expand: "$expand", batch: "$batch", changeSet: "--changeset_", batchPre: "batch_", contentId: "Content-Id: ", batchContent: "Content-Type: multipart/mixed; boundary=", changeSetContent: "Content-Type: application/http\nContent-Transfer-Encoding: binary ", batchChangeSetContentType: "Content-Type: application/json; charset=utf-8 " }, onEachWhere: function(n2, t2) {
      return n2.isComplex ? this.onComplexPredicate(n2, t2) : this.onPredicate(n2, t2);
    }, _typeStringQuery: function(n2, i2, r2, u2, f2) {
      r2.indexOf("'") != -1 && (r2 = r2.replace(new RegExp(/'/g), "''"));
      return /[ !@@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(r2) && (r2 = encodeURIComponent(r2)), r2 = "'" + r2 + "'", i2 && (u2 = "cast(" + u2 + ", 'Edm.String')"), t.isGUID(r2) && (f2 = "guid"), n2.ignoreCase && (f2 ? u2 : u2 = "tolower(" + u2 + ")", r2 = r2.toLowerCase()), { val: r2, guid: f2, field: u2 };
    }, onPredicate: function(n2, i2, r2) {
      var f2 = "", o2, l2, e2 = n2.value, v2 = typeof e2, h2 = this._p(n2.field), s2, c2, y2, a2;
      if (e2 instanceof Date && (e2 = "datetime'" + u.replacer(e2).toJSON() + "'"), v2 === "string" && (c2 = this._typeStringQuery(n2, r2, e2, h2, l2), e2 = c2.val, h2 = c2.field, l2 = c2.guid), o2 = t.data.odBiOperator[n2.operator], n2.anyCondition != "" && o2) return f2 += e2.table, f2 += "/" + n2.anyCondition, f2 += "(d:d/", f2 += h2, f2 += o2, f2 += e2.value, f2 + ")";
      if (n2.operator == "in" || n2.operator == "notin") {
        for (f2 += "(", s2 = 0; s2 < e2.length; s2++) e2[s2] instanceof Date && (e2[s2] = "datetime'" + u.replacer(e2[s2]).toJSON() + "'"), typeof e2[s2] == "string" && (c2 = this._typeStringQuery(n2, r2, e2[s2], h2, l2), e2[s2] = c2.val, h2 = c2.field, l2 = c2.guid), f2 += h2, f2 += o2, f2 += e2[s2], s2 != e2.length - 1 && (f2 += n2.operator == "in" ? " or " : " and ");
        return f2 + ")";
      }
      return o2 ? this.onOperation(f2, o2, h2, e2, l2) : (o2 = t.data.odUniOperator[n2.operator], !o2 || v2 !== "string") ? "" : (o2 === "substringof" && (y2 = e2, e2 = h2, h2 = y2), f2 += o2 + "(", f2 += h2 + ",", l2 && (f2 += l2), f2 += e2 + ")", n2.operator == "notcontains" && (f2 += " eq false"), n2.anyCondition != "" && o2) ? (a2 += e2.table, a2 += "/" + n2.anyCondition, a2 += "(d:d/", f2 += f2, a2 + ")") : f2;
    }, onOperation: function(n2, t2, i2, r2, u2) {
      return n2 += i2, n2 += t2, u2 && (n2 += u2), n2 + r2;
    }, onComplexPredicate: function(n2, t2) {
      for (var r2 = [], i2 = 0; i2 < n2.predicates.length; i2++) r2.push("(" + this.onEachWhere(n2.predicates[i2], t2) + ")");
      return r2.join(" " + n2.condition + " ");
    }, onWhere: function(n2) {
      return this.pvt.searches && n2.push(this.onEachWhere(this.pvt.searches, null, true)), n2.join(" and ");
    }, onEachSearch: function(n2) {
      var r2, i2;
      for (n2.fields.length === 0 && f("Query() - Search : oData search requires list of field names to search"), r2 = this.pvt.searches || [], i2 = 0; i2 < n2.fields.length; i2++) r2.push(new t.Predicate(n2.fields[i2], n2.operator, n2.key, n2.ignoreCase));
      this.pvt.searches = r2;
    }, onSearch: function() {
      return this.pvt.searches = t.Predicate.or(this.pvt.searches), "";
    }, onEachSort: function(n2) {
      var i2 = [], t2;
      if (n2.name instanceof Array) for (t2 = 0; t2 < n2.name.length; t2++) i2.push(this._p(n2.name[t2]));
      else i2.push(this._p(n2.name) + (n2.direction === "descending" ? " desc" : ""));
      return i2.join(",");
    }, onSortBy: function(n2) {
      return n2.reverse().join(",");
    }, onGroup: function(n2) {
      return this.pvt.groups = n2, "";
    }, onSelect: function(n2) {
      for (var t2 = 0; t2 < n2.length; t2++) n2[t2] = this._p(n2[t2]);
      return n2.join(",");
    }, onAggregates: function(n2) {
      return this.pvt.aggregates = n2, "";
    }, onCount: function(n2) {
      return n2 === true ? "allpages" : "";
    }, beforeSend: function(n2, t2, i2) {
      i2.url.endsWith(this.options.batch) && i2.type.toLowerCase() === "post" && (t2.setRequestHeader("Accept", l.multipartAccept), t2.setRequestHeader("DataServiceVersion", "2.0"), t2.overrideMimeType("text/plain; charset=x-user-defined"));
      n2.dataSource.crossDomain || (t2.setRequestHeader("DataServiceVersion", "2.0"), t2.setRequestHeader("MaxDataServiceVersion", "2.0"));
    }, processResponse: function(i2, r2, f2, e2, o2, s2) {
      var a2, l2, v2, d2, tt2, g2, it2, k2, h2, ft2;
      if (!t.isNullOrUndefined(i2.d) && (a2 = f2 && f2._requiresCount ? i2.d.results : i2.d, !t.isNullOrUndefined(a2))) for (h2 = 0; h2 < a2.length; h2++) t.isNullOrUndefined(a2[h2].__metadata) || delete a2[h2].__metadata;
      if (l2 = o2 && o2.ejPvtData, e2 && e2.getResponseHeader("Content-Type") && e2.getResponseHeader("Content-Type").indexOf("xml") != -1 && i2.nodeType == 9) return f2._requiresCount ? { result: [], count: 0 } : [];
      if (o2 && this.options.batch && o2.url.endsWith(this.options.batch) && o2.type.toLowerCase() === "post") {
        if (v2 = e2.getResponseHeader("Content-Type"), v2 = v2.substring(v2.indexOf("=batchresponse") + 1), i2 = i2.split(v2), i2.length < 2) return;
        for (i2 = i2[1], g2 = /(?:\bContent-Type.+boundary=)(changesetresponse.+)/i.exec(i2), i2.replace(g2[0], ""), it2 = g2[1], i2 = i2.split(it2), h2 = i2.length; h2 > -1; h2--) /\bContent-ID:/i.test(i2[h2]) && /\bHTTP.+201/.test(i2[h2]) && (d2 = parseInt(/\bContent-ID: (\d+)/i.exec(i2[h2])[1]), s2.added[d2] && (tt2 = u.parseJson(/^\{.+\}/m.exec(i2[h2])[0]), n.extend(s2.added[d2], this.processResponse(tt2))));
        return s2;
      }
      var y2 = e2 && e2.getResponseHeader("DataServiceVersion"), w2 = null, rt2 = {};
      if (y2 = y2 && parseInt(y2, 10) || 2, f2 && f2._requiresCount && ((i2.__count || i2["odata.count"]) && (w2 = i2.__count || i2["odata.count"]), i2.d && (i2 = i2.d), (i2.__count || i2["odata.count"]) && (w2 = i2.__count || i2["odata.count"])), y2 === 3 && i2.value && (i2 = i2.value), i2.d && (i2 = i2.d), y2 < 3 && i2.results && (i2 = i2.results), l2 && l2.aggregates && l2.aggregates.length) {
        var p2 = l2.aggregates, nt2, ut2 = {};
        for (h2 = 0; h2 < p2.length; h2++) nt2 = t.aggregates[p2[h2].type], nt2 && (ut2[p2[h2].field + " - " + p2[h2].type] = nt2(i2, p2[h2].field));
        rt2 = ut2;
      }
      if (l2 && l2.groups && l2.groups.length) for (k2 = l2.groups, h2 = 0; h2 < k2.length; h2++) ft2 = b(k2[h2], f2.queries), i2 = t.group(i2, k2[h2], l2.aggregates, ft2);
      return c(w2) ? i2 : { result: i2, count: w2, aggregates: rt2 };
    }, convertToQueryString: function(n2, t2, i2) {
      var r2 = [], u2 = n2.table || "", f2;
      delete n2.table;
      i2.dataSource.requiresFormat && (n2.$format = "json");
      for (f2 in n2) r2.push(f2 + "=" + n2[f2]);
      return (r2 = r2.join("&"), i2.dataSource.url && i2.dataSource.url.indexOf("?") !== -1 && !u2) ? r2 : r2.length ? u2 + "?" + r2 : u2 || "";
    }, insert: function(n2, t2, i2) {
      return { url: n2.dataSource.url.replace(/\/*$/, i2 ? "/" + i2 : ""), data: JSON.stringify(t2) };
    }, remove: function(n2, i2, r2, u2) {
      return typeof r2 == "string" ? { type: "DELETE", url: t.isGUID(r2) ? n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "(" + r2 + ")" : n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "('" + r2 + "')" } : { type: "DELETE", url: n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "(" + r2 + ")" };
    }, update: function(n2, i2, r2, u2) {
      var f2;
      return f2 = typeof r2[i2] == "string" ? t.isGUID(r2[i2]) ? n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "(" + r2[i2] + ")" : n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "('" + r2[i2] + "')" : n2.dataSource.url.replace(/\/*$/, u2 ? "/" + u2 : "") + "(" + r2[i2] + ")", { type: "PUT", url: f2, data: JSON.stringify(r2), accept: this.options.accept };
    }, batchRequest: function(n2, i2, r2) {
      var e2 = r2.guid = t.getGuid(l.batchPre), o2 = n2.dataSource.url.replace(/\/*$/, "/" + this.options.batch), f2 = { url: r2.url, key: r2.key, cid: 1, cSet: t.getGuid(l.changeSet) }, u2 = "--" + e2 + "\n";
      return u2 += "Content-Type: multipart/mixed; boundary=" + f2.cSet.replace("--", "") + "\n", this.pvt.changeSet = 0, u2 += this.generateInsertRequest(i2.added, f2), u2 += this.generateUpdateRequest(i2.changed, f2), u2 += this.generateDeleteRequest(i2.deleted, f2), u2 += f2.cSet + "--\n", u2 += "--" + e2 + "--", { type: "POST", url: o2, contentType: "multipart/mixed; charset=UTF-8;boundary=" + e2, data: u2 };
    }, generateDeleteRequest: function(n2, t2) {
      var i2, u2, r2;
      if (!n2) return "";
      for (i2 = "", r2 = 0; r2 < n2.length; r2++) i2 += "\n" + t2.cSet + "\n", i2 += l.changeSetContent + "\n\n", i2 += "DELETE ", u2 = typeof n2[r2][t2.key] == "string" ? "'" + n2[r2][t2.key] + "'" : n2[r2][t2.key], i2 += t2.url + "(" + u2 + ") HTTP/1.1\n", i2 += "If-Match : * \n", i2 += "Accept: " + l.accept + "\n", i2 += "Content-Id: " + this.pvt.changeSet++ + "\n", i2 += l.batchChangeSetContentType + "\n";
      return i2 + "\n";
    }, generateInsertRequest: function(n2, t2) {
      var i2, r2;
      if (!n2) return "";
      for (i2 = "", r2 = 0; r2 < n2.length; r2++) i2 += "\n" + t2.cSet + "\n", i2 += l.changeSetContent + "\n\n", i2 += "POST ", i2 += t2.url + " HTTP/1.1\n", i2 += "Accept: " + l.accept + "\n", i2 += "Content-Id: " + this.pvt.changeSet++ + "\n", i2 += l.batchChangeSetContentType + "\n\n", i2 += JSON.stringify(n2[r2]) + "\n";
      return i2;
    }, generateUpdateRequest: function(n2, t2) {
      var i2, u2, r2;
      if (!n2) return "";
      for (i2 = "", r2 = 0; r2 < n2.length; r2++) i2 += "\n" + t2.cSet + "\n", i2 += l.changeSetContent + "\n\n", i2 += "PUT ", u2 = typeof n2[r2][t2.key] == "string" ? "'" + n2[r2][t2.key] + "'" : n2[r2][t2.key], i2 += t2.url + "(" + u2 + ") HTTP/1.1\n", i2 += "If-Match : * \n", i2 += "Accept: " + l.accept + "\n", i2 += "Content-Id: " + this.pvt.changeSet++ + "\n", i2 += l.batchChangeSetContentType + "\n\n", i2 += JSON.stringify(n2[r2]) + "\n\n";
      return i2;
    }, _p: function(n2) {
      return n2.replace(/\./g, "/");
    } });
    t.ODataV4Adaptor = new t.ODataAdaptor().extend({ options: { requestType: "get", accept: "application/json;odata=light;q=1,application/json;odata=verbose;q=0.5", multipartAccept: "multipart/mixed", sortBy: "$orderby", select: "$select", skip: "$skip", take: "$top", count: "$count", search: "$search", where: "$filter", expand: "$expand", batch: "$batch", changeSet: "--changeset_", batchPre: "batch_", contentId: "Content-Id: ", batchContent: "Content-Type: multipart/mixed; boundary=", changeSetContent: "Content-Type: application/http\nContent-Transfer-Encoding: binary ", batchChangeSetContentType: "Content-Type: application/json; charset=utf-8 " }, onCount: function(n2) {
      return n2 === true ? "true" : "";
    }, onPredicate: function(n2, i2, r2) {
      var u2 = "", f2 = n2.value, e2 = f2 instanceof Date;
      return t.data.odUniOperator.contains = "contains", u2 = t.ODataAdaptor.prototype.onPredicate.call(this, n2, i2, r2), t.data.odUniOperator.contains = "substringof", e2 && (u2 = u2.replace(/datetime'(.*)'$/, "$1")), u2;
    }, onOperation: function(n2, t2, i2, r2, u2) {
      return u2 ? (n2 += "(" + i2, n2 += t2, n2 += r2.replace(/["']/g, "") + ")") : (n2 += i2, n2 += t2, n2 += r2), n2;
    }, onEachSearch: function(n2) {
      var t2 = this.pvt.search || [];
      t2.push(n2.key);
      this.pvt.search = t2;
    }, onSearch: function() {
      return this.pvt.search.join(" OR ");
    }, beforeSend: function() {
    }, processQuery: function(n2, i2) {
      for (var f2, o2, e2 = /\/[\d*\/]*/g, u2 = "", r2 = i2._expands.length - 1; r2 > 0; r2--) if (u2.indexOf(i2._expands[r2]) >= 0) i2._expands.pop();
      else if (e2.test(i2._expands[r2])) {
        for (u2 = i2._expands.pop(), f2 = u2.replace(e2, "($expand="), o2 = 0; o2 < u2.split(e2).length - 1; o2++) f2 = f2 + ")";
        i2._expands.unshift(f2);
        r2++;
      }
      return t.ODataAdaptor.prototype.processQuery.apply(this, [n2, i2]);
    }, processResponse: function(i2, r2, f2, e2, o2, s2) {
      var l2 = o2 && o2.ejPvtData, a2, w2, nt2, k2, tt2, y2, d2, p2, h2, rt2;
      if (e2 && e2.getResponseHeader("Content-Type") && e2.getResponseHeader("Content-Type").indexOf("xml") != -1 && i2.nodeType == 9) return f2._requiresCount ? { result: [], count: 0 } : [];
      if (o2 && this.options.batch && o2.url.endsWith(this.options.batch) && o2.type.toLowerCase() === "post") {
        if (a2 = e2.getResponseHeader("Content-Type"), a2 = a2.substring(a2.indexOf("=batchresponse") + 1), i2 = i2.split(a2), i2.length < 2) return;
        for (i2 = i2[1], k2 = /(?:\bContent-Type.+boundary=)(changesetresponse.+)/i.exec(i2), i2.replace(k2[0], ""), tt2 = k2[1], i2 = i2.split(tt2), h2 = i2.length; h2 > -1; h2--) /\bContent-ID:/i.test(i2[h2]) && /\bHTTP.+201/.test(i2[h2]) && (w2 = parseInt(/\bContent-ID: (\d+)/i.exec(i2[h2])[1]), s2.added[w2] && (nt2 = u.parseJson(/^\{.+\}/m.exec(i2[h2])[0]), n.extend(s2.added[w2], this.processResponse(nt2))));
        return s2;
      }
      if (y2 = null, d2 = {}, f2 && f2._requiresCount && "@odata.count" in i2 && (y2 = i2["@odata.count"]), i2 = t.isNullOrUndefined(i2.value) ? i2 : i2.value, l2 && l2.aggregates && l2.aggregates.length) {
        var v2 = l2.aggregates, g2, it2 = {};
        for (h2 = 0; h2 < v2.length; h2++) g2 = t.aggregates[v2[h2].type], g2 && (it2[v2[h2].field + " - " + v2[h2].type] = g2(i2, v2[h2].field));
        d2 = it2;
      }
      if (l2 && l2.groups && l2.groups.length) for (p2 = l2.groups, h2 = 0; h2 < p2.length; h2++) rt2 = b(p2[h2], f2.queries), i2 = t.group(i2, p2[h2], l2.aggregates, rt2);
      return c(y2) ? i2 : { result: i2, count: y2, aggregates: d2 };
    } });
    t.JsonAdaptor = new t.Adaptor().extend({ processQuery: function(n2, t2) {
      for (var u2 = n2.dataSource.json.slice(0), o2 = u2.length, s2 = true, f2, i2, h2 = {}, e2 = 0; e2 < t2.queries.length; e2++) i2 = t2.queries[e2], f2 = this[i2.fn].call(this, u2, i2.e, t2), i2.fn == "onAggregates" ? h2[i2.e.field + " - " + i2.e.type] = f2 : u2 = f2 !== r ? f2 : u2, (i2.fn === "onPage" || i2.fn === "onSkip" || i2.fn === "onTake" || i2.fn === "onRange") && (s2 = false), s2 && (o2 = u2.length);
      return t2._requiresCount && (u2 = { result: u2, count: o2, aggregates: h2 }), u2;
    }, batchRequest: function(n2, t2, i2) {
      for (var r2 = 0; r2 < t2.added.length; r2++) this.insert(n2, t2.added[r2]);
      for (r2 = 0; r2 < t2.changed.length; r2++) this.update(n2, i2.key, t2.changed[r2]);
      for (r2 = 0; r2 < t2.deleted.length; r2++) this.remove(n2, i2.key, t2.deleted[r2]);
      return t2;
    }, onWhere: function(n2, t2) {
      return n2 ? n2.filter(function(n3) {
        return t2.validate(n3);
      }) : n2;
    }, onAggregates: function(n2, i2) {
      var r2 = t.aggregates[i2.type];
      return !n2 || !r2 || n2.length == 0 ? null : r2(n2, i2.field);
    }, onSearch: function(n2, i2) {
      return !n2 || !n2.length ? n2 : (i2.fieldNames.length === 0 && t.pvt.getFieldList(n2[0], i2.fieldNames), n2.filter(function(n3) {
        for (var r2 = 0; r2 < i2.fieldNames.length; r2++) if (i2.comparer.call(n3, t.pvt.getObject(i2.fieldNames[r2], n3), i2.searchKey, i2.ignoreCase, i2.ignoreAccent)) return true;
        return false;
      }));
    }, onSortBy: function(n2, i2, r2) {
      var o2, u2, f2;
      if (!n2) return n2;
      if (u2 = e(i2.fieldName, r2), !u2) return n2.sort(i2.comparer);
      if (u2 instanceof Array) {
        for (u2 = u2.slice(0), f2 = u2.length - 1; f2 >= 0; f2--) u2[f2] && (o2 = i2.comparer, u2[f2].endsWith(" desc") && (o2 = t.pvt.fnSort(t.sortOrder.Descending), u2[f2] = u2[f2].replace(" desc", "")), n2 = it(n2, u2[f2], o2, []));
        return n2;
      }
      return it(n2, u2, i2.comparer, r2 ? r2.queries : []);
    }, onGroup: function(n2, i2, r2) {
      var u2, o2, s2, f2, h2;
      if (!n2) return n2;
      if (u2 = a(r2.queries, "onAggregates"), o2 = [], u2.length) for (f2 = 0; f2 < u2.length; f2++) s2 = u2[f2].e, o2.push({ type: s2.type, field: e(s2.field, r2) });
      return h2 = b(i2.fieldName, r2.queries), t.group(n2, e(i2.fieldName, r2), o2, h2);
    }, onPage: function(n2, t2, i2) {
      var r2 = e(t2.pageSize, i2), u2 = (e(t2.pageIndex, i2) - 1) * r2, f2 = u2 + r2;
      return n2 ? n2.slice(u2, f2) : n2;
    }, onRange: function(n2, t2) {
      return n2 ? n2.slice(e(t2.start), e(t2.end)) : n2;
    }, onTake: function(n2, t2) {
      return n2 ? n2.slice(0, e(t2.nos)) : n2;
    }, onSkip: function(n2, t2) {
      return n2 ? n2.slice(e(t2.nos)) : n2;
    }, onSelect: function(n2, i2) {
      return n2 ? t.select(n2, e(i2.fieldNames)) : n2;
    }, insert: function(n2, t2) {
      return n2.dataSource.json.push(t2);
    }, remove: function(n2, i2, r2) {
      var f2 = n2.dataSource.json, u2;
      for (typeof r2 == "object" && (r2 = t.getObject(i2, r2)), u2 = 0; u2 < f2.length; u2++) if (t.getObject(i2, f2[u2]) === r2) break;
      return u2 !== f2.length ? f2.splice(u2, 1) : null;
    }, update: function(i2, r2, u2) {
      for (var e2 = i2.dataSource.json, o2 = t.getObject(r2, u2), f2 = 0; f2 < e2.length; f2++) if (t.getObject(r2, e2[f2]) === o2) break;
      return f2 < e2.length ? n.extend(e2[f2], u2) : null;
    } });
    t.ForeignKeyAdaptor = function(i2, u2) {
      var f2 = new t[u2 || "JsonAdaptor"]().extend({ init: function() {
        var r2, n2;
        for (this.foreignData = [], this.key = [], this.adaptorType = u2, this.value = [], this.fValue = [], this.keyField = [], r2 = i2, n2 = 0; n2 < r2.length; n2++) this.foreignData[n2] = r2[n2].dataSource, this.key[n2] = r2[n2].foreignKeyField, this.fValue[n2] = t.isNullOrUndefined(r2[n2].field) ? r2[n2].foreignKeyValue : r2[n2].field + "_" + r2[n2].foreignKeyValue, this.value[n2] = r2[n2].foreignKeyValue, this.keyField[n2] = r2[n2].field || r2[n2].foreignKeyField, this.initial = true;
      }, processQuery: function(n2, i3) {
        var e2 = n2.dataSource.json, u3, f3, r2;
        if (this.initial) {
          for (u3 = 0; u3 < e2.length; u3++) for (f3 = this, r2 = 0; r2 < this.foreignData.length; r2++) this.foreignData[r2].filter(function(n3) {
            t.getObject(f3.key[r2], n3) == t.getObject(f3.keyField[r2], e2[u3]) && (e2[u3][f3.fValue[r2]] = t.getObject(f3.value[r2], n3));
          });
          this.initial = false;
        }
        return this.base.processQuery.apply(this, [n2, i3]);
      }, setValue: function(i3) {
        for (var o2, f3, e2, u3 = 0; u3 < this.foreignData.length; u3++) o2 = this, f3 = i3[this.fValue[u3]], typeof f3 != "string" || isNaN(f3) || (f3 = t.parseFloat(f3)), e2 = n.grep(o2.foreignData[u3], function(n2) {
          return n2[o2.value[u3]] == f3;
        })[0], t.isNullOrUndefined(e2) && (e2 = n.grep(o2.foreignData[u3], function(n2) {
          return n2[o2.key[u3]] == f3;
        })[0], t.getObject(this.value[u3], e2) != r && t.createObject(o2.value[u3], t.getObject(this.value[u3], e2), i3)), t.getObject(this.value[u3], e2) != r && t.createObject(this.keyField[u3], t.getObject(this.key[u3], e2), i3);
      }, insert: function(n2, t2, i3) {
        return this.setValue(t2), { url: n2.dataSource.insertUrl || n2.dataSource.crudUrl || n2.dataSource.url, data: JSON.stringify({ value: t2, table: i3, action: "insert", antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" }) };
      }, update: function(n2, i3, r2, u3) {
        return this.setValue(r2), t.JsonAdaptor.prototype.update(n2, i3, r2, u3), { type: "POST", url: n2.dataSource.updateUrl || n2.dataSource.crudUrl || n2.dataSource.url, data: JSON.stringify({ value: r2, action: "update", keyColumn: i3, key: r2[i3], table: u3, antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" }) };
      } });
      return n.extend(this, new f2()), this;
    };
    t.remoteSaveAdaptor = new t.JsonAdaptor().extend({ beforeSend: t.UrlAdaptor.prototype.beforeSend, insert: t.UrlAdaptor.prototype.insert, update: t.UrlAdaptor.prototype.update, remove: t.UrlAdaptor.prototype.remove, addParams: t.UrlAdaptor.prototype.addParams, batchRequest: function(n2, t2, i2, r2) {
      var u2 = { changed: t2.changed, added: t2.added, deleted: t2.deleted, action: "batch", table: i2.url, key: i2.key, antiForgery: n2.dataSource.antiForgery ? n2.antiForgeryToken().value : "" };
      return r2 && this.addParams({ dm: n2, query: r2, params: r2._params, reqParams: u2 }), { type: "POST", url: n2.dataSource.batchUrl || n2.dataSource.crudUrl || n2.dataSource.url, contentType: "application/json; charset=utf-8", dataType: "json", data: JSON.stringify(u2) };
    }, processResponse: function(n2, i2, r2, u2, f2, e2, o2) {
      if (t.isNullOrUndefined(e2)) return n2.d ? n2.d : n2;
      n2.d && (n2 = n2.d);
      n2.added && (e2.added = t.parseJSON(n2.added));
      n2.changed && (e2.changed = t.parseJSON(n2.changed));
      n2.deleted && (e2.deleted = t.parseJSON(n2.deleted));
      for (var s2 = 0; s2 < e2.added.length; s2++) t.JsonAdaptor.prototype.insert(i2, e2.added[s2]);
      for (s2 = 0; s2 < e2.changed.length; s2++) t.JsonAdaptor.prototype.update(i2, o2, e2.changed[s2]);
      for (s2 = 0; s2 < e2.deleted.length; s2++) t.JsonAdaptor.prototype.remove(i2, o2, e2.deleted[s2]);
      return n2;
    } });
    t.WebApiAdaptor = new t.ODataAdaptor().extend({ insert: function(n2, t2) {
      return { type: "POST", url: n2.dataSource.url, data: JSON.stringify(t2) };
    }, remove: function(n2, t2, i2) {
      return { type: "DELETE", url: n2.dataSource.url + "/" + i2, data: JSON.stringify(i2) };
    }, update: function(n2, t2, i2) {
      return { type: "PUT", url: n2.dataSource.url, data: JSON.stringify(i2) };
    }, batchRequest: function(i2, r2, u2) {
      var e2 = u2.guid = t.getGuid(l.batchPre), f2 = [];
      return n.each(r2.added, function(n2, t2) {
        f2.push("--" + e2);
        f2.push("Content-Type: application/http; msgtype=request", "");
        f2.push("POST " + i2.dataSource.insertUrl + " HTTP/1.1");
        f2.push("Content-Type: application/json; charset=utf-8");
        f2.push("Host: " + location.host);
        f2.push("", t2 ? JSON.stringify(t2) : "");
      }), n.each(r2.changed, function(n2, t2) {
        f2.push("--" + e2);
        f2.push("Content-Type: application/http; msgtype=request", "");
        f2.push("PUT " + i2.dataSource.updateUrl + " HTTP/1.1");
        f2.push("Content-Type: application/json; charset=utf-8");
        f2.push("Host: " + location.host);
        f2.push("", t2 ? JSON.stringify(t2) : "");
      }), n.each(r2.deleted, function(n2, t2) {
        f2.push("--" + e2);
        f2.push("Content-Type: application/http; msgtype=request", "");
        f2.push("DELETE " + i2.dataSource.removeUrl + "/" + t2[u2.key] + " HTTP/1.1");
        f2.push("Content-Type: application/json; charset=utf-8");
        f2.push("Host: " + location.host);
        f2.push("", t2 ? JSON.stringify(t2) : "");
      }), f2.push("--" + e2 + "--", ""), { type: "POST", url: i2.dataSource.batchUrl || i2.dataSource.crudUrl || i2.dataSource.url, data: f2.join("\r\n"), contentType: 'multipart/mixed; boundary="' + e2 + '"' };
    }, processResponse: function(n2, i2, r2, u2, f2) {
      var o2 = f2 && f2.ejPvtData, l2, e2, w2;
      if (f2 && f2.type.toLowerCase() != "post") {
        var h2 = u2 && u2.getResponseHeader("DataServiceVersion"), a2 = null, y2 = {};
        if (h2 = h2 && parseInt(h2, 10) || 2, r2 && r2._requiresCount && (c(n2.Count) || (a2 = n2.Count)), h2 < 3 && n2.Items && (n2 = n2.Items), o2 && o2.aggregates && o2.aggregates.length) {
          var s2 = o2.aggregates, v2, p2 = {};
          for (e2 = 0; e2 < s2.length; e2++) v2 = t.aggregates[s2[e2].type], v2 && (p2[s2[e2].field + " - " + s2[e2].type] = v2(n2, s2[e2].field));
          y2 = p2;
        }
        if (o2 && o2.groups && o2.groups.length) for (l2 = o2.groups, e2 = 0; e2 < l2.length; e2++) w2 = b(l2[e2], r2.queries), n2 = t.group(n2, l2[e2], o2.aggregates, w2);
        return c(a2) ? n2 : { result: n2, count: a2, aggregates: y2 };
      }
    } });
    e = function(n2, t2) {
      return typeof n2 == "function" ? n2.call(t2 || {}) : n2;
    };
    t.TableModel = function(i2, r2, u2, e2) {
      var c2, o2, l2, s2;
      if (!p(this, t.TableModel)) return new t.TableModel(r2);
      for (p(r2, Array) || f("ej.TableModel - Json Array is required"), c2 = [], l2 = h(ht, this), s2 = 0; s2 < r2.length; s2++) {
        o2 = new t.Model(r2[s2], this);
        o2.state = "unchanged";
        o2.on("stateChange", l2);
        e2 && o2.computes(e2);
        c2.push(o2);
      }
      return this.name = i2 || "table1", this.rows = t.NotifierArray(c2), this._deleted = [], this._events = n({}), this.dataManager = u2, this._isDirty = false, this;
    };
    t.TableModel.prototype = { on: function(n2, t2) {
      this._events.on(n2, t2);
    }, off: function(n2, t2) {
      this._events.off(n2, t2);
    }, setDataManager: function(n2) {
      this.dataManagar = n2;
    }, saveChanges: function() {
      if (this.dataManager && p(this.dataManager, t.DataManager) || f("ej.TableModel - saveChanges : Set the dataManager using setDataManager function"), this.isDirty()) {
        var n2 = this.dataManager.saveChanges(this.getChanges(), this.key, this.name);
        n2.done(h(function(n3) {
          for (var t2 = this.toArray(), i2 = 0; i2 < t2.length; i2++) t2.state === "added" && t2.set(this.key, n3.added.filter(function(n4) {
            return n4[this.key] === t2.get(this.key);
          })[0][this.key]), t2[i2].markCommit();
          this._events.triggerHandler({ type: "save", table: this });
        }, this));
        n2.fail(h(function(n3) {
          this.rejectChanges();
          this._events.triggerHandler({ type: "reject", table: this, error: n3 });
        }, this));
        this._isDirty = false;
      }
    }, rejectChanges: function() {
      for (var t2 = this.toArray(), n2 = 0; n2 < t2.length; n2++) t2[n2].revert(true);
      this._isDirty = false;
      this._events.triggerHandler({ type: "reject", table: this });
    }, insert: function(n2) {
      var i2 = new t.Model(n2);
      i2._isDirty = this._isDirty = true;
      this.rows.push(i2);
      this._events.triggerHandler({ type: "insert", model: i2, table: this });
    }, update: function(n2) {
      var r2;
      this.key || f("TableModel - update : Primary key should be assigned to TableModel.key");
      var i2 = n2, t2, u2 = this.key, e2 = i2[u2];
      t2 = this.rows.array.filter(function(n3) {
        return n3.get(u2) === e2;
      });
      t2 = t2[0];
      for (r2 in i2) t2.set(r2, i2[r2]);
      this._isDirty = true;
      this._events.triggerHandler({ type: "update", model: t2, table: this });
    }, remove: function(n2) {
      var t2, i2, e2, u2;
      for (this.key || f("TableModel - update : Primary key should be assigned to TableModel.key"), t2 = this.key, i2 = -1, n2 && typeof n2 == "object" && (n2 = n2[t2] !== r ? n2[t2] : n2.get(t2)), u2 = 0; u2 < this.rows.length(); u2++) if (this.rows.array[u2].get(t2) === n2) {
        i2 = u2;
        break;
      }
      i2 > -1 && (e2 = this.rows.removeAt(i2), e2.markDelete(), this._deleted.push({ model: e2, position: i2 }), this._isDirty = true, this._events.triggerHandler({ type: "remove", model: e2, table: this }));
    }, isDirty: function() {
      return this._isDirty;
    }, getChanges: function() {
      for (var i2 = { added: [], changed: [] }, r2 = this.toArray(), n2 = 0; n2 < r2.length; n2++) i2[r2[n2].state] && i2[r2[n2].state].push(r2[n2].json);
      return i2.deleted = t.select(this._deleted, ["model"]), i2;
    }, toArray: function() {
      return this.rows.toArray();
    }, setDirty: function(n2, t2) {
      this._isDirty !== !!n2 && (this._isDirty = !!n2, this._events.triggerHandler({ type: "dirty", table: this, model: t2 }));
    }, get: function(n2) {
      return this.rows.array[n2];
    }, length: function() {
      return this.rows.array.length;
    }, bindTo: function(t2) {
      var i2 = nt, e2 = n(t2.html()), f2 = this.toArray(), u2, r2;
      for (n.inArray(t2.prop("tagName").toLowerCase(), ["table", "tbody"]) && (i2 = tt), i2.insertBefore(t2), t2.detach().empty(), r2 = 0; r2 < f2.length; r2++) u2 = e2.clone(), f2[r2].bindTo(u2), t2.append(u2);
      t2.insertAfter(i2);
      i2.remove();
    } };
    nt = i ? n(document.createElement("div")) : {};
    tt = i ? n(document.createElement("tr")) : {};
    t.Model = function(i2, r2, u2) {
      typeof r2 == "string" && (u2 = r2, r2 = null);
      this.$id = et("m");
      this.json = i2;
      this.table = r2 instanceof t.TableModel ? r2 : null;
      this.name = u2 || this.table && this.table.name;
      this.dataManager = r2 instanceof t.DataManager ? r2 : r2.dataManagar;
      this.actual = {};
      this._events = n({});
      this.isDirty = false;
      this.state = "added";
      this._props = [];
      this._computeEls = {};
      this._fields = {};
      this._attrEls = {};
      this._updates = {};
      this.computed = {};
    };
    t.Model.prototype = { computes: function(t2) {
      n.extend(this.computed, t2);
    }, on: function(n2, t2) {
      this._events.on(n2, t2);
    }, off: function(n2, t2) {
      this._events.off(n2, t2);
    }, set: function(n2, t2) {
      var i2 = this.json, e2 = n2, f2, u2;
      for (n2 = n2.split("."), u2 = 0; u2 < n2.length - 1; u2++) n2 = n2[0], i2 = i2[n2[0]];
      this.isDirty = true;
      this.changeState("changed", { from: "set" });
      f2 = i2[n2];
      this.actual[n2] !== r || n2 in this.actual || (this.actual[n2] = t2);
      i2[n2] = t2;
      this._updateValues(n2, t2);
      this._events.triggerHandler({ type: e2, current: t2, previous: f2, model: this });
    }, get: function(n2) {
      return t.pvt.getObject(n2, this.json);
    }, revert: function(n2) {
      for (var t2 in this.actual) this.json[t2] = this.actual[t2];
      this.isDirty = false;
      n2 ? this.state = "unchanged" : this.changeState("unchanged", { from: "revert" });
    }, save: function(i2, r2) {
      return (i2 = i2 || this.dataManagar, r2 = r2 || i2.dataSource.key, i2 || f("ej.Model - DataManager is required to commit the changes"), this.state === "added") ? i2.insert(this.json, this.name).done(t.proxy(function(t2) {
        n.extend(this.json, t2.record);
      }, this)) : this.state === "changed" ? i2.update(r2, this.json, this.name) : this.state === "deleted" ? i2.remove(r2, this.json, this.name) : void 0;
    }, markCommit: function() {
      this.isDirty = false;
      this.changeState("unchanged", { from: "commit" });
    }, markDelete: function() {
      this.changeState("deleted", { from: "delete" });
    }, changeState: function(t2, i2) {
      if (this.state !== t2) {
        if (this.state === "added") if (t2 === "deleted") t2 = "unchanged";
        else return;
        var r2 = t2;
        i2 = i2 || {};
        this.state = t2;
        this._events.triggerHandler(n.extend({ type: "stateChange", current: t2, previous: r2, model: this }, i2));
      }
    }, properties: function() {
      if (this._props.length) return this._props;
      for (var n2 in this.json) this._props.push(n2), this._updates[n2] = { read: [], input: [] };
      return this._props;
    }, bindTo: function(t2) {
      var e2 = n(t2), r2, i2, o2 = e2.find("[ej-observe], [ej-computed], [ej-prop]"), s2 = o2.length, u2, f2;
      for (e2.data("ejModel", this), u2 = { fields: [], props: [], computes: [] }, f2 = 0; f2 < s2; f2++) {
        if (r2 = o2.eq(f2), i2 = r2.attr("ej-prop"), i2 && this._processAttrib(i2, r2, u2), i2 = r2.attr("ej-observe"), i2 && this._props.indexOf(i2) !== -1) {
          this._processField(r2, i2, u2);
          continue;
        }
        if (i2 = r2.attr("ej-computed"), i2) {
          this._processComputed(i2, r2, u2);
          continue;
        }
      }
      e2.data("ejModelBinding" + this.$id, u2);
    }, unbind: function(t2) {
      var u2, r2 = { props: this._attrEls, computes: this._computeEls }, f2 = false, i2, c2, e2, s2, h2, o2;
      t2 && (r2 = n(t2).removeData("ejModel").data("ejModelBinding" + this.$id) || r2, f2 = true);
      for (i2 in this.computed) u2 = r2.computes[i2], i2 = this.computed[i2], u2 && i2.deps && (this.off(i2.deps.join(" "), u2.handle), f2 && delete this._computeEls[i2]);
      f2 || (this._computeEls = {});
      for (i2 in r2.props) u2 = r2.props[i2], u2 && (this.off(u2.deps.join(" "), u2.handle), delete r2.props[i2], f2 && delete this._attrEls[i2]);
      if (f2 || (this._attrEls = {}), r2.fields && r2.fields.length) for (c2 = r2.fields.length, o2 = 0; o2 < c2; o2++) e2 = r2.fields[o2], n(e2).off("change", null, this._changeHandler), h2 = this.formElements.indexOf(e2.tagName.toLowerCase()) !== -1 ? "input" : "read", s2 = this._updates[h2].indexOf(e2), s2 !== -1 && this._updates[h2].splice(s2, 1);
    }, _processComputed: function(n2, t2, i2) {
      if (n2) {
        var r2, u2, f2 = st(n2), e2 = this.formElements.indexOf(t2[0].tagName.toLowerCase()) !== -1 ? "val" : "html";
        this.computed[n2] && this.computed[f2] || (this.computed[f2] = { value: new Function("var e = this; return " + n2), deps: this._generateDeps(n2) }, n2 = f2);
        r2 = this.computed[n2];
        r2.get || (r2.get = function() {
          r2.value.call(this.json);
        });
        u2 = r2.deps;
        r2 = r2.value;
        this._updateDeps(u2);
        this._updateElement(t2, e2, r2);
        r2 = { el: t2, handle: h(this._computeHandle, this, { value: n2, type: e2 }) };
        this._computeEls[n2] = r2;
        i2.computes[n2] = r2;
        this.on(u2.join(" "), r2.handle);
      }
    }, _computeHandle: function(n2) {
      var t2 = this._computeEls[n2.value];
      t2 && this.computed[n2.value] && this._updateElement(t2.el, n2.type, this.computed[n2.value].value);
    }, _updateElement: function(t2, i2, r2) {
      t2[i2](r2.call(n.extend({}, this.json, this.computed)));
    }, _updateDeps: function(n2) {
      for (var i2 = 0; i2 < n2.length; i2++) !(n2[i2] in this.json) && n2[i2] in this.computed && t.merge(n2, this.computed[n2[i2]].deps);
    }, _generateDeps: function(n2) {
      for (var r2 = n2.replace(/(^e\.)|( e\.)/g, "#%^*##ej.#").split("#%^*#"), t2, u2 = [], i2 = 0; i2 < r2.length; i2++) r2[i2].startsWith("#ej.#") && (t2 = r2[i2].replace("#ej.#", "").split(" ")[0], t2 && this._props.indexOf(t2) !== -1 && u2.push(t2));
      return u2;
    }, _processAttrib: function(n2, t2, i2) {
      var e2, o2, u2 = {}, r2, f2;
      for (n2 = n2.replace(/^ +| +$/g, "").split(";"), r2 = 0; r2 < n2.length; r2++) (n2[r2] = n2[r2].split(":"), n2[r2].length < 2) || (e2 = n2[r2][0].replace(/^ +| +$/g, "").replace(/^'|^"|'$|"$/g, ""), u2[e2] = n2[r2][1].replace(/^ +| +$/g, "").replace(/^'|^"|'$|"$/g, ""));
      n2 = u2;
      f2 = [];
      for (e2 in n2) f2.push(n2[e2]);
      this._updateDeps(f2);
      this._updateProps(t2, n2);
      u2 = et("emak");
      o2 = { el: t2, handle: h(this._attrHandle, this, u2), value: n2, deps: f2 };
      t2.prop("ejmodelattrkey", u2);
      i2.props[u2] = o2;
      this._attrEls[u2] = o2;
      this.on(f2.join(" "), o2.handle);
    }, _attrHandle: function(n2) {
      var t2 = this._attrEls[n2];
      t2 && this._updateProps(t2.el, t2.value);
    }, _updateProps: function(t2, i2) {
      var f2 = this.json, r2, u2 = this.computed;
      for (var e2 in i2) r2 = i2[e2], r2 in f2 ? r2 = f2[r2] : r2 in u2 && (r2 = u2[r2], r2 && (r2 = r2.value.call(n.extend({}, this.json, u2)))), c(r2) || t2.prop(e2, r2);
    }, _updateValues: function(n2, t2) {
      var i2 = this._updates[n2];
      i2 && (i2.read || i2.input) && (this._ensureItems(i2.read, "html", t2), this._ensureItems(i2.input, "val", t2));
    }, _ensureItems: function(t2, i2, r2) {
      if (t2) for (var u2 = t2.length - 1; u2 > -1; u2--) {
        if (!t2[u2].offsetParent) {
          t2.splice(u2, 1);
          continue;
        }
        n(t2[u2])[i2](r2);
      }
    }, _changeHandler: function(t2) {
      t2.data.self.set(t2.data.prop, n(this).val());
    }, _processField: function(n2, t2, i2) {
      var u2 = { self: this, prop: t2 }, r2 = this.get(t2);
      if (i2.fields.push(n2[0]), this.formElements.indexOf(n2[0].tagName.toLowerCase()) === -1) return n2.html(r2), this._updates[t2].read.push(n2[0]);
      n2.val(r2).off("change", null, this._changeHandler).on("change", null, u2, this._changeHandler);
      return this._updates[t2].input.push(n2[0]);
    }, formElements: ["input", "select", "textarea"] };
    var ot = /[^\w]+/g, st = function(n2) {
      return n2.replace(ot, "_");
    }, ht = function(n2) {
      this.setDirty(true, n2.model);
    };
    if (t.Predicate = function(n2, i2, r2, u2, f2) {
      if (!(this instanceof t.Predicate)) return new t.Predicate(n2, i2, r2, u2, f2);
      if (this.ignoreAccent = false, typeof n2 == "string") {
        var e2 = "";
        i2.toLowerCase().indexOf(" any") != -1 ? (i2 = i2.replace(" any", ""), e2 = "any") : i2.toLowerCase().indexOf(" all") != -1 && (i2 = i2.replace(" all", ""), e2 = "all");
        this.field = n2;
        this.operator = i2;
        this.value = r2;
        this.ignoreCase = u2;
        this.ignoreAccent = f2;
        this.isComplex = false;
        this.anyCondition = e2;
        this._comparer = t.data.fnOperators.processOperator(e2 != "" ? e2 : this.operator);
      } else (n2 instanceof t.Predicate && r2 instanceof t.Predicate || r2 instanceof Array) && (this.isComplex = true, this.condition = i2.toLowerCase(), this.predicates = [n2], r2 instanceof Array ? [].push.apply(this.predicates, r2) : this.predicates.push(r2));
      return this;
    }, t.Predicate.and = function() {
      return y._combinePredicates([].slice.call(arguments, 0), "and");
    }, t.Predicate.or = function() {
      return y._combinePredicates([].slice.call(arguments, 0), "or");
    }, t.Predicate.fromJSON = function(n2) {
      var i2, t2, r2;
      if (p(n2, Array)) {
        for (i2 = [], t2 = 0, r2 = n2.length; t2 < r2; t2++) i2.push(y._fromJSON(n2[t2]));
        return i2;
      }
      return y._fromJSON(n2);
    }, y = { _combinePredicates: function(n2, i2) {
      if (!n2.length) return r;
      if (n2.length === 1) {
        if (!p(n2[0], Array)) return n2[0];
        n2 = n2[0];
      }
      return new t.Predicate(n2[0], i2, n2.slice(1));
    }, _combine: function(n2, i2, r2, u2, e2, o2, s2) {
      return i2 instanceof t.Predicate ? t.Predicate[e2](n2, i2) : typeof i2 == "string" ? t.Predicate[e2](n2, new t.Predicate(i2, r2, u2, o2, s2)) : f("Predicate - " + e2 + " : invalid arguments");
    }, _fromJSON: function(n2) {
      var i2;
      if (!n2 || p(n2, t.Predicate)) return n2;
      var u2 = n2.predicates || [], f2 = u2.length, r2 = [];
      for (i2 = 0; i2 < f2; i2++) r2.push(y._fromJSON(u2[i2]));
      return n2.isComplex ? new t.Predicate(r2[0], n2.condition, r2.slice(1)) : new t.Predicate(n2.field, n2.operator, t.parseJSON({ val: n2.value }).val, n2.ignoreCase, n2.ignoreAccent);
    } }, t.Predicate.prototype = { and: function(n2, t2, i2, r2, u2) {
      return y._combine(this, n2, t2, i2, "and", r2, u2);
    }, or: function(n2, t2, i2, r2, u2) {
      return y._combine(this, n2, t2, i2, "or", r2, u2);
    }, validate: function(n2) {
      var f2 = this.predicates, r2, u2, i2;
      if (!this.isComplex) return this._comparer.call(this, t.pvt.getObject(this.field, n2), this.value, this.ignoreCase, this.ignoreAccent);
      for (r2 = this.condition === "and", i2 = 0; i2 < f2.length; i2++) if (u2 = f2[i2].validate(n2), r2) {
        if (!u2) return false;
      } else if (u2) return true;
      return r2;
    }, toJSON: function() {
      var t2, i2, n2;
      if (this.isComplex) for (t2 = [], i2 = this.predicates, n2 = 0; n2 < i2.length; n2++) t2.push(i2[n2].toJSON());
      return { isComplex: this.isComplex, field: this.field, operator: this.operator, value: this.value, ignoreCase: this.ignoreCase, ignoreAccent: this.ignoreAccent, condition: this.condition, predicates: t2, anyCondition: this.anyCondition };
    } }, t.dataUtil = { swap: function(n2, t2, i2) {
      if (t2 != i2) {
        var r2 = n2[t2];
        n2[t2] = n2[i2];
        n2[i2] = r2;
      }
    }, mergeSort: function(n2, i2, r2) {
      return r2 && typeof r2 != "string" || (r2 = t.pvt.fnSort(r2, true)), typeof i2 == "function" && (r2 = i2, i2 = null), t.pvt.mergeSort(n2, i2, r2);
    }, max: function(n2, i2, r2) {
      return typeof i2 == "function" && (r2 = i2, i2 = null), t.pvt.getItemFromComparer(n2, i2, r2 || t.pvt.fnDescending);
    }, min: function(n2, i2, r2) {
      return typeof i2 == "function" && (r2 = i2, i2 = null), t.pvt.getItemFromComparer(n2, i2, r2 || t.pvt.fnAscending);
    }, distinct: function(n2, t2, i2) {
      for (var f2 = [], r2, e2 = {}, u2 = 0; u2 < n2.length; u2++) r2 = v(n2, t2, u2), r2 in e2 || (f2.push(i2 ? n2[u2] : r2), e2[r2] = 1);
      return f2;
    }, sum: function(n2, t2) {
      for (var u2 = 0, i2, f2 = typeof v(n2, t2, 0) != "number", r2 = 0; r2 < n2.length; r2++) i2 = v(n2, t2, r2), isNaN(i2) || i2 === null || (f2 && (i2 = +i2), u2 += i2);
      return u2;
    }, avg: function(n2, i2) {
      return t.sum(n2, i2) / n2.length;
    }, select: function(n2, i2) {
      for (var u2 = [], r2 = 0; r2 < n2.length; r2++) u2.push(t.pvt.extractFields(n2[r2], i2));
      return u2;
    }, group: function(i2, r2, u2, f2, e2, o2) {
      var p2, a2, c2, h2, k2, l2, b2, y2, s2, w2;
      if (e2 = e2 || 1, i2.GROUPGUID == t.pvt.consts.GROUPGUID) {
        for (s2 = 0; s2 < i2.length; s2++) t.isNullOrUndefined(o2) ? (i2[s2].items = t.group(i2[s2].items, r2, u2, f2, i2.level + 1), i2[s2].count = i2[s2].items.length) : (p2 = -1, w2 = n.grep(o2, function(n2) {
          return n2.key == i2[s2].key;
        }), p2 = o2.indexOf(w2[0]), i2[s2].items = t.group(i2[s2].items, r2, u2, f2, i2.level + 1, o2[p2].items), i2[s2].count = o2[p2].count);
        return i2.childLevels += 1, i2;
      }
      for (a2 = {}, c2 = [], c2.GROUPGUID = t.pvt.consts.GROUPGUID, c2.level = e2, c2.childLevels = 0, c2.records = i2, l2 = 0; l2 < i2.length; l2++) h2 = v(i2, r2, l2), t.isNullOrUndefined(f2) || (h2 = f2(h2, r2)), a2[h2] || (a2[h2] = { key: h2, count: 0, items: [], aggregates: {}, field: r2 }, c2.push(a2[h2]), t.isNullOrUndefined(o2) || (k2 = n.grep(o2, function(n2) {
        return n2.key == a2[h2].key;
      }), a2[h2].count = k2[0].count)), a2[h2].count = t.isNullOrUndefined(o2) ? a2[h2].count += 1 : a2[h2].count, a2[h2].items.push(i2[l2]);
      if (u2 && u2.length) for (l2 = 0; l2 < c2.length; l2++) {
        for (b2 = {}, s2 = 0; s2 < u2.length; s2++) y2 = t.aggregates[u2[s2].type], t.isNullOrUndefined(o2) ? y2 && (b2[u2[s2].field + " - " + u2[s2].type] = y2(c2[l2].items, u2[s2].field)) : (w2 = n.grep(o2, function(n2) {
          return n2.key == c2[l2].key;
        }), y2 && (b2[u2[s2].field + " - " + u2[s2].type] = y2(w2[0].items, u2[s2].field)));
        c2[l2].aggregates = b2;
      }
      return c2;
    }, parseTable: function(i2, r2, u2) {
      var s2 = i2.rows, h2, v2 = [], y2 = [], f2, c2, l2, a2, e2, o2;
      if (!s2.length) return [];
      u2 = u2 || 0;
      switch ((r2 || "").toLowerCase()) {
        case t.headerOption.tHead:
          h2 = i2.tHead.rows[u2];
          break;
        case t.headerOption.row:
        default:
          h2 = i2.rows[u2];
      }
      for (c2 = h2.cells, f2 = 0; f2 < c2.length; f2++) v2.push(n.trim(c2[f2].innerHTML));
      for (f2 = u2 + 1; f2 < s2.length; f2++) {
        for (l2 = {}, a2 = s2[f2].cells, e2 = 0; e2 < a2.length; e2++) o2 = a2[e2].innerHTML, l2[v2[e2]] = typeof o2 == "string" && n.isNumeric(o2) ? Number(o2) : o2;
        y2.push(l2);
      }
      return y2;
    } }, t.headerOption = { tHead: "thead", row: "row" }, t.aggregates = { sum: function(n2, i2) {
      return t.sum(n2, i2);
    }, average: function(n2, i2) {
      return t.avg(n2, i2);
    }, minimum: function(n2, i2) {
      return t.getObject(i2, t.min(n2, i2));
    }, maximum: function(n2, i2) {
      return t.getObject(i2, t.max(n2, i2));
    }, truecount: function(n2, i2) {
      var r2 = t.Predicate(i2, "equal", true);
      return t.DataManager(n2).executeLocal(t.Query().where(r2)).length;
    }, falsecount: function(n2, i2) {
      var r2 = t.Predicate(i2, "equal", false);
      return t.DataManager(n2).executeLocal(t.Query().where(r2)).length;
    }, count: function(n2) {
      return n2.length;
    } }, t.pvt = { filterQueries: a, mergeSort: function(n2, i2, r2) {
      if (n2.length <= 1) return n2;
      var e2 = parseInt(n2.length / 2, 10), u2 = n2.slice(0, e2), f2 = n2.slice(e2);
      return u2 = t.pvt.mergeSort(u2, i2, r2), f2 = t.pvt.mergeSort(f2, i2, r2), t.pvt.merge(u2, f2, i2, r2);
    }, getItemFromComparer: function(n2, i2, r2) {
      var f2, e2, o2, u2 = 0, s2 = typeof v(n2, i2, 0) == "string";
      if (n2.length) while (t.isNullOrUndefined(f2) && u2 < n2.length) f2 = v(n2, i2, u2), o2 = n2[u2++];
      for (; u2 < n2.length; u2++) (e2 = v(n2, i2, u2), t.isNullOrUndefined(e2)) || (s2 && (f2 = +f2, e2 = +e2), r2(f2, e2) > 0 && (f2 = e2, o2 = n2[u2]));
      return o2;
    }, quickSelect: function(n2, i2, r2, u2, f2, e2) {
      if (r2 == u2) return n2[r2];
      var o2 = t.pvt.partition(n2, i2, r2, u2, e2), s2 = o2 - r2 + 1;
      return s2 == f2 ? n2[o2] : f2 < s2 ? t.pvt.quickSelect(n2, i2, r2, o2 - 1, f2, e2) : t.pvt.quickSelect(n2, i2, o2 + 1, u2, f2 - s2, e2);
    }, extractFields: function(n2, i2) {
      var u2 = {}, r2;
      if (i2.length == 1) return t.pvt.getObject(i2[0], n2);
      for (r2 = 0; r2 < i2.length; r2++) u2[i2[r2].replace(".", t.pvt.consts.complexPropertyMerge)] = t.pvt.getObject(i2[r2], n2);
      return u2;
    }, partition: function(n2, i2, r2, u2, f2) {
      var e2 = parseInt((r2 + u2) / 2, 10), s2 = v(n2, i2, e2), o2;
      for (t.swap(n2, e2, u2), e2 = r2, o2 = r2; o2 < u2; o2++) f2(v(n2, i2, o2), s2) && (t.swap(n2, o2, e2), e2++);
      return t.swap(n2, e2, u2), e2;
    }, fnSort: function(n2) {
      return (n2 = n2 ? n2.toLowerCase() : t.sortOrder.Ascending, n2 == t.sortOrder.Ascending) ? t.pvt.fnAscending : t.pvt.fnDescending;
    }, fnGetComparer: function(n2, i2) {
      return function(r2, u2) {
        return i2(t.pvt.getObject(n2, r2), t.pvt.getObject(n2, u2));
      };
    }, fnAscending: function(n2, i2) {
      return t.isNullOrUndefined(i2) && t.isNullOrUndefined(n2) ? -1 : i2 === null || i2 === r ? -1 : typeof n2 == "string" ? n2.localeCompare(i2) : n2 === null || n2 === r ? 1 : n2 - i2;
    }, fnDescending: function(n2, i2) {
      return t.isNullOrUndefined(i2) && t.isNullOrUndefined(n2) ? -1 : i2 === null || i2 === r ? 1 : typeof n2 == "string" ? n2.localeCompare(i2) * -1 : n2 === null || n2 === r ? -1 : i2 - n2;
    }, merge: function(n2, t2, i2, r2) {
      for (var u2 = [], f2; n2.length > 0 || t2.length > 0; ) f2 = n2.length > 0 && t2.length > 0 ? r2 ? r2(v(n2, i2, 0), v(t2, i2, 0)) <= 0 ? n2 : t2 : n2[0][i2] < n2[0][i2] ? n2 : t2 : n2.length > 0 ? n2 : t2, u2.push(f2.shift());
      return u2;
    }, getObject: function(n2, t2) {
      var i2, f2, u2;
      if (!t2) return r;
      if (!n2) return t2;
      if (n2.indexOf(".") === -1) return t2[n2];
      for (i2 = t2, f2 = n2.split("."), u2 = 0; u2 < f2.length; u2++) {
        if (i2 == null) break;
        i2 = i2[f2[u2]];
      }
      return i2;
    }, createObject: function(n2, t2, i2) {
      for (var f2 = n2.split("."), o2 = i2 || window, e2 = o2, u2 = 0; u2 < f2.length; u2++) u2 + 1 == f2.length ? e2[f2[u2]] = t2 === r ? {} : t2 : e2[f2[u2]] == null && (e2[f2[u2]] = {}), e2 = e2[f2[u2]];
      return o2;
    }, ignoreDiacritics: function(n2) {
      if (typeof n2 != "string") return n2;
      var i2 = n2.split(""), r2 = i2.map(function(n3) {
        return n3 in t.data.diacritics ? t.data.diacritics[n3] : n3;
      });
      return r2.join("");
    }, getFieldList: function(n2, i2, u2) {
      if (u2 === r && (u2 = ""), i2 === r || i2 === null) return t.pvt.getFieldList(n2, [], u2);
      for (var f2 in n2) typeof n2[f2] != "object" || n2[f2] instanceof Array ? i2.push(u2 + f2) : t.pvt.getFieldList(n2[f2], i2, u2 + f2 + ".");
      return i2;
    } }, t.FilterOperators = { lessThan: "lessthan", greaterThan: "greaterthan", lessThanOrEqual: "lessthanorequal", greaterThanOrEqual: "greaterthanorequal", equal: "equal", contains: "contains", startsWith: "startswith", endsWith: "endswith", notEqual: "notequal" }, t.data = {}, t.data.operatorSymbols = { "<": "lessthan", ">": "greaterthan", "<=": "lessthanorequal", ">=": "greaterthanorequal", "==": "equal", "!=": "notequal", "*=": "contains", "$=": "endswith", "^=": "startswith" }, t.data.odBiOperator = { "<": " lt ", ">": " gt ", "<=": " le ", ">=": " ge ", "==": " eq ", "!=": " ne ", lessthan: " lt ", lessthanorequal: " le ", greaterthan: " gt ", greaterthanorequal: " ge ", equal: " eq ", notequal: " ne ", "in": " eq ", notin: " ne " }, t.data.odUniOperator = { "$=": "endswith", "^=": "startswith", "*=": "substringof", endswith: "endswith", startswith: "startswith", contains: "substringof", notcontains: "substringof" }, t.data.diacritics = { "Ⓐ": "A", "Ａ": "A", "À": "A", "Á": "A", "Â": "A", "Ầ": "A", "Ấ": "A", "Ẫ": "A", "Ẩ": "A", "Ã": "A", "Ā": "A", "Ă": "A", "Ằ": "A", "Ắ": "A", "Ẵ": "A", "Ẳ": "A", "Ȧ": "A", "Ǡ": "A", "Ä": "A", "Ǟ": "A", "Ả": "A", "Å": "A", "Ǻ": "A", "Ǎ": "A", "Ȁ": "A", "Ȃ": "A", "Ạ": "A", "Ậ": "A", "Ặ": "A", "Ḁ": "A", "Ą": "A", "Ⱥ": "A", "Ɐ": "A", "Ꜳ": "AA", "Æ": "AE", "Ǽ": "AE", "Ǣ": "AE", "Ꜵ": "AO", "Ꜷ": "AU", "Ꜹ": "AV", "Ꜻ": "AV", "Ꜽ": "AY", "Ⓑ": "B", "Ｂ": "B", "Ḃ": "B", "Ḅ": "B", "Ḇ": "B", "Ƀ": "B", "Ƃ": "B", "Ɓ": "B", "Ⓒ": "C", "Ｃ": "C", "Ć": "C", "Ĉ": "C", "Ċ": "C", "Č": "C", "Ç": "C", "Ḉ": "C", "Ƈ": "C", "Ȼ": "C", "Ꜿ": "C", "Ⓓ": "D", "Ｄ": "D", "Ḋ": "D", "Ď": "D", "Ḍ": "D", "Ḑ": "D", "Ḓ": "D", "Ḏ": "D", "Đ": "D", "Ƌ": "D", "Ɗ": "D", "Ɖ": "D", "Ꝺ": "D", "Ǳ": "DZ", "Ǆ": "DZ", "ǲ": "Dz", "ǅ": "Dz", "Ⓔ": "E", "Ｅ": "E", "È": "E", "É": "E", "Ê": "E", "Ề": "E", "Ế": "E", "Ễ": "E", "Ể": "E", "Ẽ": "E", "Ē": "E", "Ḕ": "E", "Ḗ": "E", "Ĕ": "E", "Ė": "E", "Ë": "E", "Ẻ": "E", "Ě": "E", "Ȅ": "E", "Ȇ": "E", "Ẹ": "E", "Ệ": "E", "Ȩ": "E", "Ḝ": "E", "Ę": "E", "Ḙ": "E", "Ḛ": "E", "Ɛ": "E", "Ǝ": "E", "Ⓕ": "F", "Ｆ": "F", "Ḟ": "F", "Ƒ": "F", "Ꝼ": "F", "Ⓖ": "G", "Ｇ": "G", "Ǵ": "G", "Ĝ": "G", "Ḡ": "G", "Ğ": "G", "Ġ": "G", "Ǧ": "G", "Ģ": "G", "Ǥ": "G", "Ɠ": "G", "Ꞡ": "G", "Ᵹ": "G", "Ꝿ": "G", "Ⓗ": "H", "Ｈ": "H", "Ĥ": "H", "Ḣ": "H", "Ḧ": "H", "Ȟ": "H", "Ḥ": "H", "Ḩ": "H", "Ḫ": "H", "Ħ": "H", "Ⱨ": "H", "Ⱶ": "H", "Ɥ": "H", "Ⓘ": "I", "Ｉ": "I", "Ì": "I", "Í": "I", "Î": "I", "Ĩ": "I", "Ī": "I", "Ĭ": "I", "İ": "I", "Ï": "I", "Ḯ": "I", "Ỉ": "I", "Ǐ": "I", "Ȉ": "I", "Ȋ": "I", "Ị": "I", "Į": "I", "Ḭ": "I", "Ɨ": "I", "Ⓙ": "J", "Ｊ": "J", "Ĵ": "J", "Ɉ": "J", "Ⓚ": "K", "Ｋ": "K", "Ḱ": "K", "Ǩ": "K", "Ḳ": "K", "Ķ": "K", "Ḵ": "K", "Ƙ": "K", "Ⱪ": "K", "Ꝁ": "K", "Ꝃ": "K", "Ꝅ": "K", "Ꞣ": "K", "Ⓛ": "L", "Ｌ": "L", "Ŀ": "L", "Ĺ": "L", "Ľ": "L", "Ḷ": "L", "Ḹ": "L", "Ļ": "L", "Ḽ": "L", "Ḻ": "L", "Ł": "L", "Ƚ": "L", "Ɫ": "L", "Ⱡ": "L", "Ꝉ": "L", "Ꝇ": "L", "Ꞁ": "L", "Ǉ": "LJ", "ǈ": "Lj", "Ⓜ": "M", "Ｍ": "M", "Ḿ": "M", "Ṁ": "M", "Ṃ": "M", "Ɱ": "M", "Ɯ": "M", "Ⓝ": "N", "Ｎ": "N", "Ǹ": "N", "Ń": "N", "Ñ": "N", "Ṅ": "N", "Ň": "N", "Ṇ": "N", "Ņ": "N", "Ṋ": "N", "Ṉ": "N", "Ƞ": "N", "Ɲ": "N", "Ꞑ": "N", "Ꞥ": "N", "Ǌ": "NJ", "ǋ": "Nj", "Ⓞ": "O", "Ｏ": "O", "Ò": "O", "Ó": "O", "Ô": "O", "Ồ": "O", "Ố": "O", "Ỗ": "O", "Ổ": "O", "Õ": "O", "Ṍ": "O", "Ȭ": "O", "Ṏ": "O", "Ō": "O", "Ṑ": "O", "Ṓ": "O", "Ŏ": "O", "Ȯ": "O", "Ȱ": "O", "Ö": "O", "Ȫ": "O", "Ỏ": "O", "Ő": "O", "Ǒ": "O", "Ȍ": "O", "Ȏ": "O", "Ơ": "O", "Ờ": "O", "Ớ": "O", "Ỡ": "O", "Ở": "O", "Ợ": "O", "Ọ": "O", "Ộ": "O", "Ǫ": "O", "Ǭ": "O", "Ø": "O", "Ǿ": "O", "Ɔ": "O", "Ɵ": "O", "Ꝋ": "O", "Ꝍ": "O", "Ƣ": "OI", "Ꝏ": "OO", "Ȣ": "OU", "Ⓟ": "P", "Ｐ": "P", "Ṕ": "P", "Ṗ": "P", "Ƥ": "P", "Ᵽ": "P", "Ꝑ": "P", "Ꝓ": "P", "Ꝕ": "P", "Ⓠ": "Q", "Ｑ": "Q", "Ꝗ": "Q", "Ꝙ": "Q", "Ɋ": "Q", "Ⓡ": "R", "Ｒ": "R", "Ŕ": "R", "Ṙ": "R", "Ř": "R", "Ȑ": "R", "Ȓ": "R", "Ṛ": "R", "Ṝ": "R", "Ŗ": "R", "Ṟ": "R", "Ɍ": "R", "Ɽ": "R", "Ꝛ": "R", "Ꞧ": "R", "Ꞃ": "R", "Ⓢ": "S", "Ｓ": "S", "ẞ": "S", "Ś": "S", "Ṥ": "S", "Ŝ": "S", "Ṡ": "S", "Š": "S", "Ṧ": "S", "Ṣ": "S", "Ṩ": "S", "Ș": "S", "Ş": "S", "Ȿ": "S", "Ꞩ": "S", "Ꞅ": "S", "Ⓣ": "T", "Ｔ": "T", "Ṫ": "T", "Ť": "T", "Ṭ": "T", "Ț": "T", "Ţ": "T", "Ṱ": "T", "Ṯ": "T", "Ŧ": "T", "Ƭ": "T", "Ʈ": "T", "Ⱦ": "T", "Ꞇ": "T", "Ꜩ": "TZ", "Ⓤ": "U", "Ｕ": "U", "Ù": "U", "Ú": "U", "Û": "U", "Ũ": "U", "Ṹ": "U", "Ū": "U", "Ṻ": "U", "Ŭ": "U", "Ü": "U", "Ǜ": "U", "Ǘ": "U", "Ǖ": "U", "Ǚ": "U", "Ủ": "U", "Ů": "U", "Ű": "U", "Ǔ": "U", "Ȕ": "U", "Ȗ": "U", "Ư": "U", "Ừ": "U", "Ứ": "U", "Ữ": "U", "Ử": "U", "Ự": "U", "Ụ": "U", "Ṳ": "U", "Ų": "U", "Ṷ": "U", "Ṵ": "U", "Ʉ": "U", "Ⓥ": "V", "Ｖ": "V", "Ṽ": "V", "Ṿ": "V", "Ʋ": "V", "Ꝟ": "V", "Ʌ": "V", "Ꝡ": "VY", "Ⓦ": "W", "Ｗ": "W", "Ẁ": "W", "Ẃ": "W", "Ŵ": "W", "Ẇ": "W", "Ẅ": "W", "Ẉ": "W", "Ⱳ": "W", "Ⓧ": "X", "Ｘ": "X", "Ẋ": "X", "Ẍ": "X", "Ⓨ": "Y", "Ｙ": "Y", "Ỳ": "Y", "Ý": "Y", "Ŷ": "Y", "Ỹ": "Y", "Ȳ": "Y", "Ẏ": "Y", "Ÿ": "Y", "Ỷ": "Y", "Ỵ": "Y", "Ƴ": "Y", "Ɏ": "Y", "Ỿ": "Y", "Ⓩ": "Z", "Ｚ": "Z", "Ź": "Z", "Ẑ": "Z", "Ż": "Z", "Ž": "Z", "Ẓ": "Z", "Ẕ": "Z", "Ƶ": "Z", "Ȥ": "Z", "Ɀ": "Z", "Ⱬ": "Z", "Ꝣ": "Z", "ⓐ": "a", "ａ": "a", "ẚ": "a", "à": "a", "á": "a", "â": "a", "ầ": "a", "ấ": "a", "ẫ": "a", "ẩ": "a", "ã": "a", "ā": "a", "ă": "a", "ằ": "a", "ắ": "a", "ẵ": "a", "ẳ": "a", "ȧ": "a", "ǡ": "a", "ä": "a", "ǟ": "a", "ả": "a", "å": "a", "ǻ": "a", "ǎ": "a", "ȁ": "a", "ȃ": "a", "ạ": "a", "ậ": "a", "ặ": "a", "ḁ": "a", "ą": "a", "ⱥ": "a", "ɐ": "a", "ꜳ": "aa", "æ": "ae", "ǽ": "ae", "ǣ": "ae", "ꜵ": "ao", "ꜷ": "au", "ꜹ": "av", "ꜻ": "av", "ꜽ": "ay", "ⓑ": "b", "ｂ": "b", "ḃ": "b", "ḅ": "b", "ḇ": "b", "ƀ": "b", "ƃ": "b", "ɓ": "b", "ⓒ": "c", "ｃ": "c", "ć": "c", "ĉ": "c", "ċ": "c", "č": "c", "ç": "c", "ḉ": "c", "ƈ": "c", "ȼ": "c", "ꜿ": "c", "ↄ": "c", "ⓓ": "d", "ｄ": "d", "ḋ": "d", "ď": "d", "ḍ": "d", "ḑ": "d", "ḓ": "d", "ḏ": "d", "đ": "d", "ƌ": "d", "ɖ": "d", "ɗ": "d", "ꝺ": "d", "ǳ": "dz", "ǆ": "dz", "ⓔ": "e", "ｅ": "e", "è": "e", "é": "e", "ê": "e", "ề": "e", "ế": "e", "ễ": "e", "ể": "e", "ẽ": "e", "ē": "e", "ḕ": "e", "ḗ": "e", "ĕ": "e", "ė": "e", "ë": "e", "ẻ": "e", "ě": "e", "ȅ": "e", "ȇ": "e", "ẹ": "e", "ệ": "e", "ȩ": "e", "ḝ": "e", "ę": "e", "ḙ": "e", "ḛ": "e", "ɇ": "e", "ɛ": "e", "ǝ": "e", "ⓕ": "f", "ｆ": "f", "ḟ": "f", "ƒ": "f", "ꝼ": "f", "ⓖ": "g", "ｇ": "g", "ǵ": "g", "ĝ": "g", "ḡ": "g", "ğ": "g", "ġ": "g", "ǧ": "g", "ģ": "g", "ǥ": "g", "ɠ": "g", "ꞡ": "g", "ᵹ": "g", "ꝿ": "g", "ⓗ": "h", "ｈ": "h", "ĥ": "h", "ḣ": "h", "ḧ": "h", "ȟ": "h", "ḥ": "h", "ḩ": "h", "ḫ": "h", "ẖ": "h", "ħ": "h", "ⱨ": "h", "ⱶ": "h", "ɥ": "h", "ƕ": "hv", "ⓘ": "i", "ｉ": "i", "ì": "i", "í": "i", "î": "i", "ĩ": "i", "ī": "i", "ĭ": "i", "ï": "i", "ḯ": "i", "ỉ": "i", "ǐ": "i", "ȉ": "i", "ȋ": "i", "ị": "i", "į": "i", "ḭ": "i", "ɨ": "i", "ı": "i", "ⓙ": "j", "ｊ": "j", "ĵ": "j", "ǰ": "j", "ɉ": "j", "ⓚ": "k", "ｋ": "k", "ḱ": "k", "ǩ": "k", "ḳ": "k", "ķ": "k", "ḵ": "k", "ƙ": "k", "ⱪ": "k", "ꝁ": "k", "ꝃ": "k", "ꝅ": "k", "ꞣ": "k", "ⓛ": "l", "ｌ": "l", "ŀ": "l", "ĺ": "l", "ľ": "l", "ḷ": "l", "ḹ": "l", "ļ": "l", "ḽ": "l", "ḻ": "l", "ſ": "l", "ł": "l", "ƚ": "l", "ɫ": "l", "ⱡ": "l", "ꝉ": "l", "ꞁ": "l", "ꝇ": "l", "ǉ": "lj", "ⓜ": "m", "ｍ": "m", "ḿ": "m", "ṁ": "m", "ṃ": "m", "ɱ": "m", "ɯ": "m", "ⓝ": "n", "ｎ": "n", "ǹ": "n", "ń": "n", "ñ": "n", "ṅ": "n", "ň": "n", "ṇ": "n", "ņ": "n", "ṋ": "n", "ṉ": "n", "ƞ": "n", "ɲ": "n", "ŉ": "n", "ꞑ": "n", "ꞥ": "n", "ǌ": "nj", "ⓞ": "o", "ｏ": "o", "ò": "o", "ó": "o", "ô": "o", "ồ": "o", "ố": "o", "ỗ": "o", "ổ": "o", "õ": "o", "ṍ": "o", "ȭ": "o", "ṏ": "o", "ō": "o", "ṑ": "o", "ṓ": "o", "ŏ": "o", "ȯ": "o", "ȱ": "o", "ö": "o", "ȫ": "o", "ỏ": "o", "ő": "o", "ǒ": "o", "ȍ": "o", "ȏ": "o", "ơ": "o", "ờ": "o", "ớ": "o", "ỡ": "o", "ở": "o", "ợ": "o", "ọ": "o", "ộ": "o", "ǫ": "o", "ǭ": "o", "ø": "o", "ǿ": "o", "ɔ": "o", "ꝋ": "o", "ꝍ": "o", "ɵ": "o", "ƣ": "oi", "ȣ": "ou", "ꝏ": "oo", "ⓟ": "p", "ｐ": "p", "ṕ": "p", "ṗ": "p", "ƥ": "p", "ᵽ": "p", "ꝑ": "p", "ꝓ": "p", "ꝕ": "p", "ⓠ": "q", "ｑ": "q", "ɋ": "q", "ꝗ": "q", "ꝙ": "q", "ⓡ": "r", "ｒ": "r", "ŕ": "r", "ṙ": "r", "ř": "r", "ȑ": "r", "ȓ": "r", "ṛ": "r", "ṝ": "r", "ŗ": "r", "ṟ": "r", "ɍ": "r", "ɽ": "r", "ꝛ": "r", "ꞧ": "r", "ꞃ": "r", "ⓢ": "s", "ｓ": "s", "ß": "s", "ś": "s", "ṥ": "s", "ŝ": "s", "ṡ": "s", "š": "s", "ṧ": "s", "ṣ": "s", "ṩ": "s", "ș": "s", "ş": "s", "ȿ": "s", "ꞩ": "s", "ꞅ": "s", "ẛ": "s", "ⓣ": "t", "ｔ": "t", "ṫ": "t", "ẗ": "t", "ť": "t", "ṭ": "t", "ț": "t", "ţ": "t", "ṱ": "t", "ṯ": "t", "ŧ": "t", "ƭ": "t", "ʈ": "t", "ⱦ": "t", "ꞇ": "t", "ꜩ": "tz", "ⓤ": "u", "ｕ": "u", "ù": "u", "ú": "u", "û": "u", "ũ": "u", "ṹ": "u", "ū": "u", "ṻ": "u", "ŭ": "u", "ü": "u", "ǜ": "u", "ǘ": "u", "ǖ": "u", "ǚ": "u", "ủ": "u", "ů": "u", "ű": "u", "ǔ": "u", "ȕ": "u", "ȗ": "u", "ư": "u", "ừ": "u", "ứ": "u", "ữ": "u", "ử": "u", "ự": "u", "ụ": "u", "ṳ": "u", "ų": "u", "ṷ": "u", "ṵ": "u", "ʉ": "u", "ⓥ": "v", "ｖ": "v", "ṽ": "v", "ṿ": "v", "ʋ": "v", "ꝟ": "v", "ʌ": "v", "ꝡ": "vy", "ⓦ": "w", "ｗ": "w", "ẁ": "w", "ẃ": "w", "ŵ": "w", "ẇ": "w", "ẅ": "w", "ẘ": "w", "ẉ": "w", "ⱳ": "w", "ⓧ": "x", "ｘ": "x", "ẋ": "x", "ẍ": "x", "ⓨ": "y", "ｙ": "y", "ỳ": "y", "ý": "y", "ŷ": "y", "ỹ": "y", "ȳ": "y", "ẏ": "y", "ÿ": "y", "ỷ": "y", "ẙ": "y", "ỵ": "y", "ƴ": "y", "ɏ": "y", "ỿ": "y", "ⓩ": "z", "ｚ": "z", "ź": "z", "ẑ": "z", "ż": "z", "ž": "z", "ẓ": "z", "ẕ": "z", "ƶ": "z", "ȥ": "z", "ɀ": "z", "ⱬ": "z", "ꝣ": "z", "Ά": "Α", "Έ": "Ε", "Ή": "Η", "Ί": "Ι", "Ϊ": "Ι", "Ό": "Ο", "Ύ": "Υ", "Ϋ": "Υ", "Ώ": "Ω", "ά": "α", "έ": "ε", "ή": "η", "ί": "ι", "ϊ": "ι", "ΐ": "ι", "ό": "ο", "ύ": "υ", "ϋ": "υ", "ΰ": "υ", "ω": "ω", "ς": "σ" }, t.data.fnOperators = { equal: function(n2, i2, r2, u2) {
      return (u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), r2) ? s(n2) == s(i2) : n2 == i2;
    }, notequal: function(n2, i2, r2, u2) {
      return u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), !t.data.fnOperators.equal(n2, i2, r2);
    }, notin: function(n2, i2, r2) {
      for (var u2 = 0; u2 < i2.length; u2++) if (t.data.fnOperators.notequal(n2, i2[u2], r2) == false) return false;
      return true;
    }, lessthan: function(n2, t2, i2) {
      return i2 ? s(n2) < s(t2) : n2 < t2;
    }, greaterthan: function(n2, t2, i2) {
      return i2 ? s(n2) > s(t2) : n2 > t2;
    }, lessthanorequal: function(n2, t2, i2) {
      return i2 ? s(n2) <= s(t2) : n2 <= t2;
    }, greaterthanorequal: function(n2, t2, i2) {
      return i2 ? s(n2) >= s(t2) : n2 >= t2;
    }, contains: function(n2, i2, r2, u2) {
      return (u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), r2) ? !c(n2) && !c(i2) && s(n2).indexOf(s(i2)) != -1 : !c(n2) && !c(i2) && n2.toString().indexOf(i2) != -1;
    }, notcontains: function(n2, i2, r2, u2) {
      return u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), !t.data.fnOperators.contains(n2, i2, r2);
    }, notnull: function(n2) {
      return n2 !== null;
    }, isnull: function(n2) {
      return n2 === null;
    }, startswith: function(n2, i2, r2, u2) {
      return (u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), r2) ? n2 && i2 && s(n2).startsWith(s(i2)) : n2 && i2 && n2.startsWith(i2);
    }, endswith: function(n2, i2, r2, u2) {
      return (u2 && (n2 = t.pvt.ignoreDiacritics(n2), i2 = t.pvt.ignoreDiacritics(i2)), r2) ? n2 && i2 && s(n2).endsWith(s(i2)) : n2 && i2 && n2.endsWith(i2);
    }, all: function(n2, i2, r2) {
      for (var u2 = 0; u2 < i2.length; u2++) if (t.data.fnOperators[this.operator](n2, i2[u2], r2) == false) return false;
      return true;
    }, any: function(n2, i2, r2) {
      for (var u2 = 0; u2 < i2.length; u2++) if (t.data.fnOperators[this.operator](n2, i2[u2], r2) == true) return true;
      return false;
    }, processSymbols: function(n2) {
      var r2 = t.data.operatorSymbols[n2], i2;
      return r2 && (i2 = t.data.fnOperators[r2], i2) ? i2 : f("Query - Process Operator : Invalid operator");
    }, processOperator: function(n2) {
      var i2 = t.data.fnOperators[n2];
      return i2 ? i2 : t.data.fnOperators.processSymbols(n2);
    } }, t.data.fnOperators["in"] = function(n2, i2, r2) {
      for (var u2 = 0; u2 < i2.length; u2++) if (t.data.fnOperators.equal(n2, i2[u2], r2) == true) return true;
      return false;
    }, t.NotifierArray = function(i2) {
      return p(this, t.NotifierArray) ? (this.array = i2, this._events = n({}), this._isDirty = false, this) : new t.NotifierArray(i2);
    }, t.NotifierArray.prototype = { on: function(n2, t2) {
      this._events.on(n2, t2);
    }, off: function(n2, t2) {
      this._events.off(n2, t2);
    }, push: function(n2) {
      var t2;
      return t2 = p(n2, Array) ? [].push.apply(this.array, n2) : this.array.push(n2), this._raise("add", { item: n2, index: this.length() - 1 }), t2;
    }, pop: function() {
      var n2 = this.array.pop();
      return this._raise("remove", { item: n2, index: this.length() - 1 }), n2;
    }, addAt: function(n2, t2) {
      return this.array.splice(n2, 0, t2), this._raise("add", { item: t2, index: n2 }), t2;
    }, removeAt: function(n2) {
      var t2 = this.array.splice(n2, 1)[0];
      return this._raise("remove", { item: t2, index: n2 }), t2;
    }, remove: function(n2) {
      var t2 = this.array.indexOf(n2);
      return t2 > -1 && (this.array.splice(t2, 1), this._raise("remove", { item: n2, index: t2 })), t2;
    }, length: function() {
      return this.array.length;
    }, _raise: function(t2, i2) {
      this._events.triggerHandler(n.extend({ type: t2 }, i2));
      this._events.triggerHandler({ type: "all", name: t2, args: i2 });
    }, toArray: function() {
      return this.array;
    } }, n.extend(t, t.dataUtil), Array.prototype.forEach = Array.prototype.forEach || function(n2, t2) {
      for (var i2 = 0, r2 = this.length; i2 < r2; ++i2) n2.call(t2, this[i2], i2, this);
    }, Array.prototype.indexOf = Array.prototype.indexOf || function(n2) {
      var i2 = this.length, t2;
      if (i2 === 0) return -1;
      for (t2 = 0; t2 < i2; t2++) if (t2 in this && this[t2] === n2) return t2;
      return -1;
    }, Array.prototype.filter = Array.prototype.filter || function(n2) {
      var i2, u2, t2, r2;
      if (typeof n2 != "function") throw new TypeError();
      for (i2 = [], u2 = arguments[1] || this, t2 = 0; t2 < this.length; t2++) r2 = this[t2], n2.call(u2, r2, t2, this) && i2.push(r2);
      return i2;
    }, String.prototype.endsWith = String.prototype.endsWith || function(n2) {
      return this.slice(-n2.length) === n2;
    }, String.prototype.startsWith = String.prototype.startsWith || function(n2) {
      return this.slice(0, n2.length) === n2;
    }, t.support || (t.support = {}), t.support.stableSort = function() {
      for (var t2 = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16].sort(function() {
        return 0;
      }), n2 = 0; n2 < 17; n2++) if (n2 !== t2[n2]) return false;
      return true;
    }(), t.support.cors = n.support.cors, !n.support.cors && window.XDomainRequest) {
      var ct = /^https?:\/\//i, lt = /^get|post$/i, at = new RegExp("^" + location.protocol, "i"), vt = /\/xml/i;
      n.ajaxTransport("text html xml json", function(n2, t2) {
        if (n2.crossDomain && n2.async && lt.test(n2.type) && ct.test(t2.url) && at.test(t2.url)) {
          var i2 = null, u2 = (t2.dataType || "").toLowerCase();
          return { send: function(f2, e2) {
            i2 = new XDomainRequest();
            /^\d+$/.test(t2.timeout) && (i2.timeout = t2.timeout);
            i2.ontimeout = function() {
              e2(500, "timeout");
            };
            i2.onload = function() {
              var o2 = "Content-Length: " + i2.responseText.length + "\r\nContent-Type: " + i2.contentType, t3 = { code: 200, message: "success" }, f3 = { text: i2.responseText }, n3;
              try {
                if (u2 === "json") try {
                  f3.json = JSON.parse(i2.responseText);
                } catch (h2) {
                  t3.code = 500;
                  t3.message = "parseerror";
                }
                else if (u2 === "xml" || u2 !== "text" && vt.test(i2.contentType)) {
                  n3 = new ActiveXObject("Microsoft.XMLDOM");
                  n3.async = false;
                  try {
                    n3.loadXML(i2.responseText);
                  } catch (h2) {
                    n3 = r;
                  }
                  if (!n3 || !n3.documentElement || n3.getElementsByTagName("parsererror").length) {
                    t3.code = 500;
                    t3.message = "parseerror";
                    throw "Invalid XML: " + i2.responseText;
                  }
                  f3.xml = n3;
                }
              } catch (s2) {
                throw s2;
              } finally {
                e2(t3.code, t3.message, f3, o2);
              }
            };
            i2.onerror = function() {
              e2(500, "error", { text: i2.responseText });
            };
            navigator.userAgent.indexOf("MSIE 9.0") != -1 && (i2.onprogress = function() {
            });
            i2.open(n2.type, n2.url);
            i2.send(t2.data);
          }, abort: function() {
            i2 && i2.abort();
          } };
        }
      });
    }
    n.support.cors = true;
    t.sortOrder = { Ascending: "ascending", Descending: "descending" };
    t.pvt.consts = { GROUPGUID: "{271bbba0-1ee7}", complexPropertyMerge: "_" };
    d = function(n2, t2) {
      t2 && (n2 = h(n2, t2));
      (window.setImmediate || window.setTimeout)(n2, 0);
    };
    t.support.enableLocalizedSort = false;
    var it = function(n2, i2, r2, u2) {
      return t.support.stableSort ? !t.support.enableLocalizedSort && typeof t.pvt.getObject(i2, n2[0] || {}) == "string" && (r2 === t.pvt.fnAscending || r2 === t.pvt.fnDescending) && u2.filter(function(n3) {
        return n3.fn === "onSortBy";
      }).length === 1 ? yt(n2, i2, r2 === t.pvt.fnDescending) : n2.sort(t.pvt.fnGetComparer(i2, r2)) : t.mergeSort(n2, i2, r2);
    }, b = function(i2, r2) {
      for (var f2 = n.grep(r2, function(n2) {
        return n2.fn == "onGroup";
      }), u2 = 0; u2 < f2.length; u2++) if (t.getObject("fieldName", f2[u2].e) == i2) return t.getObject("fn", f2[u2].e);
    }, yt = function(n2, i2, r2) {
      var u2 = Object.prototype.toString;
      Object.prototype.toString = i2.indexOf(".") === -1 ? function() {
        return this[i2];
      } : function() {
        return t.pvt.getObject(i2, this);
      };
      n2 = n2.sort();
      Object.prototype.toString = u2;
      r2 && n2.reverse();
    }, s = function(n2) {
      return n2 ? n2.toLowerCase ? n2.toLowerCase() : n2.toString().toLowerCase() : n2 === 0 || n2 === false ? n2.toString() : "";
    }, v = function(n2, i2, r2) {
      return i2 ? t.pvt.getObject(i2, n2[r2]) : n2[r2];
    }, pt = function(n2) {
      return typeof HTMLElement == "object" ? n2 instanceof HTMLElement : n2 && n2.nodeType === 1 && typeof n2 == "object" && typeof n2.nodeName == "string";
    }, p = function(n2, t2) {
      return n2 instanceof t2;
    }, rt = function(n2, i2, r2, u2) {
      return function(f2) {
        return typeof f2 == "object" && (u2 = f2, f2 = null), new t.TableModel(f2 || n2, i2, r2, u2);
      };
    }, ut = function(t2) {
      return function(i2, r2) {
        var o2, h2, e2, u2, s2;
        for (r2 = r2 || window.ko, r2 || f("Knockout is undefined"), h2 = [], s2 = 0; s2 < t2.length; s2++) {
          o2 = {};
          for (e2 in t2[s2]) e2.startsWith("_") || (o2[e2] = r2.observable(t2[s2][e2]));
          for (e2 in i2) u2 = i2[e2], n.isPlainObject(u2) ? (u2.owner || (u2.owner = o2), u2 = r2.computed(u2)) : u2 = r2.computed(u2, o2), o2[e2] = u2;
          h2.push(o2);
        }
        return r2.observableArray(h2);
      };
    }, ft = 0, et = function(n2) {
      return ft += 1, n2 + ft;
    };
    t.getGuid = function(n2) {
      var i2 = "0123456789abcdef", t2;
      return (n2 || "") + "00000000-0000-4000-0000-000000000000".replace(/0/g, function(n3, r2) {
        if ("crypto" in window && "getRandomValues" in crypto) {
          var u2 = new Uint8Array(1);
          window.crypto.getRandomValues(u2);
          t2 = u2[0] % 16 | 0;
        } else t2 = Math.random() * 16 | 0;
        return i2[r2 === 19 ? t2 & 3 | 8 : t2];
      });
    };
    w = function(n2, t2) {
      return function() {
        var i2 = [].slice.call(arguments, 0);
        return i2.push(this), n2.apply(t2 || this, i2);
      };
    };
    h = function(n2, t2, i2) {
      return "bind" in n2 ? i2 ? n2.bind(t2, i2) : n2.bind(t2) : function() {
        var r2 = i2 ? [i2] : [];
        return r2.push.apply(r2, arguments), n2.apply(t2 || this, r2);
      };
    };
    t.merge = function(n2, t2) {
      n2 && t2 && Array.prototype.push.apply(n2, t2);
    };
    c = function(n2) {
      return n2 === r || n2 === null;
    };
    f = function(n2) {
      try {
        throw new Error(n2);
      } catch (t2) {
        throw t2.message + "\n" + t2.stack;
      }
    };
  }(window.jQuery, window.Syncfusion, window.document), function(n, t) {
    function k() {
      var n2 = {}, t2 = [], i2 = { webkit: /(chrome)[ \/]([\w.]+)/i, safari: /(webkit)[ \/]([\w.]+)/i, msie: /(msie) ([\w.]+)/i, opera: /(opera)(?:.*version|)[ \/]([\w.]+)/i, mozilla: /(mozilla)(?:.*? rv:([\w.]+)|)/i };
      for (var r2 in i2) if (i2.hasOwnProperty(r2) && (t2 = navigator.userAgent.match(i2[r2]), t2)) {
        n2.name = t2[1].toLowerCase();
        n2.version = t2[2];
        !navigator.userAgent.match(/Trident\/7\./) || (n2.name = "msie");
        break;
      }
      return n2.isMSPointerEnabled = n2.name == "msie" && n2.version > 9 && window.navigator.msPointerEnabled, n2.pointerEnabled = window.navigator.pointerEnabled, n2;
    }
    function a(t2, i2, r2) {
      var u2 = r2.type;
      r2.type = i2;
      n.event.dispatch.call(t2, r2);
      r2.type = u2;
    }
    function it(n2, t2) {
      if (t2) for (prop in t2) prop in n2 || (n2[prop] = t2[prop]);
    }
    function o(n2) {
      return n2.originalEvent.touches ? n2.originalEvent.touches[0] : i ? n2.originalEvent : n2;
    }
    function d(n2) {
      var r2 = i ? n2.originalEvent.pointerType : n2.originalEvent.touches ? "touch" : "mouse", u2 = i && l == t ? r2 == 4 ? "mouse" : "touch" : r2;
      return n2.pointerType = u2, n2.type != "mousedown" && n2.type != "mouseup" && it(n2, n2.originalEvent), u2 == "touch" && (n2.button = t), n2;
    }
    function s(t2, i2, r2) {
      var o2, s2, h2, u2 = {}, e2, f2;
      return r2 && (f2 = r2.originalEvent.touches ? [r2.originalEvent.touches[0], t2.originalEvent.changedTouches[0]] : [r2.originalEvent, t2.originalEvent], i2._isSwipe || i2._isdrag ? (o2 = n.event.special.pinch._getdistance(f2[0], f2[1]), s2 = i2.time, e2 = { pageX: i2.stopPoint.pageX, pageY: i2.stopPoint.pageY }) : i2._isPinch && (o2 = n.event.special.pinch.distance(t2), s2 = t2.timeStamp - r2.timeStamp, h2 = i2._pinchDistance), i2._isDelta && (u2._dTime = t2.timeStamp - r2.timeStamp, u2._x = f2[1].pageX - f2[0].pageX, u2._y = f2[1].pageY - f2[0].pageY)), { options: t2, delta: { time: u2._dTime || null, X: u2._x || null, Y: u2._y || null }, distance: o2, scale: i2._isPinch ? h2 : null, time: s2, velocity: { XY: o2 / u2._dTime || null, X: u2._x / u2._dTime || null, Y: u2._y / u2._dTime || null }, currentPosition: { pageX: e2 ? e2.pageX : null, pageY: e2 ? e2.pageY : null } };
    }
    function h() {
      var n2 = /* @__PURE__ */ new Date();
      return n2.getTime();
    }
    function v(n2) {
      i && n2.css("-ms-touch-action", "pinch-zoom").css("touch-action", "pinch-zoom");
    }
    var c = n(document);
    n.each("touchstart touchmove touchend tap doubletap taphold swipe swipeleft swiperight pinch pinchin pinchout pinchstop scrollstart scrollstop".split(" "), function(t2, i2) {
      n.fn[i2] = function(n2) {
        return n2 ? this.on(i2, n2) : this.trigger(i2);
      };
      n.attrFn && (n.attrFn[i2] = true);
    });
    var i = k().isMSPointerEnabled, l = k().pointerEnabled, y = "ontouchstart" in window, rt = "scroll", p = typeof orientation == "undefined", g = navigator.userAgent.match(/iPhone|iPad|iPod/i), u = i ? l ? "pointerdown" : "MSPointerDown" : y ? "touchstart" : "mousedown", f = i ? l ? "pointerup" : "MSPointerUp" : g ? "touchend" : y ? "touchend" : "mouseup", r = i ? l ? "pointermove" : "MSPointerMove" : y ? "touchmove" : "mousemove", w = i ? l ? "pointercancel" : "MSPointerCancel" : y ? "touchcancel" : "mouseleave", ut = i || !p ? u : g ? "touchstart" : "touchstart mousedown", e = i || !p ? f : "touchend mouseup", b = i || !p ? r : "touchmove mousemove", nt = k(), tt = nt.name == "msie" && nt.version == "9.0" ? true : false;
    n.event.special.ejtouchmove = { setup: function() {
      function s2() {
      }
      function h2(n2) {
        if (n2.originalEvent && !(n2.which && n2.which !== 1)) {
          var f2 = n2.target, u2 = n2.originalEvent;
          i && (t2 = { x: u2.x, y: u2.y });
          o2.on(r, l2);
        }
      }
      function l2(n2) {
        if (!(n2.which && n2.which !== 1)) {
          var u2 = n2.target, r2 = n2.originalEvent;
          it(n2, n2.originalEvent);
          (!i || !t2 || Math.abs(t2.x - r2.x) > 10 || Math.abs(t2.y - r2.y) > 10 && i) && a(e2, "ejtouchmove", n2);
        }
      }
      var e2 = this, o2 = n(e2), t2;
      o2.on(u, h2);
      c.on(f, s2);
      t2 = {};
    } };
    n.event.special.swipeupdown = { setup: function() {
      var e2 = this, i2 = n(e2);
      v(i2);
      i2.on(u, function(u2) {
        function l2(n2) {
          if (n2.preventDefault(), e3) {
            var t2 = o(n2);
            h2 = { time: (/* @__PURE__ */ new Date()).getTime(), coords: [t2.pageX, t2.pageY] };
            Math.abs(e3.coords[1] - h2.coords[1]) > 10 && n2.preventDefault();
          }
        }
        if (u2.originalEvent) {
          var a2 = u2, c2 = o(u2), e3 = { time: (/* @__PURE__ */ new Date()).getTime(), coords: [c2.pageX, c2.pageY], origin: n(u2.target) }, h2;
          i2.on(r, l2).one(f, function(u3) {
            if (i2.off(r, l2), e3 && h2 && h2.time - e3.time < 1e3 && Math.abs(e3.coords[1] - h2.coords[1]) > 30 && Math.abs(e3.coords[0] - h2.coords[0]) < 75) {
              var o2 = { time: h2.time - e3.time, _isSwipe: true, _isDelta: true, stopPoint: h2 }, f2 = s(u3, o2, a2);
              e3.origin.trigger(n.extend(true, { type: "swipeupdown" }, f2)).trigger(n.extend(true, { type: e3.coords[1] > h2.coords[1] ? "swipeup" : "swipedown" }, f2));
            }
            e3 = h2 = t;
          });
        }
      });
    } };
    n.event.special.scrollstart = { isEnabled: true, setup: function() {
      function u2(n2, r3) {
        t2 = r3;
        a(i2, t2 ? "scrollstart" : "scrollstop", n2);
      }
      var i2 = this, f2 = n(i2), t2, r2;
      f2.on(rt, function(i3) {
        n.event.special.scrollstart.isEnabled && (t2 || u2(i3, true), clearTimeout(r2), r2 = setTimeout(function() {
          u2(i3, false);
        }, 250));
      });
    } };
    n.event.special.tap = { doubleTapThreshold: 500, tapholdThreshold: 650, canDoubleTap: function(t2) {
      return h() - t2.doubleTapStartTime <= n.event.special.tap.doubleTapThreshold;
    }, setup: function() {
      var u2 = this, i2 = n(u2), t2 = i2.data(), r2;
      v(i2);
      t2.isDoubleTapWait = false;
      t2.stopProcess = false;
      t2.preTouchend = null;
      t2.preTouchstart = null;
      i2.on(ut, function(f2) {
        function o2() {
          clearTimeout(v2);
          i2.off(e, s2);
          tt && c.off(e, s2);
          c.off(e, o2);
          i2.off(w, o2);
          i2.off(b, y2);
          i2.off("dragstart", it2);
        }
        function y2(n2) {
          var t3 = 10, r3 = n2.originalEvent.changedTouches ? n2.originalEvent.changedTouches[0] : n2.originalEvent, u3 = f2.originalEvent.changedTouches ? f2.originalEvent.changedTouches[0] : f2.originalEvent;
          r3.pageX - u3.pageX < t3 && r3.pageX - u3.pageX > -t3 && r3.pageY - u3.pageY < t3 && r3.pageY - u3.pageY > -t3 || (n2.type == "mousemove" || n2.type == "pointermove" && n2.originalEvent.pointerType == "mouse" || n2.type == "MSPointerMove" && n2.originalEvent.pointerType == 4 ? (clearTimeout(v2), i2.off(w, o2), i2.off(b, y2)) : o2());
        }
        function s2(i3) {
          var e2, s3;
          i3.type == "touchend" && (t2.preTouchend = h());
          o2();
          r2 != i3.target && (i3.type == "mouseup" || f2.type == "pointerup" || "MSPointerUp") && (e2 = i3.target, jQuery.contains(r2, e2) ? nt2(i3, r2) : jQuery.contains(e2, r2) || (s3 = n(r2).parents().has(n(e2)).first()[0], ej.isNullOrUndefined(s3) || nt2(i3, s3)));
          g2 === i3.currentTarget && (a(u2, "tap", n.extend(d(i3), { time: h() - t2.startTime })), t2.isDoubleTapWait && n.event.special.tap.canDoubleTap(t2) ? (t2.isDoubleTapWait = false, a(u2, "doubletap", n.extend(d(i3), { time: h() - t2.doubleTapStartTime }))) : (t2.isDoubleTapWait && (t2.isDoubleTapWait = false, t2.doubleTapStartTime = t2.startTime), n.event.special.tap.canDoubleTap(t2) && (t2.isDoubleTapWait = true)));
        }
        function nt2(n2, t3) {
          n2.target = t3;
          n2.toElement = t3;
        }
        function it2() {
          o2();
        }
        var g2, l2, v2, p2, k2;
        if (f2.originalEvent) if ((f2.type == "mousedown" || f2.type == "pointerdown" || "MSPointerDown") && (r2 = f2.target), t2 = i2.data(), t2.startTime = h(), t2.isDoubleTapWait || (t2.doubleTapStartTime = t2.startTime), f2.type == "touchstart" && (t2.preTouchstart = t2.startTime), t2.stopProcess = f2.type == "mousedown" && (t2.startTime - t2.preTouchend < 300 || t2.startTime - t2.preTouchstart < 30) ? true : false, g2 = f2.currentTarget, l2 = f2.originalEvent, f2.which && f2.which !== 1 || t2.stopProcess) t2.stopProcess && (t2.stopProcess = false);
        else {
          i2.on(e, s2);
          c.on(e, o2);
          if (tt) c.on(e, s2);
          i2.on(w, o2);
          i2.on(b, y2);
          i2.on("dragstart", it2);
          p2 = {};
          for (k2 in l2) p2[k2] = l2[k2];
          v2 = setTimeout(function() {
            t2.isDoubleTapWait && (t2.isDoubleTapWait = false);
            a(u2, "taphold", n.extend(d(f2), { options: p2, time: h() - t2.startTime }));
          }, n.event.special.tap.tapholdThreshold);
        }
      });
    } };
    n.event.special.swipe = { scrollSupression: 10, duration: 1e3, horizontalDistance: 30, verticalDistance: 75, pointers: window.navigator.msPointerEnabled, startPoint: function(t2) {
      var i2 = o(t2);
      return { time: (/* @__PURE__ */ new Date()).getTime(), Items: i2, coords: [i2.pageX, i2.pageY], origin: n(t2.target) };
    }, stopPoint: function(n2) {
      var t2 = o(n2);
      return { time: (/* @__PURE__ */ new Date()).getTime(), Items: t2, coords: [t2.pageX, t2.pageY] };
    }, handleSwipe: function(t2, i2, r2, u2) {
      if ((t2.Items.pageY != t2.Items.clientY || i2.Items.pageY != i2.Items.clientY) && (n.event.special.swipe.horizontalDistance = 130), i2.time - t2.time < n.event.special.swipe.duration && Math.abs(t2.coords[0] - i2.coords[0]) > n.event.special.swipe.horizontalDistance && Math.abs(t2.coords[1] - i2.coords[1]) < n.event.special.swipe.verticalDistance) {
        var e2 = { time: i2.time - t2.time, _isSwipe: true, _isDelta: true, stopPoint: i2 }, f2 = s(r2, e2, u2);
        t2.origin.trigger(n.extend(true, { type: "swipe" }, f2)).trigger(n.extend(true, { type: t2.coords[0] > i2.coords[0] ? "swipeleft" : "swiperight" }, f2));
      }
    }, setup: function() {
      var e2 = this, i2 = n(e2);
      v(i2);
      i2.on(u, function(u2) {
        function s2(t2) {
          e3 && (o2 = n.event.special.swipe.stopPoint(t2), Math.abs(e3.coords[0] - o2.coords[0]) > n.event.special.swipe.scrollSupression && t2.preventDefault());
        }
        if (u2.originalEvent) {
          var e3 = n.event.special.swipe.startPoint(u2), o2, h2 = u2;
          n(u2.target).data("_dataTouchStart", { event: u2, _now: (/* @__PURE__ */ new Date()).getTime() });
          i2.on(r, s2).one(f, function(u3) {
            i2.off(r, s2);
            e3 && o2 && n.event.special.swipe.handleSwipe(e3, o2, u3, h2);
            e3 = o2 = t;
          });
        }
      });
    } };
    n.event.special.pinch = { distance: function(t2) {
      return t2.originalEvent.touches.length < 2 ? null : n.event.special.pinch._getdistance(t2.originalEvent.touches[0], t2.originalEvent.touches[1]);
    }, _getdistance: function(n2, t2) {
      return Math.sqrt((n2.pageX - t2.pageX) * (n2.pageX - t2.pageX) + (n2.pageY - t2.pageY) * (n2.pageY - t2.pageY));
    }, setup: function() {
      var e2 = this, i2 = n(e2);
      v(i2);
      i2.on(u, function(u2) {
        var h2;
        if (u2.originalEvent && (h2 = u2, u2.originalEvent.touches && u2.originalEvent.touches.length >= 2)) {
          let a3 = function(t2) {
            l2 = t2;
            e3 = n.event.special.pinch.distance(t2) || null;
            o2 && e3 && Math.abs(o2 - e3) > v2 && (n(t2.target).trigger(n.extend(true, { type: o2 > e3 ? "pinchin" : "pinchout" }, s(t2, { _isPinch: true, _pinchDistance: c2 }, h2))), o2 = e3);
          };
          var a2 = a3;
          var o2 = n.event.special.pinch.distance(u2), e3, v2 = 5, c2 = o2, l2, y2 = s(u2, { _isPinch: true, _pinchDistance: c2 }, h2);
          n(u2.target).trigger(n.extend(true, { type: "pinch" }, y2));
          i2.on(r, a3).one(f, function() {
            i2.off(r, a3);
            n(u2.target).trigger(n.extend(true, { type: "pinchstop" }, s(l2, { _isPinch: true, _pinchDistance: e3 }, h2)));
            o2 = e3 = t;
          });
        }
      });
    } };
    n.event.special.touchdrag = { setup: function() {
      var e2 = this, i2 = n(e2);
      v(i2);
      i2.on(u, function(u2) {
        function c2(t2) {
          h2 && (e3 = o(t2), n.event.special.pinch._getdistance(h2, e3) > 5 && n(t2.target).trigger(n.extend(true, { type: "touchdrag" }, s(t2, { _isdrag: true, stopPoint: e3, _isDelta: true }, l2))));
        }
        if (u2.originalEvent) {
          var h2 = o(u2), e3, l2 = u2;
          n(u2.target).data("_dataTouchStart", { event: u2, _now: (/* @__PURE__ */ new Date()).getTime() });
          i2.on(r, c2).one(f, function() {
            i2.off(r, c2);
            h2 = e3 = t;
          });
        }
      });
    } };
    n.each({ scrollstop: "scrollstart", doubletap: "tap", taphold: "tap", swipeleft: "swipe", swiperight: "swipe", swipedown: "swipeupdown", swipeup: "swipeupdown", pinchin: "pinch", pinchout: "pinch", pinchstop: "pinch" }, function(t2, i2) {
      n.event.special[t2] = { setup: function() {
        n(this).on(i2, n.noop);
      } };
    });
  }(jQuery), function(n, t, i) {
    t.widget("ejDraggable", "ej.Draggable", { element: null, model: null, validTags: ["div", "span", "a"], defaults: { scope: "default", handle: null, dragArea: null, clone: false, distance: 1, dragOnTaphold: false, cursorAt: { top: -1, left: -2 }, dragStart: null, drag: null, dragStop: null, create: null, destroy: null, autoScroll: false, scrollSensitivity: 20, scrollSpeed: 20, helper: function() {
      return n('<div class="e-drag-helper" />').html("draggable").appendTo(document.body);
    } }, _init: function() {
      this.handler = function() {
      };
      this.resizables = {};
      this._wireEvents();
      this._browser = t.browserInfo();
      this._isIE8 = this._browser.name == "msie" && this._browser.version == "8.0";
      this._isIE9 = this._browser.name == "msie" && this._browser.version == "9.0";
      this._browser.name == "msie" && this.element.addClass("e-pinch");
      this._browser.name == "edge" && this.element.css("touch-action", "none");
    }, _setModel: function(n2) {
      for (var t2 in n2) switch (t2) {
        case "dragArea":
          this.model.dragArea = n2[t2];
          break;
        case "dragOnTaphold":
          this.model.dragOnTaphold = n2[t2];
          break;
        case "autoScroll":
          this.model.autoScroll = n2[t2];
      }
    }, _destroy: function() {
      n(document).off(t.eventType.mouseUp, this._destroyHandler).off(t.eventType.mouseUp, this._dragStopHandler).off(t.eventType.mouseMove, this._dragStartHandler).off(t.eventType.mouseMove, this._dragHandler).off("mouseleave", this._dragMouseOutHandler).off("selectstart", false);
      t.widgetBase.droppables[this.scope] = null;
    }, _initialize: function(i2) {
      var r, u;
      if (i2.target && i2.target.nodeName && n(i2.target).closest("input[type='text'], input[type='checkbox'], textarea, select, option").length) return true;
      r = i2;
      i2.preventDefault();
      i2 = this._getCoordinate(i2);
      this.target = n(r.currentTarget);
      this._initPosition = { x: i2.pageX, y: i2.pageY };
      n(document).on(t.eventType.mouseMove, this._dragStartHandler).on(t.eventType.mouseUp, this._destroyHandler);
      this.model.clone || (u = this.element.offset(), this._relXposition = i2.pageX - u.left, this._relYposition = i2.pageY - u.top);
      n(document.documentElement).trigger(t.eventType.mouseDown, r);
    }, _setDragArea: function() {
      var o = n(this.model.dragArea)[0], s, h, u, r, f, e, i2;
      if (o) {
        if (r = ["left", "right", "bottom", "top"], t.isNullOrUndefined(o.getBoundingClientRect)) {
          for (s = n(this.model.dragArea).outerWidth(), h = n(this.model.dragArea).outerHeight(), i2 = 0; i2 < r.length; i2++) this["border-" + r[i2] + "-width"] = 0, this["padding-" + r[i2]] = 0;
          f = e = 0;
        } else {
          for (u = o.getBoundingClientRect(), s = u.width ? u.width : u.right - u.left, h = u.height ? u.height : u.bottom - u.top, i2 = 0; i2 < r.length; i2++) this["border-" + r[i2] + "-width"] = isNaN(parseFloat(n(n(this.model.dragArea)[0]).css("border-" + r[i2] + "-width"))) ? 0 : parseFloat(n(n(this.model.dragArea)[0]).css("border-" + r[i2] + "-width")), this["padding-" + r[i2]] = isNaN(parseFloat(n(n(this.model.dragArea)[0]).css("padding-" + r[i2]))) ? 0 : parseFloat(n(n(this.model.dragArea)[0]).css("padding-" + r[i2]));
          f = n(this.model.dragArea).offset().top;
          e = n(this.model.dragArea).offset().left;
        }
        this._left = t.isNullOrUndefined(n(this.model.dragArea).offset()) ? 0 + this["border-left-width"] + this["padding-left"] : e + this["border-left-width"] + this["padding-left"];
        this._top = t.isNullOrUndefined(n(this.model.dragArea).offset()) ? 0 + this["border-top-width"] + this["padding-top"] : f + this["border-top-width"] + this["padding-top"];
        this._right = e + s - [this["border-right-width"] + this["padding-right"]];
        this._bottom = f + h - [this["border-bottom-width"] + this["padding-bottom"]];
      }
    }, _dragStart: function(r) {
      var a, u, o, e, f, s, h;
      if (r.type == "touchmove" || r.type == "mousemove" && (r.buttons !== i ? r.buttons : r.which) == 1 || this._isIE8 || this._isIE9) {
        u = r;
        r = this._getCoordinate(r);
        this.margins = { left: parseInt(this.element.css("marginLeft"), 10) || 0, top: parseInt(this.element.css("marginTop"), 10) || 0, right: parseInt(this.element.css("marginRight"), 10) || 0, bottom: parseInt(this.element.css("marginBottom"), 10) || 0 };
        this.offset = this.element.offset();
        this.offset = { top: this.offset.top - this.margins.top, left: this.offset.left - this.margins.left };
        this.position = this._getMousePosition(u);
        var c = this._initPosition.x - r.pageX, l = this._initPosition.y - r.pageY, v = Math.sqrt(c * c + l * l);
        if (v >= this.model.distance) {
          if (o = this.model.helper({ sender: u, element: this.target }), !o || t.isNullOrUndefined(o)) return;
          if (e = this.model.handle = this.helper = o, this.model.dragStart && (f = null, u.type == "touchmove" ? (s = u.originalEvent.changedTouches[0], f = document.elementFromPoint(s.clientX, s.clientY)) : f = u.originalEvent.target || u.target, this.model.cursorAt.top == 0 && this.model.cursorAt.left == 0 && (f = this._checkTargetElement(r) || f), this._trigger("dragStart", { event: u, element: this.element, target: f, currentTarget: this._getCurrTarget(r) }))) return this._destroy(), false;
          if (this.model.dragArea ? this._setDragArea() : (this._left = this._top = this._right = this._bottom = 0, this["border-top-width"] = this["border-left-width"] = 0), !t.isNullOrUndefined(e) && e.length > 0) {
            h = e.offsetParent().offset();
            n(document).off(t.eventType.mouseMove, this._dragStartHandler).off(t.eventType.mouseUp, this._destroyHandler).on(t.eventType.mouseMove, this._dragHandler).on(t.eventType.mouseUp, this._dragStopHandler).on("mouseleave", this._dragMouseOutHandler).on("selectstart", false);
            t.widgetBase.droppables[this.model.scope] = { draggable: this.element, helper: e.css({ position: "absolute", left: this.position.left - h.left, top: this.position.top - h.top }), destroy: this._destroyHandler };
          }
        }
      }
      (this.model.autoScroll && r.type == "touchmove" || r.type == "mousemove" && (r.buttons !== i ? r.buttons : r.which) == 1 || this._isIE8 || this._isIE9) && (a = this._getScrollParent(u.target));
    }, _drag: function(i2) {
      var f, e, s, h, r, l, v, u, a, o, c;
      i2.preventDefault();
      this.position = this._getMousePosition(i2);
      this.position.top < 0 && (this.position.top = 0);
      n(document).height() < this.position.top && (this.position.top = n(document).height());
      n(document).width() < this.position.left && (this.position.left = n(document).width());
      o = t.widgetBase.droppables[this.model.scope].helper;
      this.model.drag && (l = null, i2.type == "touchmove" ? (v = i2.originalEvent.changedTouches[0], l = document.elementFromPoint(v.clientX, v.clientY)) : l = i2.originalEvent.target || i2.target, this.model.cursorAt.top == 0 && this.model.cursorAt.left == 0 && (l = this._checkTargetElement(i2) || l), u = { event: i2, element: this.target, target: l, currentTarget: this._getCurrTarget(i2), position: { left: null, top: null } }, this._trigger("drag", u));
      a = this._checkTargetElement(i2);
      t.isNullOrUndefined(a) ? this._hoverTarget && (i2.target = i2.toElement = this._hoverTarget, this._hoverTarget.object._out(i2), this._hoverTarget = null) : (i2.target = i2.toElement = a, a.object._over(i2), this._hoverTarget = a);
      o = t.widgetBase.droppables[this.model.scope].helper;
      c = o.offsetParent().offset();
      s = t.isNullOrUndefined(i2.pageX) || i2.pageX === 0 && i2.type == "touchmove" ? i2.originalEvent.changedTouches[0].pageX : i2.pageX;
      h = t.isNullOrUndefined(i2.pageY) || i2.pageY === 0 && i2.type == "touchmove" ? i2.originalEvent.changedTouches[0].pageY : i2.pageY;
      this.model.dragArea ? (this._pageX != s && (f = this._left > this.position.left ? this._left : this._right < this.position.left + o.outerWidth(true) ? this._right - o.outerWidth(true) : this.position.left), this._pageY != h && (e = this._top > this.position.top ? this._top : this._bottom < this.position.top + o.outerHeight(true) ? this._bottom - o.outerHeight(true) : this.position.top)) : (f = this.position.left, e = this.position.top);
      (e < 0 || e - [c.top + this["border-top-width"]] < 0) && (e = [c.top + this["border-top-width"]]);
      (f < 0 || f - [c.left + this["border-left-width"]] < 0) && (f = [c.left + this["border-left-width"]]);
      f = u && u.position && u.position.left ? u.position.left : f;
      e = u && u.position && u.position.top ? u.position.top : e;
      o.css({ left: u && u.position && u.position.left ? f : f - [c.left + this["border-left-width"]], top: u && u.position && u.position.top ? e : e - [c.top + this["border-top-width"]] });
      this.position.left = f;
      this.position.top = e;
      this._pageX = s;
      this._pageY = h;
      this.model.autoScroll && (r && r != document && r.tagName != "HTML" ? (n(r).offset().top + r.clientHeight - h < this.model.scrollSensitivity ? r.scrollTop = r.scrollTop + this.model.scrollSpeed : h - n(r).offset().top < this.model.scrollSensitivity && (r.scrollTop = r.scrollTop - this.model.scrollSpeed), n(r).offset().left + r.clientWidth - s < this.model.scrollSensitivity ? r.scrollLeft = r.scrollLeft + this.model.scrollSpeed : s - n(r).offset().left < this.model.scrollSensitivity && (r.scrollLeft = r.scrollLeft + this.model.scrollSpeed)) : (h - n(document).scrollTop() < this.model.scrollSensitivity ? n(document).scrollTop(n(document).scrollTop() - this.model.scrollSpeed) : n(window).height() - (h - n(document).scrollTop()) < this.model.scrollSensitivity && n(document).scrollTop(n(document).scrollTop() + this.model.scrollSpeed), s - n(document).scrollLeft() < this.model.scrollSensitivity ? n(document).scrollLeft(n(document).scrollLeft() - this.model.scrollSpeed) : n(window).width() - (s - n(document).scrollLeft()) < this.model.scrollSensitivity && n(document).scrollLeft(n(document).scrollLeft() + this.model.scrollSpeed)));
    }, _dragStop: function(n2) {
      var t2, i2;
      (n2.type == "mouseup" || n2.type == "touchend") && this._destroy(n2);
      this.model.dragStop && (t2 = null, n2.type == "touchend" ? this.model.cursorAt.top == 0 && this.model.cursorAt.left == 0 ? t2 = n2.originalEvent.target || n2.target : (i2 = n2.originalEvent.changedTouches[0], t2 = document.elementFromPoint(i2.clientX, i2.clientY)) : t2 = n2.originalEvent.target || n2.target, this.model.cursorAt.top == 0 && this.model.cursorAt.left == 0 && (t2 = this._checkTargetElement(n2) || t2), this._trigger("dragStop", { event: n2, element: this.target, target: t2, currentTarget: this._getCurrTarget(n2) }));
      this._dragEnd(n2);
    }, _dragEnd: function(n2) {
      var i2 = this._checkTargetElement(n2);
      t.isNullOrUndefined(i2) || (n2.target = n2.toElement = i2, i2.object._drop(n2, this.element));
    }, _dragMouseEnter: function(t2) {
      n(document).off("mouseenter", this._dragMouseEnterHandler);
      this._isIE9 ? this._dragManualStop(t2) : this._isIE8 ? t2.button == 0 && this._dragManualStop(t2) : t2.buttons == 0 && this._dragManualStop(t2);
    }, _dragManualStop: function(n2) {
      this.model.dragStop != null && this._trigger("dragStop", { event: n2, element: this.target, target: n2.originalEvent.target || n2.target, currentTarget: this._getCurrTarget(n2) });
      this._destroy(n2);
    }, _dragMouseOut: function() {
      n(document).on("mouseenter", this._dragMouseEnterHandler);
    }, _checkTargetElement: function(n2) {
      var t2, i2;
      return (n2.type == "touchmove" || n2.type == "touchstart" || n2.type == "touchend" || n2.type == "taphold" ? (i2 = n2.originalEvent.changedTouches[0], t2 = document.elementFromPoint(i2.clientX, i2.clientY)) : t2 = n2.target, this.helper && this._contains(this.helper[0], t2)) ? (this.helper.hide(), t2 = this._elementUnderCursor(n2), this.helper.show(), this._withDropElement(t2)) : this._withDropElement(t2);
    }, _getCurrTarget: function(n2) {
      var i2 = n2.originalEvent && n2.originalEvent.target || n2.target, u, r;
      if (!t.isNullOrUndefined(i2.style)) return u = i2.style.display, this.element.is(i2) && (i2.style.display = "none"), r = null, t.isNullOrUndefined(n2.pageX) || t.isNullOrUndefined(n2.pageY) || (r = document.elementFromPoint(n2.pageX, n2.pageY)), i2.style.display = u, r;
    }, _withDropElement: function(i2) {
      if (i2) {
        var r = n(i2).data("ejDroppable");
        if (t.isNullOrUndefined(r) && (r = this._checkParentElement(n(i2))), !t.isNullOrUndefined(r)) return n.extend(i2, { object: r });
      }
    }, _checkParentElement: function(i2) {
      var u = n(i2).closest(".e-droppable"), r;
      if (u.length > 0 && (r = n(u).data("ejDroppable"), !t.isNullOrUndefined(r))) return r;
    }, _elementUnderCursor: function(n2) {
      return n2.type == "touchmove" || n2.type == "touchstart" || n2.type == "touchend" || n2.type == "taphold" ? document.elementFromPoint(n2.originalEvent.changedTouches[0].clientX, n2.originalEvent.changedTouches[0].clientY) : document.elementFromPoint(n2.clientX, n2.clientY);
    }, _contains: function(t2, i2) {
      try {
        return n.contains(t2, i2) || t2 == i2;
      } catch (r) {
        return false;
      }
    }, _wireEvents: function() {
      t.isDevice() == true && this.model.dragOnTaphold == true ? this._on(this.element, "taphold", this._initialize) : this._on(this.element, t.eventType.mouseDown, this._initialize);
      this._dragStartHandler = n.proxy(this._dragStart, this);
      this._destroyHandler = n.proxy(this._destroy, this);
      this._dragStopHandler = n.proxy(this._dragStop, this);
      this._dragHandler = n.proxy(this._drag, this);
      this._dragMouseEnterHandler = n.proxy(this._dragMouseEnter, this);
      this._dragMouseOutHandler = n.proxy(this._dragMouseOut, this);
    }, _getMousePosition: function(n2) {
      n2 = this._getCoordinate(n2);
      var t2 = this.model.clone ? n2.pageX : n2.pageX - this._relXposition, i2 = this.model.clone ? n2.pageY : n2.pageY - this._relYposition;
      return { left: t2 - [this.margins.left + this.model.cursorAt.left], top: i2 - [this.margins.top + this.model.cursorAt.top] };
    }, _getCoordinate: function(n2) {
      var i2 = n2;
      return (n2.type == "touchmove" || n2.type == "touchstart" || n2.type == "touchend" || n2.type == "taphold" && t.browserInfo().name != "msie") && (i2 = n2.originalEvent.changedTouches[0]), i2;
    }, _getScrollParent: function(n2) {
      return n2 && n2.scrollHeight > n2.clientHeight ? n2 : n2 && n2.parentNode ? this._getScrollParent(n2.parentNode) : void 0;
    } });
  }(jQuery, Syncfusion), function(n, t) {
    t.widget("ejDroppable", "ej.Droppable", { element: null, model: null, validTags: ["div", "span", "a"], dropElements: [], defaults: { accept: null, scope: "default", drop: null, over: null, out: null, create: null, destroy: null }, _init: function() {
      this._mouseOver = false;
      this.dropElements.push(this);
    }, _setModel: function() {
    }, _destroy: function() {
      n(this.element).off("mouseup", n.proxy(this._drop, this));
    }, _over: function(n2) {
      this._mouseOver || (this._trigger("over", n2), this._mouseOver = true);
    }, _out: function(n2) {
      this._mouseOver && (this._trigger("out", n2), this._mouseOver = false);
    }, _drop: function(t2, i) {
      var u = t2.target, f = n(u).parents(".e-droppable"), r;
      for (n(u).hasClass("e-droppable") && f.push(u), r = 0; r < this.dropElements.length; r++) n(f).is(n(this.dropElements[r].element)) && this.dropElements[r]._dropEvent.call(this.dropElements[r], t2, i);
    }, _dropEvent: function(i, r) {
      var u = t.widgetBase.droppables[this.model.scope], f = !t.isNullOrUndefined(u.helper) && u.helper.is(":visible"), e;
      f && i.type == "touchend" && n(u.helper).hide();
      e = this._isDropArea(i);
      f && i.type == "touchend" && n(u.helper).show();
      u && !t.isNullOrUndefined(this.model.drop) && f && e.canDrop && this.model.drop(n.extend(i, { dropTarget: e.target, dragElement: r }, true), u);
    }, _isDropArea: function(t2) {
      var i = { canDrop: true, target: n(t2.target) }, e, r, o, u, f;
      if (t2.type == "touchend") for (e = t2.originalEvent.changedTouches[0], r = document.elementFromPoint(e.clientX, e.clientY), i.canDrop = false, o = n(r).parents(), u = 0; u < this.element.length; u++) {
        if (n(r).is(n(this.element[u]))) i = { canDrop: true, target: n(r) };
        else for (f = 0; f < o.length; f++) if (n(this.element[u]).is(n(o[f]))) {
          i = { canDrop: true, target: n(r) };
          break;
        }
        if (i.canDrop) break;
      }
      return i;
    } });
  }(jQuery, Syncfusion), function(n, t) {
    t.widget("ejResizable", "ej.resizable", { element: null, model: null, validTags: ["div", "span", "a"], defaults: { scope: "default", handle: null, distance: 1, maxHeight: null, maxWidth: null, minHeight: 10, minWidth: 10, cursorAt: { top: 1, left: 1 }, resizeStart: null, resize: null, resizeStop: null, create: null, destroy: null, helper: function() {
      return n('<div class="e-resize-helper" />').html("resizable").appendTo(document.body);
    } }, _init: function() {
      if (this.target = this.element, this._browser = t.browserInfo(), this._isIE8 = this._browser.name == "msie" && this._browser.version == "8.0", this._isIE9 = this._browser.name == "msie" && this._browser.version == "9.0", this.handle != null) n(this.target).delegate(this.handle, t.eventType.mouseDown, n.proxy(this._mousedown, this)).delegate(this.handle, "resizestart", this._blockDefaultActions);
      else n(this.target).on(t.eventType.mouseDown, n.proxy(this._mousedown, this));
      this._resizeStartHandler = n.proxy(this._resizeStart, this);
      this._destroyHandler = n.proxy(this._destroy, this);
      this._resizeStopHandler = n.proxy(this._resizeStop, this);
      this._resizeHandler = n.proxy(this._resize, this);
      this._resizeMouseEnterHandler = n.proxy(this._resizeMouseEnter, this);
    }, _mouseover: function(i) {
      if (n(i.target).hasClass("e-resizable")) {
        n(i.target).css({ cursor: "se-resize" });
        n(this.target).on(t.eventType.mouseDown, n.proxy(this._mousedown, this));
      } else n(this.target).off(t.eventType.mouseDown), n(this.target).css({ cursor: "" });
    }, _blockDefaultActions: function(n2) {
      n2.cancelBubble = true;
      n2.returnValue = false;
      n2.preventDefault && n2.preventDefault();
      n2.stopPropagation && n2.stopPropagation();
    }, _setModel: function() {
    }, _mousedown: function(i) {
      var r = i;
      i = this._getCoordinate(i);
      this.target = n(r.currentTarget);
      this._initPosition = { x: i.pageX, y: i.pageY };
      this._pageX = i.pageX;
      this._pageY = i.pageY;
      n(document).on(t.eventType.mouseMove, this._resizeStartHandler).on(t.eventType.mouseUp, this._destroyHandler);
      return n(document.documentElement).trigger(t.eventType.mouseDown, r), false;
    }, _resizeStart: function(i) {
      var r, h;
      if (n(i.target).hasClass("e-resizable")) {
        i = this._getCoordinate(i);
        var u = this._initPosition.x - i.pageX, f = this._initPosition.y - i.pageY, e, o, s = Math.sqrt(u * u + f * f);
        if (s >= this.model.distance) {
          if (this.model.resizeStart != null && this._trigger("resizeStart", { event: i, element: this.target })) return;
          r = this.model.helper({ element: this.target });
          e = i.pageX - this._pageX + r.outerWidth();
          o = i.pageY - this._pageY + r.outerHeight();
          this._pageX = i.pageX;
          this._pageY = i.pageY;
          h = this.getElementPosition(r);
          n(document).off(t.eventType.mouseMove, this._resizeStartHandler).off(t.eventType.mouseUp, this._destroyHandler).on(t.eventType.mouseMove, this._resizeHandler).on(t.eventType.mouseUp, this._resizeStopHandler).on("mouseenter", this._resizeMouseEnterHandler).on("selectstart", false);
          t.widgetBase.resizables[this.scope] = { resizable: this.target, helper: r.css({ width: e, height: o }), destroy: this._destroyHandler };
        }
      }
    }, _resize: function(n2) {
      var i, r, u, e, f;
      n2 = this._getCoordinate(n2);
      e = this.getElementPosition(t.widgetBase.resizables[this.scope].helper);
      f = this.model.helper({ element: this.target });
      i = n2.pageX - this._pageX + f.outerWidth();
      r = n2.pageY - this._pageY + f.outerHeight();
      this._pageX = n2.pageX;
      this._pageY = n2.pageY;
      i < this.model.minWidth && (u = this.model.minWidth - i, i = this.model.minWidth, this._pageX = n2.pageX + u);
      r < this.model.minHeight && (u = this.model.minHeight - r, r = this.model.minHeight, this._pageY = n2.pageY + u);
      this.model.maxHeight != null && r > this.model.maxHeight && (u = r - this.model.maxHeight, r = this.model.maxHeight, this._pageY = n2.pageY - u);
      this.model.maxWidth != null && i > this.model.maxWidth && (u = i - this.model.maxWidth, i = this.model.maxWidth, this._pageX = n2.pageX - u);
      t.widgetBase.resizables[this.scope].helper.css({ width: i, height: r });
      this._trigger("resize", { element: this.target });
    }, _resizeStop: function(n2) {
      this.model.resizeStop != null && this._trigger("resizeStop", { element: this.target });
      (n2.type == "mouseup" || n2.type == "touchend") && this._destroy(n2);
    }, _resizeMouseEnter: function(n2) {
      this._isIE9 ? this._resizeManualStop(n2) : this._isIE8 ? n2.button == 0 && this._resizeManualStop(n2) : n2.buttons == 0 && this._resizeManualStop(n2);
    }, _resizeManualStop: function(n2) {
      this.model.resizeStop != null && this._trigger("resizeStop", { element: this.target });
      this._destroy(n2);
    }, _destroy: function() {
      n(document).off(t.eventType.mouseUp, this._destroyHandler).off(t.eventType.mouseUp, this._resizeStopHandler).off(t.eventType.mouseMove, this._resizeStartHandler).off(t.eventType.mouseMove, this._resizeHandler).off("mouseenter", this._resizeMouseEnterHandler).off("selectstart", false);
      t.widgetBase.resizables[this.scope] = null;
    }, getElementPosition: function(n2) {
      return n2 != null && n2.length > 0 ? { left: n2[0].offsetLeft, top: n2[0].offsetTop } : null;
    }, _getCoordinate: function(n2) {
      var t2 = n2;
      return (n2.type == "touchmove" || n2.type == "touchstart" || n2.type == "touchend") && (t2 = n2.originalEvent.changedTouches[0]), t2;
    } });
  }(jQuery, Syncfusion), function(n, t, i, r) {
    "use strict";
    t.widget("ejScrollBar", "ej.ScrollBar", { defaults: { orientation: "horizontal", viewportSize: 0, height: 18, width: 18, smallChange: 57, largeChange: 57, value: 0, maximum: 0, minimum: 0, buttonSize: 18, infiniteScrolling: false }, validTags: ["div"], type: "transclude", dataTypes: { buttonSize: "number", smallChange: "number", largeChange: "number" }, observables: ["value"], value: t.util.valueFunction("value"), _enabled: true, content: function() {
      return this._content && this._content.length || (this._content = this.model.orientation === "horizontal" ? this.element.find(".e-hhandle") : this.element.find(".e-vhandle")), this._content;
    }, _init: function() {
      this.element.addClass("e-widget");
      this._ensureScrollers();
      this.content();
      this._setInitialValues();
    }, _setInitialValues: function() {
      var n2 = "X";
      this.model.orientation === t.ScrollBar.Orientation.Horizontal ? this.element.addClass("e-hscrollbar") : (this.element.addClass("e-vscrollbar"), n2 = "Y");
      (this.value() !== 0 || this.model.minimum !== 0) && (this.value() < this.model.minimum && this.value(this.model.minimum), this.scroll(this.value(), "none"));
    }, _ensureScrollers: function() {
      var t2 = n.fn.jquery;
      this.model.height && this.element.height(this.model.height);
      this.model.width && this.element.width(this.model.width);
      this._scrollData || (this._scrollData = this.model.orientation === "vertical" ? this._createScroller("Height", "Y", "Top", "e-v") : this._createScroller("Width", "X", "Left", "e-h"));
    }, _setModel: function(n2) {
      for (var t2 in n2) if (t2 === "value") this.value() && this.scroll(this.value(), "none");
      else {
        this.refresh();
        break;
      }
    }, _createScroller: function(t2, i2, r2, u) {
      var f = {}, o = n.fn.jquery, e;
      return f.dimension = t2, f.xy = i2, f.position = r2, f.css = u, f.uDimension = t2, this._calculateLayout(f), this._createLayout(f), e = this[f.main].find(".e-button"), this._off(e, "mousedown")._on(e, "mousedown", { d: f, step: 1 }, this._spaceMouseDown), this._off(this[f.scroll], "mousedown")._on(this[f.scroll], "mousedown", { d: f }, this._spaceMouseDown), this._off(this[f.handler], "mousedown touchstart")._on(this[f.handler], "mousedown touchstart", { d: f }, this._mouseDown), f;
    }, _createLayout: function(i2) {
      var r2 = "<div class='" + i2.css + "{0}' style='" + i2.dimension + ":{1}px'>{2}</div>", u = n.fn.jquery, f = {}, o, e;
      f[i2.dimension] = i2.modelDim;
      e = t.buildTag("div." + i2.css + "scroll e-box", String.format(r2, "up e-chevron-up_01 e-icon e-box e-button", i2.buttonSize) + String.format(r2, "handlespace", i2.handleSpace, String.format(r2, "handle e-box e-pinch", i2.handle)) + String.format(r2, "down e-chevron-down_01 e-icon e-box e-button", i2.buttonSize), f);
      this.element.append(e);
      this.element.find(".e-vhandle").addClass("e-v-line e-icon");
      this.element.find(".e-hhandle").addClass("e-h-line e-icon");
      o = u === "1.7.1" || u === "1.7.2" ? i2.uDimension.toLowerCase() : "outer" + i2.uDimension;
      this[i2.handler] = this.element.find("." + i2.handler);
      this[i2.handler].css("transition", "none");
      this[i2.scroll] = this[i2.handler].parent();
      this[i2.main] = this[i2.scroll].parent();
      this[i2.main].find(".e-button")["outer" + i2.uDimension](i2.buttonSize);
    }, _calculateLayout: function(n2) {
      var i2, u;
      n2.scrollDim = "scroll" + n2.dimension;
      n2.lPosition = n2.position.toLowerCase();
      n2.clientXy = "page" + n2.xy;
      n2.scrollVal = "scroll" + n2.position;
      n2.scrollOneStepBy = this.model.smallChange;
      n2.modelDim = this.model[n2.dimension = n2.dimension.toLowerCase()];
      n2.handler = n2.css + "handle";
      n2.buttonSize = this.model.buttonSize;
      n2.main = n2.css + "scroll";
      n2.scroll = n2.css + "ScrollSpace";
      n2.handleSpace = n2.modelDim - 2 * n2.buttonSize;
      n2.scrollable = this.model.maximum - this.model.minimum;
      i2 = this.model.height;
      this.model.orientation === "horizontal" && (i2 = this.model.width);
      n2.handle = this.model.viewportSize / (this.model.maximum - this.model.minimum + this.model.viewportSize) * (i2 - 2 * this.model.buttonSize);
      u = !t.isNullOrUndefined(this.model.elementHeight) && typeof this.model.elementHeight == "string" && this.model.elementHeight.indexOf("%") != -1 ? true : false;
      n2.handle < 20 && !u && (n2.handle = 20);
      n2.onePx = n2.scrollable / (n2.handleSpace - n2.handle);
      n2.fromScroller = false;
      n2.up = true;
      n2.vInterval = r;
    }, _updateLayout: function(n2) {
      this.element.height(this.model.height);
      this.element.width(this.model.width);
      var t2 = this.element.find("." + n2.css + "handle"), f = this.element.find("." + n2.css + "handlespace"), u = n2.dimension == "width" ? t2.css("left") : t2.css("top"), i2 = n2.dimension == "width" ? f.outerWidth() : f.outerHeight();
      u !== r && u !== "auto" && (i2 >= n2.handle + parseFloat(u) || (this.model.enableRTL ? t2.css(n2.dimension === "width" ? "left" : "top", parseFloat(i2) - n2.handle) : t2.css(n2.dimension === "width" ? "left" : "top", parseFloat(i2) - n2.handle > 0 ? parseFloat(i2) - n2.handle : 0)));
      this.element.find("." + n2.css + "scroll").css(n2.dimension, n2.modelDim + "px").find(".e-button").css(n2.dimension, this.model.buttonSize).end().find("." + n2.css + "handlespace").css(n2.dimension, n2.handleSpace + "px").find("." + n2.css + "handle").css(n2.dimension, n2.handle + "px");
    }, refresh: function() {
      this._ensureScrollers();
      this.value() && this.scroll(this.value(), "none");
      this._scrollData && (this._calculateLayout(this._scrollData), this._updateLayout(this._scrollData));
    }, scroll: function(n2, i2, r2, u) {
      var o = this._scrollData, f, e;
      if (!r2) {
        if (this.model.orientation === t.ScrollBar.Orientation.Horizontal) {
          if (this._trigger("scroll", { source: i2 || "custom", scrollData: this._scrollData, scrollLeft: n2, originalEvent: u })) return;
        } else if (this._trigger("scroll", { source: i2 || "custom", scrollData: this._scrollData, scrollTop: n2, originalEvent: u })) return;
      }
      this._scrollData && (this._scrollData.enableRTL && (u == "mousemove" || u == "touchmove") && t.browserInfo().name != "msie" ? this.value(-o.scrollable + n2) : this._scrollData.enableRTL && (u == "mousemove" || u == "touchmove") && t.browserInfo().name == "msie" ? this.value(-1 * n2) : this.value(n2), this.content().length > 0 && (this.model.orientation === t.ScrollBar.Orientation.Horizontal ? (f = this.element.find(".e-hhandlespace").width() - this.element.find(".e-hhandle").outerWidth(), n2 = f < (n2 - this.model.minimum) / this._scrollData.onePx ? f : (n2 - this.model.minimum) / this._scrollData.onePx, this._scrollData.enableRTL && (u == "mousemove" || u == "touchmove") && t.browserInfo().name != "msie" && (n2 = f - n2, n2 > 0 ? n2 = n2 * -1 : n2), this._scrollData.enableRTL && (u == "mousemove" || u == "touchmove") && t.browserInfo().name == "msie" && (n2 = -n2), this._scrollData.enableRTL && n2 > 0 && !this._scrollData._scrollleftflag ? n2 = 0 : n2, this._scrollData._scrollleftflag && (n2 > 0 ? n2 = n2 * -1 : n2, this.value(n2)), this.content()[0].style.left = n2 + "px", this._scrollData._scrollleftflag = false) : (e = this.element.find(".e-vhandlespace").height() - this.element.find(".e-vhandle").outerHeight(), n2 = e < (n2 - this.model.minimum) / this._scrollData.onePx ? e : (n2 - this.model.minimum) / this._scrollData.onePx, t.browserInfo().name == "msie" && isNaN(n2) && (n2 = ""), this.content()[0].style.top = n2 + "px")));
    }, _changeTop: function(n2, t2, i2) {
      var u, r2;
      return u = n2.dimension === "height" ? this.value() : this.value(), r2 = u + t2, n2.step = t2, n2.enableRTL && t2 < 0 || t2 > 0 && !n2.enableRTL ? n2.enableRTL ? r2 < this.model.maximum * -1 && (r2 = this.model.maximum * -1) : r2 > this.model.maximum && (r2 = this.model.maximum) : n2.enableRTL ? r2 > this.model.minimum && (r2 = this.model.minimum) : r2 < this.model.minimum && (r2 = this.model.minimum), (r2 !== u || this.model.infiniteScrolling) && this.scroll(r2, i2), r2 !== u;
    }, _mouseUp: function(i2) {
      if (i2.data) {
        var r2 = i2.data.d;
        clearInterval(r2.vInterval);
        i2.type == "touchend" && n(i2.target).removeClass("e-touch");
        i2.type !== "mouseup" && i2.type !== "touchend" && (i2.toElement || i2.relatedTarget || i2.target) || (this._prevY = this._d = this._data = null, this._off(n(document), "mousemove touchmove", this._mouseMove), n(document).off("mouseup touchend", t.proxy(this._mouseUp, this)), r2.fromScroller = false, this[r2.scroll].off("mousemove"), this[r2.handler].off("mousemove").css("transition", ""), i2.data.source !== "thumb" || t.isNullOrUndefined(this.model) || n.when(this.content()).done(t.proxy(function() {
          this._trigger("thumbEnd", { originalEvent: i2, scrollData: r2 });
        }, this)));
        r2.up = true;
      }
    }, _mouseDown: function(i2) {
      if (this._enabled) {
        this._d = i2;
        this._data = this._d.data.d;
        this._data.target = this._d.target;
        this._data.fromScroller = true;
        this[this._data.handler].css("transition", "none");
        this._on(n(document), "mousemove touchmove", { d: this._data, source: "thumb" }, this._mouseMove);
        this._trigger("thumbStart", { originalEvent: this._d, scrollData: this._data });
        n(document).one("mouseup touchend", { d: this._data, source: "thumb" }, t.proxy(this._mouseUp, this));
        i2.type == "touchstart" && n(i2.target).addClass("e-touch");
      }
    }, _mouseCall: function(n2) {
      n2.type = "mouseup";
      this._mouseUp(n2);
    }, _mouseMove: function(i2) {
      var u, f = 0, r2 = parseInt(this[this._data.handler].css(this._data.lPosition)) || 0, o, e;
      if (i2.preventDefault(), o = 1, t.isNullOrUndefined(i2.target.tagName)) {
        if (n(i2.target).is(document)) {
          this._mouseCall(i2);
          return;
        }
      } else if (i2.target.tagName.toLowerCase() === "iframe") {
        this._mouseCall(i2);
        return;
      }
      e = i2.type == "mousemove" ? i2[this._data.clientXy] : i2.originalEvent.changedTouches[0][this._data.clientXy];
      this._prevY && e !== this._prevY && (f = e - this._prevY, this.model.infiniteScrolling ? (r2 = r2 + f, this._data.step = f, (this._data.enableRTL ? r2 > 0 : r2 < 0) && (r2 = 0), r2 * (this._data.enableRTL ? -1 : 1) + this._data.handle >= this._data.handleSpace && (r2 = (this._data.handleSpace - this._data.handle) * (this._data.enableRTL ? -1 : 1)), u = Math.ceil(r2 * this._data.onePx), this.scroll(u, "thumb")) : (u = f * this._data.onePx, this._changeTop(this._data, u, "thumb", this._d)), this._trigger("thumbMove", { originalEvent: i2, direction: this._data.step > 0 ? 1 : -1, scrollData: this._data }));
      o === 1 && (this._prevY = e);
    }, _spaceMouseDown: function(r2) {
      var u, o, f, e;
      if (r2.data && this._enabled && (u = r2.data.d, o = this[u.handler][0].getBoundingClientRect(), r2.which === 1 && r2.target !== this[u.handler][0])) {
        f = r2.data.step ? this.model.smallChange : this.model.largeChange;
        e = r2.data.top || o[u.lPosition];
        r2[u.clientXy] = r2[u.clientXy] || 0;
        r2[u.clientXy] - i.scrollY < e && (f *= -1);
        u.target = r2.target;
        this._changeTop(u, f, f === 3 ? "track" : "button", r2);
        r2.data.step !== 1 && this[u.scroll].mousemove(function() {
          u.up = true;
        });
        u.up = false;
        u.vInterval = setInterval(t.proxy(function() {
          if ((f < 0 ? e + f / u.onePx < r2[u.clientXy] : e + u.handle + f / u.onePx > r2[u.clientXy]) && (u.up = true), u.up) {
            clearInterval(u.vInterval);
            return;
          }
          this._changeTop(u, f, f === 3 ? "track" : "button", r2);
          e = r2.data ? r2.data.top || o[u.lPosition] : o[u.lPosition];
        }, this), 150);
        n(document).one("mouseup", { d: u }, t.proxy(this._mouseUp, this));
        n(document).mouseout({ d: u }, t.proxy(this._mouseUp, this));
      }
    }, _remove: function() {
      this.model.orientation === t.ScrollBar.Orientation.Horizontal && this.element.find(".e-hscroll").remove();
      this.model.orientation === t.ScrollBar.Orientation.Vertical && this.element.find(".e-vscroll").remove();
      this._scrollData = null;
      this._content = null;
    }, _destroy: function() {
      this.element.remove();
    } });
    t.ScrollBar.Orientation = { Horizontal: "horizontal", Vertical: "vertical" };
  }(jQuery, Syncfusion, window), function(n, t, i, r) {
    "use strict";
    t.widget("ejScroller", "ej.Scroller", { _addToPersist: ["scrollLeft", "scrollTop"], defaults: { height: 250, autoHide: false, animationSpeed: 600, width: 0, scrollOneStepBy: 57, buttonSize: 18, scrollLeft: 0, scrollTop: 0, targetPane: null, scrollerSize: 18, enablePersistence: false, enableRTL: r, enableTouchScroll: true, preventDefault: false, enabled: true, create: null, destroy: null, wheelStart: null, wheelMove: null, wheelStop: null }, validTags: ["div"], type: "transclude", dataTypes: { buttonSize: "number", scrollOneStepBy: "number" }, observables: ["scrollTop", "scrollLeft"], scrollTop: t.util.valueFunction("scrollTop"), scrollLeft: t.util.valueFunction("scrollLeft"), keyConfigs: { up: "38", down: "40", left: "37", right: "39", pageUp: "33", pageDown: "34", pageLeft: "ctrl+37", pageRight: "ctrl+39" }, content: function() {
      return !this._contentOffsetParent && this._content && this._content[0] && (this._contentOffsetParent = this._content[0].offsetParent), this._content && this._content.length && this._contentOffsetParent || (this._content = this.element.children().first().addClass("e-content")), this._content;
    }, _setFirst: true, _updateScroll: false, _init: function() {
      t.isNullOrUndefined(this.content()[0]) || (this._isJquery3 = parseInt(n.fn.jquery) >= 3 ? true : false, this._tempWidth = this.model.width, this._prevScrollWidth = this.content()[0].scrollWidth, this._prevScrollHeight = this.content()[0].scrollHeight, this.element.addClass("e-widget"), this.content(), this._browser = t.browserInfo().name, this._wheelStart = true, this._eleHeight = this.model.height, this._eleWidth = this.model.width, this._isNativeScroll = t.isDevice(), this.model.targetPane != null && this.content().find(this.model.targetPane).addClass("e-target-pane"), this.model.enableRTL === r && (this.model.enableRTL = this.element.css("direction") === "rtl"), this.model.autoHide && this._on(this.element, "mousedown", this._mouseDownInitContent), this._ensureScrollers(), this.model.enableRTL && (this.element.addClass("e-rtl"), this._rtlScrollLeftValue = this.content().scrollLeft()), this._isNativeScroll && this.element.addClass("e-native-scroll"), this._on(this.content(), "scroll", this._scroll), this.model.targetPane != null && this._on(this.content().find(this.model.targetPane), "scroll", this._scroll), this.scrollLeft() && this._setScrollLeftValue(this.scrollLeft()), this.scrollTop() && this.scrollTop(this._isJquery3 ? Math.ceil(this.scrollTop()) : this.scrollTop()), this.content().scrollTop(this.scrollTop()), this.model.autoHide && this._autohide(), this.model.enabled ? this.enable() : this.disable(), this._setDimension(), (this._prevScrollWidth !== this.content()[0].scrollWidth || this._prevScrollHeight !== this.content()[0].scrollHeight) && this.refresh());
      this._addActionClass();
      this._isNativeScroll && this._on(this.content(), "scrollstop", this._touchDown);
    }, _mouseDownInitContent: function() {
      this.model.autoHide && this._on(n(document), "mouseup", this._mouseUpContent);
      this.element.addClass("e-scroll-focus");
    }, _addActionClass: function() {
      this._browser == "msie" && (this.content().removeClass("e-pinch e-pan-x e-pan-y"), this._vScrollbar && this._hScrollbar ? this.content().addClass("e-pinch") : this._vScrollbar && !this._hScrollbar ? this.content().addClass("e-pan-x") : this._hScrollbar && !this._vScrollbar && this.content().addClass("e-pan-y"));
    }, _setDimension: function() {
      t.isNullOrUndefined(this.model.height) || typeof this.model.height != "string" || this.model.height.indexOf("%") == -1 || (this._vScroll || this._hScroll ? this.model.height = this._convertPercentageToPixel(parseInt(this._eleHeight), this.element.parent().height()) : n(this.content()[0]).height(""));
      t.isNullOrUndefined(this.model.width) || typeof this.model.width != "string" || this.model.width.indexOf("%") == -1 || (this._hScroll || this._vScroll ? this.model.width = this._convertPercentageToPixel(parseInt(this._eleWidth), this.element.parent().width()) : n(this.content()[0]).width(""));
    }, _setScrollLeftValue: function(n2) {
      this.model.enableRTL && (n2 = t.browserInfo().name == "mozilla" ? n2 < 0 ? n2 : n2 * -1 : !t.isNullOrUndefined(this._rtlScrollLeftValue) && (t.browserInfo().name == "chrome" || this._rtlScrollLeftValue > 0) ? n2 < 0 ? this._rtlScrollLeftValue + n2 : this._rtlScrollLeftValue - n2 : Math.abs(n2));
      this.content().scrollLeft(n2);
    }, _ensureScrollers: function() {
      var u = n.fn.jquery, f;
      if (this.model.height = typeof this.model.height == "string" && this.model.height.indexOf("px") != -1 ? parseInt(this.model.height) : this.model.height, this.model.width = typeof this.model.width == "string" && this.model.width.indexOf("px") != -1 ? parseInt(this.model.width) : this.model.width, this.model.height && this.element.height(this.model.height), this.model.width && this.element.width(this.model.width), this._off(this.content(), "mousedown touchstart"), this.content().length > 0) {
        if (this.isVScroll() ? (this._tempVscrollbar || (this._vScrollbar = this._createScrollbar(t.ScrollBar.Orientation.Vertical, this.isHScroll()), this._tempVscrollbar = this._vScrollbar), this.model.enableTouchScroll && this._on(this.content(), "mousedown touchstart", { d: this._vScrollbar._scrollData }, this._mouseDownOnContent)) : (this._vScrollbar = null, this._tempVscrollbar = this._vScrollbar, this.element.children(".e-vscrollbar").remove()), this.isHScroll() ? (this._tempHscrollbar || (this._hScrollbar = this._createScrollbar(t.ScrollBar.Orientation.Horizontal, this.isVScroll()), this._tempHscrollbar = this._hScrollbar), this.model.enableTouchScroll && this._on(this.content(), "mousedown touchstart", { d: this._hScrollbar._scrollData }, this._mouseDownOnContent)) : (this._hScrollbar = null, this._tempHscrollbar = this._hScrollbar, this.element.children(".e-hscrollbar").remove()), this._vScrollbar || this._hScrollbar || this.content().css({ width: "auto", height: "auto" }), this.element.find(".e-hscroll").length > 0 || this._vScrollbar && this.content().outerHeight(this.content().outerHeight() - 1), u === "1.7.1" || u === "1.7.2" ? (this._contentHeight = "height", this._contentWidth = "width") : (this._contentHeight = "outerHeight", this._contentWidth = "outerWidth"), this._hScroll = this.isHScroll(), this._vScroll = this.isVScroll(), this._hScroll || this._vScroll) {
          if (this.content().addClass("e-content"), f = this._exactElementDimension(this.element), this._elementDimension(f), this.model.targetPane !== null && this.content().find(this.model.targetPane)[0] !== r ? this.content().find(this.model.targetPane)[0].scrollLeft = this.scrollLeft() : !this.isHScroll() && this.element.children(".e-hscrollbar").length > 0 && this._ensureScrollers(), isNaN(this._eleWidth) && this._eleWidth.indexOf("%") > 0 && isNaN(this._eleHeight) && this._eleHeight.indexOf("%") > 0) n(i).on("resize", n.proxy(this._resetScroller, this));
        } else this.content().removeClass("e-content");
        this._setDimension();
        this._parentHeight = n(this.element).parent().height();
        this._parentWidth = n(this.element).parent().width();
      }
    }, _elementDimension: function(n2) {
      this._ElementHeight = n2.height - (this.border_bottom + this.border_top + this.padding_bottom + this.padding_top);
      this.content()[this._contentHeight](this._ElementHeight - (this._hScroll && !this.model.autoHide ? this.model.scrollerSize : this.element.find(".e-hscrollbar").is(":visible") ? this.model.scrollerSize : 0));
      this._ElementWidth = n2.width - (this.border_left + this.border_right + this.padding_left + this.padding_right);
      this.content()[this._contentWidth](this._ElementWidth - (this._vScroll && !this.model.autoHide ? this.model.scrollerSize : this.element.find(".e-vscrollbar").is(":visible") ? this.model.scrollerSize : 0));
    }, _convertPercentageToPixel: function(n2, t2) {
      return Math.floor(n2 * t2 / 100);
    }, isHScroll: function() {
      var u = parseFloat(n.fn.jquery) >= 3 ? Math.ceil(this.element.width()) : this.element.width(), i2 = this.model.width, r2;
      if (t.isNullOrUndefined(this.model.width) || (i2 = typeof this.model.width == "string" && this.model.width.indexOf("%") != -1 ? u : parseFloat(n.fn.jquery) >= 3 && !isNaN(parseFloat(this.model.width)) ? Math.ceil(parseFloat(this.model.width)) : this.model.width), t.isNullOrUndefined(this._tempWidth) || typeof this._tempWidth != "string" || this._tempWidth.indexOf("%") == -1) {
        if (i2 > 0) {
          if (r2 = this.content().find(this.model.targetPane), this.model.targetPane != null && r2.length) return r2[0].scrollWidth + r2.siblings().width() > i2;
          if (this.content()[0].scrollWidth > i2) return true;
          if (this.content()[0].scrollWidth == i2) {
            if (this.model.autoHide && n(this.content()[0]).find("> *").length > 0) return n(this.content()[0]).find("> *")[0].scrollWidth > n(this.content()[0]).width();
            if (n(this.content()[0]).find("> *").length > 0) return n(this.content()[0]).find("> *")[0].scrollWidth > (t.isNullOrUndefined(this._tempVscrollbar) ? i2 : i2 - this.model.scrollerSize);
          }
          return false;
        }
        return false;
      }
      if (t.isNullOrUndefined(this.model.width) || typeof this.model.width != "string" || this.model.width.indexOf("%") == -1) {
        if (this.content()[0].scrollWidth > u) return true;
      } else return this.content()[0].scrollWidth > u;
    }, isVScroll: function() {
      if (t.isNullOrUndefined(this.model.height) || typeof this.model.height != "string" || this.model.height.indexOf("%") == -1) {
        if (this.model.height > 0 && (this.content()[0].scrollHeight > Math.ceil(this.model.height) || this.isHScroll() && (this.content()[0].scrollHeight == this.model.height || this.content()[0].scrollHeight > this.model.height - (this.model.scrollerSize - 2)))) return true;
      } else return this.content()[0].scrollHeight > this.element.outerHeight();
      return false;
    }, _setModel: function(n2) {
      for (var i2 in n2) switch (i2) {
        case "enableRTL":
          n2[i2] ? (this.element.addClass("e-rtl"), this._rtlScrollLeftValue = this.content().scrollLeft(), t.isNullOrUndefined(this._hScrollbar) || (this._hScrollbar._scrollData.enableRTL = true)) : (this.element.removeClass("e-rtl"), t.isNullOrUndefined(this._hScrollbar) || (this._hScrollbar._scrollData.enableRTL = false));
          this._hScrollbar && (this.element.find(".e-hhandle").css("left", 0), this._hScrollbar.value(0));
          break;
        case "preventDefault":
          this.model.preventDefault = n2[i2];
          break;
        case "scrollLeft":
          (parseFloat(t.util.getVal(n2[i2])) < 0 || !this._hScroll) && (n2[i2] = 0);
          this._hScrollbar && (n2[i2] = parseFloat(t.util.getVal(n2[i2])) > this._hScrollbar._scrollData.scrollable ? this._hScrollbar._scrollData.scrollable : parseFloat(t.util.getVal(n2[i2])));
          this._setScrollLeftValue(parseFloat(n2[i2]));
          this.scrollLeft(n2[i2]);
          !this._hScrollbar || this._hScrollbar._scrollData._scrollleftflag && this.model.enableRTL || this.scrollX(n2[i2], true);
          break;
        case "scrollTop":
          this._vScrollbar && (n2[i2] = parseFloat(t.util.getVal(n2[i2])) > this._vScrollbar._scrollData.scrollable ? this._vScrollbar._scrollData.scrollable : parseFloat(t.util.getVal(n2[i2])));
          (parseFloat(n2[i2]) < 0 || !this._vScroll) && (n2[i2] = 0);
          this.content().scrollTop(parseFloat(n2[i2]));
          this.scrollTop(n2[i2]);
          this.scrollY(n2[i2], true);
          break;
        case "touchScroll":
          this.model.enableTouchScroll ? (this._vScrollbar && this._on(this.content(), "mousedown touchstart", { d: this._vScrollbar._scrollData }, this._mouseDownOnContent), this._hScrollbar && this._on(this.content(), "mousedown touchstart", { d: this._hScrollbar._scrollData }, this._mouseDownOnContent)) : this._off(this.content(), "mousedown touchstart");
          break;
        case "scrollOneStepBy":
          this._vScrollbar && (this._vScrollbar._scrollData.scrollOneStepBy = n2[i2], this._vScrollbar.model.smallChange = n2[i2]);
          this._hScrollbar && (this._hScrollbar._scrollData.scrollOneStepBy = n2[i2], this._hScrollbar.model.smallChange = n2[i2]);
          break;
        case "buttonSize":
          this._vScrollbar && (this._vScrollbar.model.buttonSize = this.model.buttonSize);
          this._hScrollbar && (this._hScrollbar.model.buttonSize = this.model.buttonSize);
          this.refresh();
          break;
        case "height":
          this._eleHeight = n2[i2];
          this.refresh();
          break;
        case "width":
          this._eleWidth = n2[i2];
          this.refresh();
          break;
        case "enabled":
          n2[i2] ? this.enable() : this.disable();
          break;
        default:
          this.refresh();
      }
    }, _createScrollbar: function(i2, r2) {
      var c = this, f, o, l, s, a, h = document.createElement("div"), e, u;
      return i2 === t.ScrollBar.Orientation.Vertical ? (o = this.model.scrollerSize, l = t.isNullOrUndefined(this.model.height) || typeof this.model.height != "string" || this.model.height.indexOf("%") == -1 ? f = this.model.height - (r2 ? this.model.scrollerSize : 0) : f = this.element.height() - (r2 ? this.model.scrollerSize : 0), s = this.content()[0].scrollHeight, a = this.scrollTop()) : (o = f = this.model.width - (r2 ? this.model.scrollerSize : 0), l = this.model.scrollerSize, t.isNullOrUndefined(this.model.width) || typeof this.model.width != "string" || this.model.width.indexOf("%") == -1 ? (e = this.content().find(this.model.targetPane), s = this.model.targetPane != null && e.length ? e[0].scrollWidth + e.parent().width() - e.width() : this.content()[0].scrollWidth) : (o = f = this.element.width() - (r2 ? this.model.scrollerSize : 0), s = this.content()[0].scrollWidth), a = this.scrollLeft()), this.element.children(".e-hscrollbar").length > 0 ? n(this.element.children(".e-hscrollbar")).before(h) : this.element.append(h), n(h).ejScrollBar({ elementHeight: c._eleHeight, elementWidth: c._eleWidth, buttonSize: c.model.buttonSize, orientation: i2, viewportSize: f, height: l, width: o, maximum: s - f, value: a, smallChange: this.model.scrollOneStepBy, largeChange: 3 * this.model.scrollOneStepBy, scroll: t.proxy(this._scrollChanged, this), thumbEnd: t.proxy(this._thumbEnd, this), thumbStart: t.proxy(this._thumbStart, this), thumbMove: t.proxy(this._thumbMove, this) }), u = n(h).ejScrollBar("instance"), i2 !== t.ScrollBar.Orientation.Vertical && r2 || this._off(this.element, this._browser == "msie" ? "wheel mousewheel" : "mousewheel DOMMouseScroll", this._mouseWheel)._on(this.element, this._browser == "msie" ? "wheel mousewheel" : "mousewheel DOMMouseScroll", { d: u._scrollData }, this._mouseWheel), i2 === t.ScrollBar.Orientation.Horizontal ? this._scrollXdata = u._scrollData : this._scrollYdata = u._scrollData, i2 === t.ScrollBar.Orientation.Horizontal && this.model.enableRTL && (u._scrollData.enableRTL = true), u._enabled = this.model.enabled, u;
    }, _updateScrollbar: function(i2, r2) {
      var u = i2 === t.ScrollBar.Orientation.Vertical ? this._vScrollbar : this._hScrollbar;
      u && (i2 === t.ScrollBar.Orientation.Vertical ? (u.model.width = this.model.scrollerSize, u.model.height = u.model.viewportSize = this.model.height - (r2 ? this.model.scrollerSize : 0), u.model.maximum = this.content()[0].scrollHeight - u.model.viewportSize, u.model.value = this.scrollTop()) : (u.model.width = u.model.viewportSize = this.model.width - (r2 ? this.model.scrollerSize : 0), u.model.height = this.model.scrollerSize, u.model.maximum = (this.model.targetPane != null && this.content().find(this.model.targetPane).length > 0 ? this.content().find(this.model.targetPane)[0].scrollWidth + (this.content().width() - this.content().find(n(this.model.targetPane)).outerWidth()) : this.content()[0].scrollWidth) - u.model.viewportSize, this.model.enableRTL || (u.model.value = this.scrollLeft())));
    }, _autohide: function() {
      this.model.autoHide ? (this.element.addClass("e-autohide"), this._on(this.element, "mouseenter mouseleave touchstart touchend", this._scrollerHover), n(":hover").filter(this.element[0]).length || this.content().siblings(".e-scrollbar.e-js").hide(), this._elementDimension(this._exactElementDimension(this.element))) : (this.element.removeClass("e-autohide"), this._off(this.element, "mouseenter mouseleave touchstart touchend", this._scrollerHover), this.content().siblings(".e-scrollbar.e-js").show());
    }, _mouseUpContent: function(t2) {
      t2.type == "mouseup" && (this.element.removeClass("e-scroll-focus"), this._autohide(), this._off(n(document), "mouseup", this._mouseUpContent));
    }, _scrollChanged: function(i2) {
      this._updateScroll = true;
      i2.scrollTop !== r ? this.scrollY(i2.scrollTop, true, "", i2.source) : i2.scrollLeft !== r && this.scrollX(i2.scrollLeft, true, "", i2.source);
      this._updateScroll = false;
      var u = this;
      n.when(this.content()).done(t.proxy(function() {
        u._trigger("scrollEnd", { scrollData: i2 });
      }));
    }, _bindBlurEvent: function(r2, u) {
      this._scrollEle = n(r2).data("ejScrollBar");
      this._event = u;
      var f = this;
      this._listener = function() {
        this._scrollEle._off(n(document), "mousemove touchmove", this._scrollEle._mouseMove);
        n(document).off("mouseup touchend", t.proxy(this._scrollEle._mouseUp, this._scrollEle));
        this._scrollEle._prevY = null;
        this._off(n(document), "mousemove touchmove", this._mouseMove);
        this._off(n(document), "mouseup touchend", this._mouseUp);
        this._off(n(i), "blur");
        this._evtData.handler === "e-vhandle" ? this._scrollEle._trigger("thumbEnd", { originalEvent: this._event, scrollData: this._evtData }) : this._scrollEle._trigger("thumbEnd", { originalEvent: this._event, scrollData: this._evtData });
      };
      this._on(n(i), "blur", this._listener);
    }, _thumbStart: function(n2) {
      this._evtData = n2.scrollData;
      var t2 = n2.scrollData.handler === "e-vhandle" ? this.element.find("." + n2.scrollData.handler).closest(".e-scrollbar") : this.element.find("." + n2.scrollData.handler).closest(".e-scrollbar"), t2 = n2.scrollData.handler === "e-vhandle" ? this.element.find("." + n2.scrollData.handler).closest(".e-scrollbar") : this.element.find("." + n2.scrollData.handler).closest(".e-scrollbar");
      this._bindBlurEvent(t2, n2);
      this._trigger("thumbStart", n2);
    }, _thumbMove: function(n2) {
      this._trigger("thumbMove", n2);
    }, _thumbEnd: function(t2) {
      this._trigger("thumbEnd", t2);
      this._off(n(i), "blur");
    }, refresh: function(i2) {
      var r2, u;
      i2 ? (this._tempVscrollbar = null, this.element.children(".e-vscrollbar").remove(), this._tempHscrollbar = null, this.element.children(".e-hscrollbar").remove()) : this.element.find(">.e-content").removeAttr("style");
      t.isNullOrUndefined(this._eleHeight) || typeof this._eleHeight != "string" || this._eleHeight.indexOf("%") == -1 || this._parentHeight == n(this.element).parent().height() || (r2 = this._exactElementDimension(this.element.parent()), r2 = r2.height - (this.border_bottom + this.border_top + this.padding_bottom + this.padding_top), this.model.height = this._convertPercentageToPixel(parseInt(this._eleHeight), r2));
      t.isNullOrUndefined(this._eleWidth) || typeof this._eleWidth != "string" || this._eleWidth.indexOf("%") == -1 || this._parentWidth == n(this.element).parent().width() || (r2 = this._exactElementDimension(this.element.parent()), r2 = r2.width - (this.border_left + this.border_right + this.padding_left + this.padding_right), this.model.width = this._convertPercentageToPixel(parseInt(this._eleWidth), r2));
      this._ensureScrollers();
      u = this.model.scrollLeft;
      this.model.enableRTL ? (this.element.hasClass("e-rtl") || this.element.addClass("e-rtl"), this._rtlScrollLeftValue = this.content().scrollLeft(), u > 0 ? this.content().scrollLeft(this._rtlScrollLeftValue - u) : this._setScrollLeftValue(u)) : this.content().scrollLeft(u);
      (this.scrollTop() && t.isNullOrUndefined(this._vScrollbar) || !t.isNullOrUndefined(this._vScrollbar) && this._vScrollbar && this._vScrollbar._scrollData != null && !this._vScrollbar._scrollData.skipChange) && this.scrollTop(this._isJquery3 ? Math.ceil(this.scrollTop()) : this.scrollTop());
      this.content().scrollTop(this.scrollTop());
      this._vScrollbar && (this._vScrollbar._scrollData.dimension = "Height", this._updateScrollbar(t.ScrollBar.Orientation.Vertical, this._hScroll), this._vScroll && !this._vScrollbar._calculateLayout(this._vScrollbar._scrollData) && this._vScrollbar._updateLayout(this._vScrollbar._scrollData));
      this._hScrollbar && (this._hScrollbar._scrollData.dimension = "Width", this._updateScrollbar(t.ScrollBar.Orientation.Horizontal, this._vScroll), this._hScroll && !this._hScrollbar._calculateLayout(this._hScrollbar._scrollData) && this._hScrollbar._updateLayout(this._hScrollbar._scrollData));
      t.browserInfo().name == "msie" && t.browserInfo().version == "8.0" ? this.element.find(".e-hhandle").css("left", "0px") : this.model.targetPane != null && this._on(this.content().find(this.model.targetPane), "scroll", this._scroll);
      this._addActionClass();
      this._autohide();
    }, _exactElementDimension: function(n2) {
      var i2 = n2.get(0).getBoundingClientRect(), r2 = ["left", "right", "top", "bottom"], u, f, t2;
      for (u = i2.width ? i2.width : i2.right - i2.left, f = i2.height ? i2.height : i2.bottom - i2.top, t2 = 0; t2 < r2.length; t2++) this["border_" + r2[t2]] = isNaN(parseFloat(n2.css("border-" + r2[t2] + "-width"))) ? 0 : parseFloat(n2.css("border-" + r2[t2] + "-width")), this["padding_" + r2[t2]] = isNaN(parseFloat(n2.css("padding-" + r2[t2]))) ? 0 : parseFloat(n2.css("padding-" + r2[t2]));
      return { width: u, height: f };
    }, _keyPressed: function(n2, i2) {
      if (this.model.enabled) {
        if (["input", "select", "textarea"].indexOf(i2.tagName.toLowerCase()) !== -1) return true;
        var r2, u;
        if (["up", "down", "pageUp", "pageDown"].indexOf(n2) !== -1) this._vScrollbar && (t.browserInfo().name == "msie" && this.model.allowVirtualScrolling && this._content.focus(), r2 = this._vScrollbar._scrollData), u = "o";
        else if (["left", "right", "pageLeft", "pageRight"].indexOf(n2) !== -1) this._hScrollbar && (r2 = this._hScrollbar._scrollData), u = "i";
        else return true;
        return r2 ? !this._changeTop(r2, (n2.indexOf(u) < 0 ? -1 : 1) * (n2[0] !== "p" ? 1 : 3) * r2.scrollOneStepBy, "key") : true;
      }
    }, scrollY: function(n2, i2, r2, u, f) {
      var e = this, f;
      if (n2 !== "") {
        if (i2) {
          if (f = { source: u || "custom", scrollData: this._vScrollbar ? this._vScrollbar._scrollData : null, scrollTop: n2, originalEvent: f }, n2 = this._isJquery3 ? Math.ceil(f.scrollTop) : f.scrollTop, this.scrollTop(n2), this._trigger("scroll", f)) return;
          this.content().scrollTop(n2);
          return;
        }
        (t.isNullOrUndefined(r2) || r2 === "") && (r2 = 100);
        this._vScrollbar && (n2 = parseFloat(n2) > this._vScrollbar._scrollData.scrollable ? this._vScrollbar._scrollData.scrollable : parseFloat(n2));
        n2 = this._isJquery3 ? Math.ceil(n2) : n2;
        this.scrollTop(n2);
        this.content().stop().animate({ scrollTop: n2 }, r2, "linear", function() {
          e._trigger("scroll", { source: u || "custom", scrollData: e._vScrollbar ? e._vScrollbar._scrollData : null, scrollTop: n2, originalEvent: f });
        });
      }
    }, scrollX: function(n2, i2, r2, u, f) {
      var o = this, e, s;
      if (n2 !== "") {
        if (this._hScrollbar && (n2 = parseFloat(n2) > this._hScrollbar._scrollData.scrollable ? this._hScrollbar._scrollData.scrollable : parseFloat(n2)), e = t.browserInfo().name, this.model.enableRTL && e != "mozilla" && (n2 < 0 && (n2 = Math.abs(n2)), s = this.model.targetPane != null ? this.content().find(this.model.targetPane)[0] : this.content()[0], f != "mousemove" && f != "touchmove" && e != "msie" && e != "msie" && (n2 = this._hScrollbar._scrollData.scrollable - n2)), this.scrollLeft(n2), i2) {
          if (this._trigger("scroll", { source: u || "custom", scrollData: this._hScrollbar ? this._hScrollbar._scrollData : null, scrollLeft: n2, originalEvent: f })) return;
          this.model.targetPane != null && this.content().find(this.model.targetPane).length ? this.content().find(this.model.targetPane).scrollLeft(n2) : this.content().scrollLeft(n2);
          return;
        }
        (t.isNullOrUndefined(r2) || r2 === "") && (r2 = 100);
        this.model.targetPane != null && this.content().find(this.model.targetPane).length ? this.content().find(this.model.targetPane).stop().animate({ scrollLeft: n2 }, r2, "linear") : this.content().stop().animate({ scrollLeft: n2 }, r2, "linear", function() {
          o._trigger("scroll", { source: u || "custom", scrollData: o._hScrollbar ? o._hScrollbar._scrollData : null, scrollLeft: n2, originalEvent: f });
        });
      }
    }, enable: function() {
      var n2 = this.element.find(".e-vscrollbar,.e-hscrollbar,.e-vscroll,.e-hscroll,.e-vhandle,.e-hhandle,.e-vscroll .e-icon,.e-hscroll .e-icon");
      n2.hasClass("e-disable") && (n2.removeClass("e-disable").attr({ "aria-disabled": false }), this.model.enabled = true);
      this._vScrollbar && (this._vScrollbar._enabled = this.model.enabled);
      this._hScrollbar && (this._hScrollbar._enabled = this.model.enabled);
    }, disable: function() {
      var n2 = this.element.find(".e-vscrollbar,.e-hscrollbar,.e-vscroll,.e-hscroll,.e-vhandle,.e-hhandle,.e-vscroll .e-icon,.e-hscroll .e-icon");
      n2.addClass("e-disable").attr({ "aria-disabled": true });
      this.model.enabled = false;
      this._vScrollbar && (this._vScrollbar._enabled = this.model.enabled);
      this._hScrollbar && (this._hScrollbar._enabled = this.model.enabled);
    }, _changeTop: function(n2, i2, r2, u) {
      var e = Math.ceil(this.model.targetPane != null && n2.dimension != "height" ? this.content().find(this.model.targetPane)[n2.scrollVal]() : this.content()[n2.scrollVal]()), f;
      return n2.dimension == "height" && e == 0 && (e = this.scrollTop() != 0 ? this.scrollTop() : 0), f = e + i2, (n2.enableRTL ? f < n2.scrollable : f > n2.scrollable) && (f = Math.round(n2.scrollable)), (n2.enableRTL ? f > 0 : f < 0) && (f = 0), f !== e && (this["scroll" + n2.xy](f, true, "", r2, u), n2.xy !== "X" || t.isNullOrUndefined(this._hScrollbar) ? t.isNullOrUndefined(this._vScrollbar) || this._vScrollbar.scroll(f, r2, true, u) : this._hScrollbar.scroll(f, r2, true, u)), f !== e;
    }, _mouseWheel: function(t2) {
      var o;
      if ((!this._vScrollbar || !t2.ctrlKey) && (this._vScrollbar || t2.shiftKey) && t2.data && this.model.enabled) {
        var u = 0, f = t2.data.d, r2 = t2, e;
        if (t2 = t2.originalEvent, this._wheelStart && this._trigger("wheelStart", { originalEvent: t2, scrollData: r2.data.d }), this._wheelStart = false, clearTimeout(n.data(this, "timer")), this._wheelx != 1 && (t2.wheelDeltaX == 0 || t2.wheelDeltaY == 0) && (this._wheelx = 1), navigator.platform.indexOf("Mac") == 0 && this._wheelx == 0 && (this._browser == "webkit" || this._browser == "chrome")) return true;
        (this._browser == "mozilla" ? t2.axis == t2.HORIZONTAL_AXIS ? f = this._scrollXdata : this._scrollYdata : this._browser == "msie" ? (t2.type == "wheel" && (u = t2.deltaX / 120), t2.type == "mousewheel" && t2.shiftKey && (f = this._scrollXdata, t2.preventDefault ? t2.preventDefault() : t2.returnValue = false)) : this._wheelx && t2.wheelDeltaX != 0 && t2.wheelDeltaY == 0 && this._scrollXdata && (f = this._scrollXdata), t2.wheelDeltaX == 0 && (this._wheelx = t2.wheelDeltaX), t2.wheelDelta ? (u = this._normalizingDelta(t2), i.opera && parseFloat(i.opera.version, 10) < 10 && (u = -u)) : t2.detail && (u = t2.detail / 3), u) && (r2.originalEvent && (e = r2.originalEvent.wheelDelta && r2.originalEvent.wheelDelta > 0 || r2.originalEvent.detail && r2.originalEvent.detail < 0 ? -1 : 1), this._changeTop(f, u * f.scrollOneStepBy, "wheel", t2) ? (t2.preventDefault ? t2.preventDefault() : r2.preventDefault(), this._trigger("wheelMove", { originalEvent: t2, scrollData: r2.data.d, direction: e })) : (this._trigger("scrollEnd", { originalEvent: t2, scrollData: r2 }), this._wheelx = 0), o = this, n.data(this, "timer", setTimeout(function() {
          o._wheelStart = true;
          o._trigger("wheelStop", { originalEvent: t2, scrollData: r2.data.d, direction: e });
        }, 250)));
      }
    }, _normalizingDelta: function(n2) {
      return navigator.platform.indexOf("Mac") == 0 ? -n2.wheelDelta / 3 : -n2.wheelDelta / 120;
    }, _contentHeightWidth: function() {
      this.content().siblings().css("display") == "block" && this.model.autoHide ? (this._hScroll && this.content()[this._contentHeight](this._ElementHeight - this.model.scrollerSize), this._vScroll && this.content()[this._contentWidth](this._ElementWidth - this.model.scrollerSize)) : this.content().siblings().css("display") == "none" && this.model.autoHide && (this._vScroll || this._hScroll) && (this.content()[this._contentHeight](this._ElementHeight), this.content()[this._contentWidth](this._ElementWidth));
    }, _scrollerHover: function(n2) {
      this.model.enabled && (n2.type != "mouseenter" && n2.type != "touchstart" || this.content().siblings().is(":visible") ? n2.type != "mouseleave" && n2.type != "touchend" || this.element.hasClass("e-scroll-focus") || (this.content().siblings().hide(), this._contentHeightWidth(), this._trigger("scrollHide", { originalEvent: n2 })) : (this.content().siblings().css("display", "block"), this._contentHeightWidth(), this._ensureScrollers(), this._setScrollLeftValue(this.model.scrollLeft), this._trigger("scrollVisible", { originalEvent: n2 })));
    }, _mouseUp: function(r2) {
      if (r2.data) {
        var u = r2.data.d;
        this.model.enableRTL && (r2.type == "mouseup" || r2.type == "touchend") && (this.model.scrollLeft = this._rtlScrollLeftValue - this.model.scrollLeft);
        r2.type !== "mouseup" && r2.type !== "touchend" && (r2.toElement || r2.relatedTarget) || (this.content().css("cursor", "default"), this._off(n(document), "mousemove touchmove"), this._off(this.content(), "touchmove", this._touchMove), this._off(n(document), "mouseup touchend", this._mouseUp), u.fromScroller = false, this._mouseMoved !== true || r2.data.source !== "thumb" || t.isNullOrUndefined(this.model) || (n.when(this.content()).done(t.proxy(function() {
          this._trigger("thumbEnd", { originalEvent: r2, scrollData: u });
        }, this)), this._off(n(i), "blur")));
        u.up = true;
        this._mouseMoved = false;
        i.ontouchmove = null;
      }
    }, _mouseDownOnContent: function(u) {
      var f, s;
      if ((this._startX = u.clientX != r ? u.clientX : u.originalEvent.changedTouches[0].clientX, this._startY = u.clientY != r ? u.clientY : u.originalEvent.changedTouches[0].clientY, this._timeStart = u.timeStamp || Date.now(), this.model.enabled) && (f = u.data.d, this._evtData = u.data, s = f.handler === "e-vhandle" ? this.element.find("." + f.handler).closest(".e-scrollbar") : this.element.find("." + f.handler).closest(".e-scrollbar"), this._bindBlurEvent(s, u), !this._trigger("thumbStart", { originalEvent: u, scrollData: f })) && (u.which != 3 || u.button != 2)) {
        f.fromScroller = true;
        var e = null, o = 1, c = 5, h;
        this._document = n(document);
        this._window = n(i);
        this._mouseMove = function(n2) {
          var l, a, s2;
          if (this.model.enableRTL && this._UpdateScrollLeftValue(u), this._startX + this._startY != n2.clientX + n2.clientY) {
            if (this._relDisX = (this._startx = n2.clientX != r ? n2.clientX : n2.originalEvent.changedTouches[0].clientX) - this._startX, this._relDisY = (this._starty = n2.clientY != r ? n2.clientY : n2.originalEvent.changedTouches[0].clientY) - this._startY, this._duration = (n2.timeStamp || Date.now()) - this._timeStart, this._velocityY = Math.abs(this._relDisY) / this._duration, this._velocityX = Math.abs(this._relDisX) / this._duration, this._swipe = Math.abs(this._relDisX) > Math.abs(this._relDisY) ? this._relDisX > 0 ? "left" : "right" : this._relDisY > 0 ? "up" : "down", !t.isNullOrUndefined(n2.target.tagName) && n2.target.tagName.toLowerCase() === "iframe") {
              n2.type = "mouseup";
              this._mouseUp(n2);
              return;
            }
            if (l = n2.type == "mousemove" ? n2[f.clientXy] : n2.originalEvent.changedTouches[0][f.clientXy], e && l !== e && (this._mouseMoved = true, a = l - e, s2 = this.model[f.scrollVal] - a, o == 1 && Math.abs(a) > c && (h = f.position, o = 0), o == 0 && (e = l), s2 >= 0 && s2 <= f.scrollable && h === f.position)) {
              var v = this._velocityY > 0.5 && this._duration < 50 && f.position == "Top", y = this._velocityX > 0.5 && this._duration < 50 && f.position == "Left", p = (this._velocityY > 0.5 || this._velocityX > 0.5) && this._duration < 50;
              p ? v ? (s2 = Math.abs(this._relDisY) + this._duration * this._velocityY, this._startY > this._starty ? (s2 += this.scrollTop(), s2 > f.scrollable && (s2 = f.scrollable)) : (s2 < this.scrollTop() && (s2 = Math.abs(s2 - this.scrollTop())), s2 > this.scrollTop() && (s2 = 0)), this.scrollTop() <= f.scrollable && this.scrollY(s2, false, this.model.animationSpeed, "thumb")) : y && (s2 = Math.abs(this._relDisX), this._startX > this._startx ? (s2 += this.scrollLeft(), s2 > f.scrollable && (s2 = f.scrollable)) : (s2 -= this.scrollLeft(), s2 = Math.abs(s2), (s2 > f.scrollable || s2 >= this.scrollLeft()) && (s2 = 0)), this.scrollLeft() <= f.scrollable && this.scrollX(s2, false, this.model.animationSpeed, "thumb")) : (this["scroll" + f.xy](s2, true, "", "thumb", n2.type), f.xy === "X" ? this._hScrollbar.scroll(s2, "thumb", true, n2.type) : t.isNullOrUndefined(this._vScrollbar) || this._vScrollbar.scroll(s2, "thumb", true, n2.type), this.content().css("cursor", "pointer"), this._trigger("thumbMove", { originalEvent: n2, direction: this._swipe == "down" || this._swipe == "right" ? 1 : -1, scrollData: f }));
            }
            i.ontouchmove = function(n3) {
              n3 = n3 || i.event;
              n3.preventDefault && n3.preventDefault();
              n3.returnValue = false;
            };
            e == null && (e = l);
            (Math.round(this._content.scrollTop()) == 0 && this._swipe == "down" || (Math.ceil(this._content.scrollTop()) == f.scrollable || Math.ceil(this._content.scrollTop()) + 1 == f.scrollable) && this._swipe == "up") && (this._trigger("scrollEnd", { originalEvent: n2.originalEvent, scrollData: n2 }), i.ontouchmove = null);
          }
        };
        this._trigger("touchStart", { originalEvent: u, direction: this._swipe == "down" || this._swipe == "right" ? 1 : -1, scrollData: this._scrollData, scrollTop: this.content().scrollTop(), scrollLeft: this.content().scrollLeft() });
        this._on(n(document), "mousemove", { d: f, source: "thumb" }, this._mouseMove);
        this._isNativeScroll ? this._on(this.content(), "touchmove", { d: f, source: "thumb" }, this._touchMove) : this._on(n(document), "touchmove", { d: f, source: "thumb" }, this._mouseMove);
        this._on(n(document), "mouseup touchend", { d: f, source: "thumb" }, this._mouseUp);
      }
    }, _touchMove: function() {
      this.content().css("cursor", "pointer");
      this._mouseMoved = true;
      this._tempLeft = this.model.targetPane != null ? this.content().find(this.model.targetPane).scrollLeft() : this.content().scrollLeft();
      this._tempTop = this.content().scrollTop();
    }, _touchDown: function(n2) {
      var t2;
      t2 = this._tempLeft != this.scrollLeft() ? this._scrollXdata : this._tempTop != this.scrollTop() ? this._scrollYdata : this._scrollYdata ? this._scrollYdata : this._scrollXdata;
      this._trigger("scrollStop", { source: "thumb", originalEvent: n2, scrollData: t2, scrollTop: this.content().scrollTop(), scrollLeft: this.content().scrollLeft() });
    }, _speedScrolling: function(n2) {
      var r2, i2, u, n2, t2;
      if (this._mouseMoved) {
        if (this.element.find(".e-vhandle").length > 0 && (r2 = this.content().scrollTop(), this._tempTop !== r2 && (this._trigger("thumbMove", { originalEvent: n2, direction: this._swipe == "down" || this._swipe == "right" ? 1 : -1, scrollData: this._scrollData }), this._vScrollbar.scroll(this.content().scrollTop(), "thumb", true, "touchmove"), n2 = { source: "thumb", scrollData: this._vScrollbar ? this._vScrollbar._scrollData : null, scrollTop: this.content().scrollTop(), originalEvent: n2 }, t2 = this._isJquery3 ? Math.ceil(n2.scrollTop) : n2.scrollTop, this.scrollTop(t2), this._trigger("scroll", n2)))) return;
        if (this.element.find(".e-hhandle").length > 0 && (i2 = this.model.targetPane != null ? this.content().find(this.model.targetPane) : this.content(), u = i2.scrollLeft(), this._tempLeft !== u && (this._trigger("thumbMove", { originalEvent: n2, direction: this._swipe == "down" || this._swipe == "right" ? 1 : -1, scrollData: this._scrollData }), this._hScrollbar.scroll(i2.scrollLeft(), "thumb", true, "touchmove"), n2 = { source: "thumb", scrollData: this._hScrollbar ? this._hScrollbar._scrollData : null, scrollLeft: this.content().scrollLeft(), originalEvent: n2 }, t2 = this._isJquery3 ? Math.ceil(n2.scrollLeft) : n2.scrollLeft, this.scrollLeft(t2), this._trigger("scroll", n2)))) return;
        this.content().css("cursor", "pointer");
      }
    }, _scroll: function(r2) {
      var s = [this._vScrollbar ? this._vScrollbar._scrollData : null, this._hScrollbar ? this._hScrollbar._scrollData : null], h, f, u, o, e;
      for (this._evtData && (h = this._evtData.d ? this._evtData.d : this._evtData), f = 0; f < 2; f++) (u = s[f], u && !u.skipChange) && ((this.model && (!this.model.targetPane || this.model.targetPane && h && h.xy != "X") && (u.dimension === "height" ? this.scrollTop(r2.target[u.scrollVal]) : this.scrollLeft(r2.target[u.scrollVal])), u.sTop = this.model && this.model.targetPane != null && f == 1 && this.content().find(this.model.targetPane).length ? this.content().find(this.model.targetPane)[0][u.scrollVal] : u.scrollVal == "scrollTop" ? this.scrollTop() : this.scrollLeft(), this[u.scrollVal](u.sTop), u.fromScroller) || (f === 1 ? (o = this.content()[0], this._rtlScrollLeftValue && o.scrollWidth - o.clientWidth != this._rtlScrollLeftValue && (this._rtlScrollLeftValue = o.scrollWidth - o.clientWidth), u.sTop = this.model && t.browserInfo().name != "mozilla" && this.model.enableRTL && !this._hScrollbar._scrollData._scrollleftflag ? this._rtlScrollLeftValue == 0 ? u.sTop * -1 : u.sTop - this._rtlScrollLeftValue : u.sTop, this._hScrollbar.scroll(u.sTop, "", true)) : this._vScrollbar.scroll(u.sTop, "", true), (s.length == 2 && f == 1 || s.length == 1 && f == 0) && (this._externalScroller = false, this.model && this._trigger("scroll", { source: "custom", scrollData: this._hScrollbar ? this._hScrollbar._scrollData : null, scrollLeft: this.scrollLeft(), originalEvent: r2 }))));
      this._isNativeScroll && this.model.enableTouchScroll && this._speedScrolling(r2);
      this._UpdateScrollLeftValue(r2);
      e = this;
      this._vScrollbar && this._scrollYdata && this.model && this._scrollYdata.scrollable - this.model.scrollOneStepBy >= this.scrollTop() && (n(":hover").filter(this.element[0]).length || e._off(t.getScrollableParents(e.wrapper), "scroll", null), i.onmousewheel = function(t2) {
        e.model && e.model.preventDefault && n(":hover").filter(e.element[0]).length && t2.preventDefault();
      });
    }, _UpdateScrollLeftValue: function(n2) {
      this.model && n2.type != "touchstart" && n2.type != "mousedown" && this.model.enableRTL && this._rtlScrollLeftValue && this.model.scrollLeft != this._previousScrollLeft && (this.model.scrollLeft = this._rtlScrollLeftValue - this.model.scrollLeft, this._previousScrollLeft = this.model.scrollLeft);
      (this.model && n2.type == "touchstart" || n2.type == "mousedown") && this.model.enableRTL && (this.model.scrollLeft = this.content().scrollLeft(), this.option("scrollLeft", this.content().scrollLeft()));
    }, _changevHandlerPosition: function(n2) {
      var t2 = this._vScrollbar;
      t2 && (n2 = t2._scrollData != null && n2 >= t2._scrollData.scrollable ? t2._scrollData.scrollable : n2, t2 != null && n2 >= 0 && n2 <= t2._scrollData.scrollable && t2[t2._scrollData.handler].css(t2._scrollData.lPosition, n2 / t2._scrollData.onePx + "px"));
    }, _changehHandlerPosition: function(n2) {
      var t2 = this._hScrollbar;
      t2 && (n2 = t2._scrollData != null && n2 >= t2._scrollData.scrollable ? t2._scrollData.scrollable : n2, t2 != null && top >= 0 && n2 <= t2._scrollData.scrollable && t2[t2._scrollData.handler].css(t2._scrollData.lPosition, n2 / t2._scrollData.onePx + "px"));
    }, _destroy: function() {
      this._off(this.content(), "scrollstop", this._touchDown);
      this._off(n(document), "mouseup", this._mouseUpContent);
      this.element.css({ width: "", height: "" }).children(".e-vscrollbar,.e-hscrollbar").remove();
      this.content().removeClass("e-content").css({ width: "", height: "" });
      this.element.removeClass("e-widget");
    }, _preventDefault: function(n2) {
      n2 = n2 || i.event;
      n2.preventDefault && n2.preventDefault();
      n2.returnValue = false;
    } });
  }(jQuery, Syncfusion, window), function(n, t) {
    function ft(n2, t2) {
      var s2 = n2 || "", k2 = y, t2 = t2.toString(), ct2 = t2.indexOf(".") > -1 || n2.indexOf(".") > -1, r2 = 0, rt2 = 0, i2 = "", d2 = "", a2 = n2.split(","), ut2 = "0", lt2, at2 = n2.toLowerCase().indexOf("e"), w2, o2, ft2 = s2.indexOf("#"), v2, st2, b2, l2, p2, nt2, tt2, f2, u2, c2, e2;
      if (n2.indexOf("\\") > -1 && (d2 = n2.substr(0, n2.lastIndexOf("\\") + 1), n2 = n2.substr(n2.lastIndexOf("\\") + 1, n2.length), ft2 = n2.indexOf("#")), at2 > -1) {
        for (v2 = "", s2 = "", o2 = n2.toLowerCase().split("e"), lt2 = n2.indexOf("+") > -1 ? n2.split("+")[1] : n2.split("-")[1], t2 = parseInt(t2).toExponential(), w2 = t2.split("e"), r2 = o2[1].length - w2[1].length, u2 = o2[1].length - 1; u2 > 0; u2--) o2[1][u2] != "0" ? s2 += o2[1][u2] : r2 > 1 ? (s2 += "#", r2--) : s2 += "0";
        for (st2 = n2.indexOf("+") > -1 ? "+" : "", s2 = st2 + s2.split("").reverse().join(""), u2 = 0; u2 < w2[0].length; u2++) v2 = w2[0][u2] != "." ? v2.concat("#") : v2.concat(".");
        v2.length > o2[0].length && (v2 = o2[0]);
        s2 = d2 + v2 + "e" + s2;
      } else if (ct2) {
        if (o2 = n2.split("."), w2 = t2.split("."), o2[1] = o2[1].replace(/[,.]/g, ""), r2 = o2[0].replace(/[,.]/g, "").length - w2[0].replace(/[,.]/g, "").length, r2 < 0 && ej.isNullOrUndefined(n2.match(/[\[\(\)\]]/g))) {
          for (a2 = o2[0].split(","), i2 = o2[0].split(","), e2 = a2.length - 1; e2 >= 0; e2--) if (a2[e2]) for (f2 = a2[e2].length, u2 = 0, c2 = Math.abs(r2); u2 < c2; u2++) {
            if (f2 === 3) break;
            i2[e2] = "0" + i2[e2];
            f2++;
            r2++;
          }
          if (i2 = i2.join(), r2 < 0) for (ej.isNullOrUndefined(f2) || f2 == 3 || (i2 = "," + i2), u2 = 0, c2 = Math.abs(r2); u2 < c2; u2++) f2 === 3 && (i2 = "," + i2, f2 = 0), i2 = "0" + i2, f2++;
          r2 = 0;
          s2 = d2 + i2 + "." + o2[1];
        } else if (ej.isNullOrUndefined(n2.match(/[\[\(\)\]]/g))) {
          for (i2 = o2[0].replace(/[,.]/g, ""), b2 = "", f2 = 0, l2 = i2.length - 1; l2 >= 0; l2--) f2 === 3 ? (b2 = "," + b2, f2 = 0) : f2++, b2 = i2[l2] + b2;
          s2 = d2 + b2 + "." + o2[1];
        }
      } else {
        if (p2 = 0, nt2 = a2.splice(1, a2.length), r2 = n2.replace(/[,.\[\(\]\)]/g, "").length - t2.replace(/[,.]/g, "").length, ft2 > -1) {
          for (tt2 = 0, c2 = n2.length; tt2 < c2; tt2++) n2[tt2] === "#" && p2++;
          (p2 === 1 || a2[1] && p2 === 2) && (ut2 = "#");
          p2 === 1 && (nt2 = a2[0]);
        }
        if (r2 < 0) {
          for (o2 = s2.split(","), i2 = o2.splice(1, o2.length), e2 = a2.length - 1; e2 >= 0; e2--) if (nt2[e2]) for (f2 = nt2[e2].length, i2[e2] || (i2[e2] = ""), u2 = 0, c2 = Math.abs(r2) + 1; u2 < c2; u2++) {
            if (p2 != 1 && f2 === 3) {
              f2 = 0;
              break;
            }
            i2[e2] = i2[e2].concat(ut2);
            f2++;
            r2++;
          }
          if (i2 = i2.join(), r2 < 0) for (ej.isNullOrUndefined(f2) || f2 == 3 || (i2 = "," + i2), u2 = 0, c2 = Math.abs(r2) + 1; u2 < c2; u2++) p2 != 1 && f2 === 3 && (i2 = "," + i2, f2 = 0), i2 = ut2 + i2, f2++;
          r2 = 0;
          s2 = d2 + i2;
        }
        rt2 = 0;
      }
      for (var et2 = [], vt2 = s2.split(""), it2 = 0, l2 = 0, h2, g2, ot2 = false, ht2 = false, yt2 = n2.indexOf("\\"); l2 < s2.length; l2++) if (h2 = vt2[l2], h2 === "e" && (ht2 = true), h2 === "0" && ft2 < 0 ? r2 > 0 && rt2 <= l2 ? (r2--, rt2++) : r2 > 0 ? r2-- : g2 = k2[h2] : h2 == "0" && (ht2 || h2 != "0") || (g2 = k2[h2]), h2 === "0" && yt2 > -1 && (g2 = k2[h2]), l2 === s2.lastIndexOf("\\") && (ot2 = false), g2 && !ot2) et2[it2] = { rule: g2 }, it2 += 1;
      else for (h2 === "\\" && (h2 = "", l2 === s2.lastIndexOf("\\") || (ot2 = true)), h2 = h2.split(""), e2 = 0; e2 < h2.length; e2++) et2[it2] = h2[e2], it2 += 1;
      return k2 = et2, { rules: k2, format: s2 };
    }
    function et(n2, t2, i2) {
      var u2, r2, e2, o2;
      if (ej.isNullOrUndefined(n2) || typeof n2 == "string" || !t2) throw "Bad Number Format Exception";
      if (o2 = t2, u2 = ft(t2, n2), e2 = u2.rules, t2 = u2.format, t2.indexOf("\\") >= 0) {
        var s2 = t2.lastIndexOf("\\"), l2 = t2.slice(0, s2), f2 = t2.slice(s2 + 1, t2.length), c2;
        f2 = f2.replace(/[9?CANa#&]/g, "_");
        c2 = l2 + f2;
        r2 = c2.replace(/[\\]/g, "");
        t2 = t2.replace(/[\\]/g, "");
      } else r2 = t2.replace(/[9?CANa#&]/g, "_");
      return r2 = h(r2, i2), ot(n2, t2, r2, e2, i2, o2);
    }
    function h(n2, t2) {
      var r2, f2, e2, o2, u2, i2;
      if (n2.length != 0) {
        for (r2 = ej.preferredCulture(t2), u2 = "", f2 = r2.numberFormat[","], e2 = r2.numberFormat.currency.symbol, o2 = r2.numberFormat["."], i2 = 0; i2 < n2.length; i2++) u2 += n2[i2] == "," ? f2 : n2[i2] == "." ? o2 : n2[i2] == "$" ? e2 : n2[i2];
        n2 = u2;
      }
      return n2;
    }
    function ot(i2, r2, u2, f2, e2, o2) {
      var ft2, l2, nt2, it2;
      if (!ej.isNullOrUndefined(i2)) {
        r2.toLowerCase().indexOf("e") > -1 && (ft2 = o2.indexOf("+") > -1 ? o2.split("+")[1] : o2.split("-")[1], i2 = i2.toExponential(), o2.indexOf("-") > -1 && (i2 = i2.replace("+", "")));
        var b2, ut2, k2, et2, rt2 = b2 = ut2 = i2.toString(), s2 = u2, a2 = k2 = 0, c2, g2 = "_", d2, v2, y2, tt2, ot2 = r2.match(/[\(\[\]\)]/g);
        if (rt2 = !r2.indexOf("\\") >= 0 ? i2 = ut2.replace(/[\(\)-]/g, "") : et2, l2 = f2.length - 1, nt2 = b2.length - 1, ej.isNullOrUndefined(ot2)) while (a2 < f2.length) {
          if (c2 = b2[k2], d2 = f2[a2], c2 == t) break;
          if (c2 === d2 || c2 === g2 || c2 === "e" && c2 === d2.toLowerCase() ? (c2 === g2 ? g2 : "", v2 = s2.substring(0, a2), y2 = s2.substring(a2), c2 = h(c2, e2), s2 = v2 + c2 + y2.substr(1, y2.length), k2 += 1, a2 += 1) : f2[a2].rule != t ? (it2 = b2.charCodeAt(k2), p(r2, it2, a2) ? (v2 = s2.substring(0, a2), y2 = s2.substring(a2), tt2 = w(b2, k2, a2, r2, u2), s2 = v2 + tt2 + y2.substr(1, y2.length), a2++, k2++) : a2++) : (d2 === "e" && (k2 = b2.indexOf("e") + 1), a2++), k2 > rt2.length || l2 < 0) break;
        }
        else while (l2 >= 0) {
          if (c2 = b2[nt2], d2 = f2[l2], c2 == t) break;
          if (c2 === d2 || c2 === g2 || c2 === "e" && c2 === d2.toLowerCase() ? (c2 === g2 ? g2 : "", v2 = s2.substring(0, l2 + 1), y2 = s2.substring(l2 + 1), c2 = h(c2, e2), s2 = v2.substr(0, v2.length - 1) + c2 + y2, l2--, nt2--) : f2[l2].rule != t ? (it2 = b2.charCodeAt(nt2), p(r2, it2, l2) ? (v2 = s2.substring(0, l2 + 1), y2 = s2.substring(l2 + 1), tt2 = w(b2, nt2, l2, r2, u2), s2 = v2.substr(0, v2.length - 1) + tt2 + y2, l2--, nt2--) : l2--) : l2--, k2 > rt2.length || l2 < 0) break;
        }
        if (i2) return (s2.indexOf("_") - s2.indexOf(",") == 1 || s2.indexOf("_") - s2.indexOf(".") == 1) && (s2 = s2.slice(0, s2.indexOf("_") - 1)), n.trim(s2.replace(/[_]/g, "")) == "" ? null : s2.replace(/[_]/g, "");
      }
    }
    function p(t2, i2, r2) {
      var f2 = y, u2 = false, e2 = t2.substr(r2, 1), o2 = String.fromCharCode(i2);
      return n.each(f2, function(n2, t3) {
        e2 == n2 && (u2 = o2.match(new RegExp(t3)) ? true : false);
      }), u2;
    }
    function w(n2, t2, i2, r2, u2) {
      var f2 = false;
      return r2.indexOf(".") > -1 && i2 === u2.length - 1 && n2[t2 + 1] > 5 && (f2 = true), f2 ? (parseInt(n2[t2]) + 1).toString() : n2[t2];
    }
    function o(n2, t2) {
      return n2.indexOf(t2) === 0;
    }
    function c(n2, t2) {
      return n2.substr(n2.length - t2.length) === t2;
    }
    function f(n2) {
      return (n2 + "").replace(tt, "");
    }
    function st(n2) {
      return isNaN(n2) ? NaN : Math[n2 < 0 ? "ceil" : "floor"](n2);
    }
    function s(n2, t2, i2) {
      for (var r2 = n2.length; r2 < t2; r2++) n2 = i2 ? "0" + n2 : n2 + "0";
      return n2;
    }
    function l(n2, t2, i2) {
      var r2 = t2["-"], u2 = t2["+"], f2;
      switch (i2) {
        case "n -":
          r2 = " " + r2;
          u2 = " " + u2;
        case "n-":
          c(n2, r2) ? f2 = ["-", n2.substr(0, n2.length - r2.length)] : c(n2, u2) && (f2 = ["+", n2.substr(0, n2.length - u2.length)]);
          break;
        case "- n":
          r2 += " ";
          u2 += " ";
        case "-n":
          o(n2, r2) ? f2 = ["-", n2.substr(r2.length)] : o(n2, u2) && (f2 = ["+", n2.substr(u2.length)]);
          break;
        case "(n)":
          o(n2, "(") && c(n2, ")") && (f2 = ["-", n2.substr(1, n2.length - 2)]);
      }
      return f2 || ["", n2];
    }
    function ht(n2, t2, i2) {
      var l2 = i2.groupSizes || [3], c2 = l2[0], a2 = 1, v2 = ej._round(n2, t2), p2;
      isFinite(v2) || (v2 = n2);
      n2 = v2;
      var r2 = n2 + "", u2 = "", e2 = r2.split(/e/i), f2 = e2.length > 1 ? parseInt(e2[1], 10) : 0;
      r2 = e2[0];
      e2 = r2.split(".");
      r2 = e2[0];
      u2 = e2.length > 1 ? e2[1] : "";
      f2 > 0 ? (u2 = s(u2, f2, false), r2 += u2.slice(0, f2), u2 = u2.substr(f2)) : f2 < 0 && (f2 = -f2, r2 = s(r2, f2 + 1, true), u2 = r2.slice(-f2, r2.length) + u2, r2 = r2.slice(0, -f2));
      p2 = i2["."] || ".";
      u2 = t2 > 0 ? p2 + (u2.length > t2 ? u2.slice(0, t2) : s(u2, t2)) : "";
      for (var o2 = r2.length - 1, y2 = i2[","] || ",", h2 = ""; o2 >= 0; ) {
        if (c2 === 0 || c2 > o2) return r2.slice(0, o2 + 1) + (h2.length ? y2 + h2 + u2 : u2);
        h2 = r2.slice(o2 - c2 + 1, o2 + 1) + (h2.length ? y2 + h2 : "");
        o2 -= c2;
        a2 < l2.length && (c2 = l2[a2], a2++);
      }
      return r2.slice(0, o2 + 1) + y2 + h2 + u2;
    }
    function ct(n2, t2, i2) {
      var h2, r2;
      if (!t2 || t2 === "i") return i2.name.length ? n2.toLocaleString() : n2.toString();
      t2 = t2 || "D";
      var e2 = i2.numberFormat, u2 = Math.abs(n2), f2 = -1, o2;
      t2.length > 1 && (f2 = parseInt(t2.slice(1), 10));
      h2 = t2.charAt(0).toUpperCase();
      switch (h2) {
        case "D":
          o2 = "n";
          u2 = st(u2);
          f2 !== -1 && (u2 = s("" + u2, f2, true));
          n2 < 0 && (u2 = -u2);
          break;
        case "N":
          r2 = e2;
          r2.pattern = r2.pattern || ["-n"];
        case "C":
          r2 = r2 || e2.currency;
          r2.pattern = r2.pattern || ["-$n", "$n"];
        case "P":
          r2 = r2 || e2.percent;
          r2.pattern = r2.pattern || ["-n %", "n %"];
          o2 = n2 < 0 ? r2.pattern[0] || "-n" : r2.pattern[1] || "n";
          f2 === -1 && (f2 = r2.decimals);
          u2 = ht(u2 * (h2 === "P" ? 100 : 1), f2, r2);
          break;
        default:
          return et(n2, t2, i2);
      }
      return lt(u2, o2, e2);
    }
    function lt(n2, t2, i2) {
      for (var f2 = /n|\$|-|%/g, r2 = "", e2, u2; ; ) {
        if (e2 = f2.lastIndex, u2 = f2.exec(t2), r2 += t2.slice(e2, u2 ? u2.index : t2.length), !u2) break;
        switch (u2[0]) {
          case "n":
            r2 += n2;
            break;
          case "$":
            r2 += i2.currency.symbol || "$";
            break;
          case "-":
            /[1-9]/.test(n2) && (r2 += i2["-"] || "-");
            break;
          case "%":
            r2 += i2.percent.symbol || "%";
        }
      }
      return r2;
    }
    function b(n2, t2, i2) {
      var p2, o2, s2, r2, w2, d2, b2, k2, g2, h2, nt2;
      typeof i2 == "string" && (t2 = i2, i2 = 10);
      t2 = ej.globalize.findCulture(t2);
      var c2 = NaN, u2 = t2.numberFormat, y2 = t2.numberFormat.pattern[0];
      if (n2 = n2.replace(/ /g, ""), n2.indexOf(t2.numberFormat.currency.symbol) > -1 ? (n2 = n2.replace(t2.numberFormat.currency.symbol || "$", ""), n2 = n2.replace(t2.numberFormat.currency["."] || ".", t2.numberFormat["."] || "."), y2 = f(t2.numberFormat.currency.pattern[0].replace("$", ""))) : n2.indexOf(t2.numberFormat.percent.symbol) > -1 && (n2 = n2.replace(t2.numberFormat.percent.symbol || "%", ""), n2 = n2.replace(t2.numberFormat.percent["."] || ".", t2.numberFormat["."] || "."), y2 = f(t2.numberFormat.percent.pattern[0].replace("%", ""))), n2 = f(n2), it.test(n2)) c2 = parseFloat(n2, "", i2);
      else if (rt.test(n2)) c2 = parseInt(n2, 16);
      else {
        var a2 = l(n2, u2, y2), v2 = a2[0], e2 = a2[1];
        v2 === "" && u2.pattern[0] !== "-n" && (a2 = l(n2, u2, "-n"), v2 = a2[0], e2 = a2[1]);
        v2 = v2 || "+";
        s2 = e2.indexOf("e");
        s2 < 0 && (s2 = e2.indexOf("E"));
        s2 < 0 ? (o2 = e2, p2 = null) : (o2 = e2.substr(0, s2), p2 = e2.substr(s2 + 1));
        d2 = u2["."] || ".";
        b2 = o2.indexOf(d2);
        b2 < 0 ? (r2 = o2, w2 = null) : (r2 = o2.substr(0, b2), w2 = o2.substr(b2 + d2.length));
        k2 = u2[","] || ",";
        r2 = r2.split(k2).join("");
        g2 = k2.replace(/\u00A0/g, " ");
        k2 !== g2 && (r2 = r2.split(g2).join(""));
        h2 = v2 + r2;
        w2 !== null && (h2 += "." + w2);
        p2 !== null && (nt2 = l(p2, u2, y2), h2 += "e" + (nt2[0] || "+") + nt2[1]);
        !i2 && ut.test(h2) ? c2 = parseFloat(h2) : i2 && (c2 = parseInt(h2, i2));
      }
      return c2;
    }
    function r(n2, t2, i2) {
      return n2 < t2 || n2 > i2;
    }
    function at(n2, t2) {
      var u2 = /* @__PURE__ */ new Date(), i2, r2;
      return t2 < 100 && (i2 = n2.twoDigitYearMax, i2 = typeof i2 == "string" ? (/* @__PURE__ */ new Date()).getFullYear() % 100 + parseInt(i2, 10) : i2, r2 = u2.getFullYear(), t2 += r2 - r2 % 100, t2 > i2 && (t2 -= 100)), t2;
    }
    function e(n2, t2) {
      if (n2.indexOf) return n2.indexOf(t2);
      for (var i2 = 0, r2 = n2.length; i2 < r2; i2++) if (n2[i2] === t2) return i2;
      return -1;
    }
    function a(n2) {
      return n2.split(" ").join(" ").toUpperCase();
    }
    function u(n2) {
      for (var i2 = [], t2 = 0, r2 = n2.length; t2 < r2; t2++) i2[t2] = a(n2[t2]);
      return i2;
    }
    function vt(n2, t2, i2) {
      var r2, o2 = n2.days, f2 = n2._upperDays;
      return f2 || (n2._upperDays = f2 = [u(o2.names), u(o2.namesAbbr), u(o2.namesShort)]), t2 = a(t2), i2 ? (r2 = e(f2[1], t2), r2 === -1 && (r2 = e(f2[2], t2))) : r2 = e(f2[0], t2), r2;
    }
    function yt(n2, t2, i2) {
      var s2 = n2.months, h2 = n2.monthsGenitive || n2.months, r2 = n2._upperMonths, o2 = n2._upperMonthsGen, f2;
      return r2 || (n2._upperMonths = r2 = [u(s2.names), u(s2.namesAbbr)], n2._upperMonthsGen = o2 = [u(h2.names), u(h2.namesAbbr)]), t2 = a(t2), f2 = e(i2 ? r2[1] : r2[0], t2), f2 < 0 && (f2 = e(i2 ? o2[1] : o2[0], t2)), f2;
    }
    function v(n2, t2) {
      for (var r2, f2 = 0, i2 = false, u2 = 0, e2 = n2.length; u2 < e2; u2++) r2 = n2.charAt(u2), r2 == "'" ? (i2 ? t2.push("'") : f2++, i2 = false) : r2 == "\\" ? (i2 && t2.push("\\"), i2 = !i2) : (t2.push(r2), i2 = false);
      return f2;
    }
    function pt(n2, t2, i2, r2) {
      var s2, e2;
      if (!n2) return null;
      var u2 = 0, f2 = 0, o2 = null;
      t2 = t2.split("");
      for (var h2 = t2.length, c2 = function(n3) {
        for (var i3 = 0; t2[u2] === n3; ) i3++, u2++;
        return i3 > 0 && (u2 -= 1), i3;
      }, l2 = function(t3) {
        var r3 = new RegExp("^\\d{1," + t3 + "}"), i3 = n2.substr(f2, t3).match(r3);
        return i3 ? (i3 = i3[0], f2 += i3.length, parseInt(i3, 10)) : null;
      }, a2 = function(t3, i3) {
        for (var r3 = 0, s3 = t3.length, e3, o3, u3; r3 < s3; r3++) if (e3 = t3[r3], o3 = e3.length, u3 = n2.substr(f2, o3), i3 && (u3 = u3.toLowerCase()), u3 == e3) return f2 += o3, r3 + 1;
        return null;
      }, v2 = function(n3) {
        for (var t3 = 0, r3 = n3.length, i3 = []; t3 < r3; t3++) i3[t3] = (n3[t3] + "").toLowerCase();
        return i3;
      }, y2 = function(n3) {
        var t3 = {};
        for (var i3 in n3) t3[i3] = v2(n3[i3]);
        return t3;
      }; u2 < h2; u2++) s2 = t2[u2], s2 === "d" && (e2 = c2("d"), r2._lowerDays || (r2._lowerDays = y2(r2.days)), o2 = e2 < 3 ? l2(2) : a2(r2._lowerDays[e2 == 3 ? "namesAbbr" : "names"], true));
      return o2;
    }
    function k(n2, t2) {
      t2 = t2 || "F";
      var i2, u2 = n2.patterns, r2 = t2.length;
      if (r2 === 1) {
        if (i2 = u2[t2], !i2) throw "Invalid date format string '" + t2 + "'.";
        t2 = i2;
      } else r2 === 2 && t2.charAt(0) === "%" && (t2 = t2.charAt(1));
      return t2;
    }
    function d(n2, t2, u2) {
      var w2, ct2, h2, g2, nt2, s2, wt, b2, et2;
      n2 = f(n2);
      t2 = f(t2);
      var e2 = u2.calendar, ot2 = ej.globalize._getDateParseRegExp(e2, t2), st2 = new RegExp(ot2.regExp).exec(n2);
      if (st2 === null) return null;
      var ht2 = ot2.groups, y2 = null, a2 = null, v2 = null, tt2 = null, l2 = 0, p2, it2 = 0, rt2 = 0, ut2 = 0, k2 = null, ft2 = false;
      for (w2 = 0, ct2 = ht2.length; w2 < ct2; w2++) if (h2 = st2[w2 + 1], h2) {
        var lt2 = ht2[w2], d2 = lt2.length, c2 = parseInt(h2, 10);
        switch (lt2) {
          case i.DAY_OF_MONTH_DOUBLE_DIGIT:
          case i.DAY_OF_MONTH_SINGLE_DIGIT:
            if (v2 = c2, r(v2, 1, 31)) return null;
            break;
          case i.MONTH_THREE_LETTER:
          case i.MONTH_FULL_NAME:
            if (a2 = yt(e2, h2, d2 === 3), r(a2, 0, 11)) return null;
            break;
          case i.MONTH_SINGLE_DIGIT:
          case i.MONTH_DOUBLE_DIGIT:
            if (a2 = c2 - 1, r(a2, 0, 11)) return null;
            break;
          case i.YEAR_SINGLE_DIGIT:
          case i.YEAR_DOUBLE_DIGIT:
          case i.YEAR_FULL:
            if (y2 = d2 < 4 ? at(e2, c2) : c2, r(y2, 0, 9999)) return null;
            break;
          case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:
          case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:
            if (l2 = c2, l2 === 12 && (l2 = 0), r(l2, 0, 11)) return null;
            break;
          case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:
          case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:
            if (l2 = c2, r(l2, 0, 23)) return null;
            break;
          case i.MINUTES_SINGLE_DIGIT:
          case i.MINUTES_DOUBLE_DIGIT:
            if (it2 = c2, r(it2, 0, 59)) return null;
            break;
          case i.SECONDS_SINGLE_DIGIT:
          case i.SECONDS_DOUBLE_DIGIT:
            if (rt2 = c2, r(rt2, 0, 59)) return null;
            break;
          case i.MERIDIAN_INDICATOR_FULL:
          case i.MERIDIAN_INDICATOR_SINGLE:
            if (ft2 = e2.PM && (h2 === e2.PM[0] || h2 === e2.PM[1] || h2 === e2.PM[2]), !ft2 && (!e2.AM || h2 !== e2.AM[0] && h2 !== e2.AM[1] && h2 !== e2.AM[2])) return null;
            break;
          case i.DECISECONDS:
          case i.CENTISECONDS:
          case i.MILLISECONDS:
            if (ut2 = c2 * Math.pow(10, 3 - d2), r(ut2, 0, 999)) return null;
            break;
          case i.DAY_OF_WEEK_THREE_LETTER:
            v2 = pt(n2, t2, u2, e2);
            break;
          case i.DAY_OF_WEEK_FULL_NAME:
            if (vt(e2, h2, d2 === 3), r(tt2, 0, 6)) return null;
            break;
          case i.TIME_ZONE_OFFSET_FULL:
            if ((g2 = h2.split(/:/), g2.length !== 2) || (p2 = parseInt(g2[0], 10), r(p2, -12, 13)) || (nt2 = parseInt(g2[1], 10), r(nt2, 0, 59))) return null;
            k2 = p2 * 60 + (o(h2, "-") ? -nt2 : nt2);
            break;
          case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:
          case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:
            if (p2 = c2, r(p2, -12, 13)) return null;
            k2 = p2 * 60;
        }
      }
      if (s2 = /* @__PURE__ */ new Date(), b2 = e2.convert, wt = b2 ? b2.fromGregorian(s2)[0] : s2.getFullYear(), y2 === null && (y2 = wt), a2 === null && (a2 = 0), v2 === null && (v2 = 1), b2) {
        if (s2 = b2.toGregorian(y2, a2, v2), s2 === null) return null;
      } else if ((s2.setFullYear(y2, a2, v2), s2.getDate() !== v2) || tt2 !== null && s2.getDay() !== tt2) return null;
      return ft2 && l2 < 12 && (l2 += 12), s2.setHours(l2, it2, rt2, ut2), k2 !== null && (et2 = s2.getMinutes() - (k2 + s2.getTimezoneOffset()), s2.setHours(s2.getHours() + parseInt(et2 / 60, 10), et2 % 60)), s2;
    }
    function g(n2, t2, r2) {
      function o2(n3, t3) {
        var i2, r3 = n3 + "";
        return t3 > 1 && r3.length < t3 ? (i2 = it2[t3 - 2] + r3, i2.substr(i2.length - t3, t3)) : r3;
      }
      function ut2() {
        return l2 || b2 ? l2 : (l2 = rt2.test(t2), b2 = true, l2);
      }
      var e2 = r2.calendar, p2 = e2.convert, u2, w2, y2, f2, tt2, h2;
      if (!t2 || !t2.length || t2 === "i") return r2 && r2.name.length ? p2 ? g(n2, e2.patterns.F, r2) : n2.toLocaleString() : n2.toString();
      w2 = t2 === "s";
      t2 = k(e2, t2);
      u2 = [];
      var s2, it2 = ["0", "00", "000"], l2, b2, rt2 = /([^d]|^)(d|dd)([^d]|$)/g, d2 = 0, nt2 = /\/|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|zzz|zz|z|gg|g/g, c2;
      for (!w2 && p2 && (c2 = p2.fromGregorian(n2)); ; ) {
        var ft2 = nt2.lastIndex, a2 = nt2.exec(t2), et2 = t2.slice(ft2, a2 ? a2.index : t2.length);
        if (d2 += v(et2, u2), !a2) break;
        if (d2 % 2) {
          u2.push(a2[0]);
          continue;
        }
        y2 = a2[0];
        f2 = y2.length;
        switch (y2) {
          case i.DAY_OF_WEEK_THREE_LETTER:
          case i.DAY_OF_WEEK_FULL_NAME:
            tt2 = f2 === 3 ? e2.days.namesAbbr : e2.days.names;
            u2.push(tt2[n2.getDay()]);
            break;
          case i.DAY_OF_MONTH_SINGLE_DIGIT:
          case i.DAY_OF_MONTH_DOUBLE_DIGIT:
            l2 = true;
            u2.push(o2(c2 ? c2[2] : n2.getDate(), f2));
            break;
          case i.MONTH_THREE_LETTER:
          case i.MONTH_FULL_NAME:
            h2 = c2 ? c2[1] : n2.getMonth();
            u2.push(e2.monthsGenitive && ut2() ? e2.monthsGenitive[f2 === 3 ? "namesAbbr" : "names"][h2] : e2.months[f2 === 3 ? "namesAbbr" : "names"][h2]);
            break;
          case i.MONTH_SINGLE_DIGIT:
          case i.MONTH_DOUBLE_DIGIT:
            u2.push(o2((c2 ? c2[1] : n2.getMonth()) + 1, f2));
            break;
          case i.YEAR_SINGLE_DIGIT:
          case i.YEAR_DOUBLE_DIGIT:
          case i.YEAR_FULL:
            h2 = c2 ? c2[0] : n2.getFullYear();
            f2 < 4 && (h2 = h2 % 100);
            u2.push(o2(h2, f2));
            break;
          case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:
          case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:
            s2 = n2.getHours() % 12;
            s2 === 0 && (s2 = 12);
            u2.push(o2(s2, f2));
            break;
          case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:
          case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:
            u2.push(o2(n2.getHours(), f2));
            break;
          case i.MINUTES_SINGLE_DIGIT:
          case i.MINUTES_DOUBLE_DIGIT:
            u2.push(o2(n2.getMinutes(), f2));
            break;
          case i.SECONDS_SINGLE_DIGIT:
          case i.SECONDS_DOUBLE_DIGIT:
            u2.push(o2(n2.getSeconds(), f2));
            break;
          case i.MERIDIAN_INDICATOR_SINGLE:
          case i.MERIDIAN_INDICATOR_FULL:
            h2 = n2.getHours() < 12 ? e2.AM ? e2.AM[0] : " " : e2.PM ? e2.PM[0] : " ";
            u2.push(f2 === 1 ? h2.charAt(0) : h2);
            break;
          case i.DECISECONDS:
          case i.CENTISECONDS:
          case i.MILLISECONDS:
            u2.push(o2(n2.getMilliseconds(), 3).substr(0, f2));
            break;
          case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:
          case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:
            s2 = n2.getTimezoneOffset() / 60;
            u2.push((s2 <= 0 ? "+" : "-") + o2(Math.floor(Math.abs(s2)), f2));
            break;
          case i.TIME_ZONE_OFFSET_FULL:
            s2 = n2.getTimezoneOffset() / 60;
            u2.push((s2 <= 0 ? "+" : "-") + o2(Math.floor(Math.abs(s2)), 2) + ":" + o2(Math.abs(n2.getTimezoneOffset() % 60), 2));
            break;
          case i.DATE_SEPARATOR:
            u2.push(e2["/"] || "/");
            break;
          default:
            throw "Invalid date format pattern '" + y2 + "'.";
        }
      }
      return u2.join("");
    }
    function nt(n2, t2) {
      return t2.length ? nt(n2[t2[0]], t2.slice(1)) : n2;
    }
    var i;
    ej.globalize = {};
    ej.cultures = {};
    ej.cultures["default"] = ej.cultures["en-US"] = n.extend(true, { name: "en-US", englishName: "English", nativeName: "English", language: "en", isRTL: false, numberFormat: { pattern: ["-n"], decimals: 2, ",": ",", ".": ".", groupSizes: [3], "+": "+", "-": "-", percent: { pattern: ["-n %", "n %"], decimals: 2, groupSizes: [3], ",": ",", ".": ".", symbol: "%" }, currency: { pattern: ["($n)", "$n"], decimals: 2, groupSizes: [3], ",": ",", ".": ".", symbol: "$" } }, calendars: { standard: { "/": "/", ":": ":", firstDay: 0, week: { name: "Week", nameAbbr: "Wek", nameShort: "Wk" }, days: { names: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], namesAbbr: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], namesShort: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"] }, months: { names: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December", ""], namesAbbr: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", ""] }, AM: ["AM", "am", "AM"], PM: ["PM", "pm", "PM"], twoDigitYearMax: 2029, patterns: { d: "M/d/yyyy", D: "dddd, MMMM dd, yyyy", t: "h:mm tt", T: "h:mm:ss tt", f: "dddd, MMMM dd, yyyy h:mm tt", F: "dddd, MMMM dd, yyyy h:mm:ss tt", M: "MMMM dd", Y: "yyyy MMMM", S: "yyyy'-'MM'-'dd'T'HH':'mm':'ss" } } } }, ej.cultures["en-US"]);
    ej.cultures["en-US"].calendar = ej.cultures["en-US"].calendar || ej.cultures["en-US"].calendars.standard;
    var tt = /^\s+|\s+$/g, it = /^[+-]?infinity$/i, rt = /^0x[a-f0-9]+$/i, ut = /^[+-]?\d*\.?\d*(e[+-]?\d+)?$/, y = { "9": "[0-9 ]", "0": "[0-9 ]", a: "[A-Za-z0-9 ]", A: "[A-Za-z0-9]", N: "[0-9]", "#": "[0-9]", "&": "[^]+", "<": "", ">": "", C: "[A-Za-z ]", "?": "[A-Za-z]" };
    i = { DAY_OF_WEEK_THREE_LETTER: "ddd", DAY_OF_WEEK_FULL_NAME: "dddd", DAY_OF_MONTH_SINGLE_DIGIT: "d", DAY_OF_MONTH_DOUBLE_DIGIT: "dd", MONTH_THREE_LETTER: "MMM", MONTH_FULL_NAME: "MMMM", MONTH_SINGLE_DIGIT: "M", MONTH_DOUBLE_DIGIT: "MM", YEAR_SINGLE_DIGIT: "y", YEAR_DOUBLE_DIGIT: "yy", YEAR_FULL: "yyyy", HOURS_SINGLE_DIGIT_12_HOUR_CLOCK: "h", HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK: "hh", HOURS_SINGLE_DIGIT_24_HOUR_CLOCK: "H", HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK: "HH", MINUTES_SINGLE_DIGIT: "m", MINUTES_DOUBLE_DIGIT: "mm", SECONDS_SINGLE_DIGIT: "s", SECONDS_DOUBLE_DIGIT: "ss", MERIDIAN_INDICATOR_SINGLE: "t", MERIDIAN_INDICATOR_FULL: "tt", DECISECONDS: "f", CENTISECONDS: "ff", MILLISECONDS: "fff", TIME_ZONE_OFFSET_SINGLE_DIGIT: "z", TIME_ZONE_OFFSET_DOUBLE_DIGIT: "zz", TIME_ZONE_OFFSET_FULL: "zzz", DATE_SEPARATOR: "/" };
    ej.globalize._getDateParseRegExp = function(n2, t2) {
      var e2 = n2._parseRegExp, s2, p2, o2, w2, r2, b2, d2;
      if (e2) {
        if (s2 = e2[t2], s2) return s2;
      } else n2._parseRegExp = e2 = {};
      for (var h2 = k(n2, t2).replace(/([\^\$\.\*\+\?\|\[\]\(\)\{\}])/g, "\\\\$1"), u2 = ["^"], l2 = [], c2 = 0, a2 = 0, y2 = /\/|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|zzz|zz|z|gg|g/g, f2; (f2 = y2.exec(h2)) !== null; ) {
        if (p2 = h2.slice(c2, f2.index), c2 = y2.lastIndex, a2 += v(p2, u2), a2 % 2) {
          u2.push(f2[0]);
          continue;
        }
        o2 = f2[0];
        w2 = o2.length;
        switch (o2) {
          case i.DAY_OF_WEEK_THREE_LETTER:
          case i.DAY_OF_WEEK_FULL_NAME:
          case i.MONTH_FULL_NAME:
          case i.MONTH_THREE_LETTER:
            r2 = "(\\D+)";
            break;
          case i.MERIDIAN_INDICATOR_FULL:
          case i.MERIDIAN_INDICATOR_SINGLE:
            r2 = "(\\D*)";
            break;
          case i.YEAR_FULL:
          case i.MILLISECONDS:
          case i.CENTISECONDS:
          case i.DECISECONDS:
            r2 = "(\\d{" + w2 + "})";
            break;
          case i.DAY_OF_MONTH_DOUBLE_DIGIT:
          case i.DAY_OF_MONTH_SINGLE_DIGIT:
          case i.MONTH_DOUBLE_DIGIT:
          case i.MONTH_SINGLE_DIGIT:
          case i.YEAR_DOUBLE_DIGIT:
          case i.YEAR_SINGLE_DIGIT:
          case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:
          case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:
          case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:
          case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:
          case i.MINUTES_DOUBLE_DIGIT:
          case i.MINUTES_SINGLE_DIGIT:
          case i.SECONDS_DOUBLE_DIGIT:
          case i.SECONDS_SINGLE_DIGIT:
            r2 = "(\\d\\d?)";
            break;
          case i.TIME_ZONE_OFFSET_FULL:
            r2 = "([+-]?\\d\\d?:\\d{2})";
            break;
          case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:
          case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:
            r2 = "([+-]?\\d\\d?)";
            break;
          case i.DATE_SEPARATOR:
            r2 = "(\\" + n2["/"] + ")";
            break;
          default:
            throw "Invalid date format pattern '" + o2 + "'.";
        }
        r2 && u2.push(r2);
        l2.push(f2[0]);
      }
      return v(h2.slice(c2), u2), u2.push("$"), b2 = u2.join("").replace(/\s+/g, "\\s+"), d2 = { regExp: b2, groups: l2 }, e2[t2] = d2;
    };
    ej.globalize.addCulture = function(t2, i2) {
      ej.cultures[t2] = n.extend(true, n.extend(true, {}, ej.cultures["default"], i2), ej.cultures[t2]);
      ej.cultures[t2].calendar = ej.cultures[t2].calendars.standard;
    };
    ej.globalize.preferredCulture = function(n2) {
      return n2 = typeof n2 != "undefined" && typeof n2 == typeof this.cultureObject ? n2.name : n2, this.cultureObject = ej.globalize.findCulture(n2), this.cultureObject;
    };
    ej.globalize.setCulture = function(n2) {
      return ej.isNullOrUndefined(this.globalCultureObject) && (this.globalCultureObject = ej.globalize.findCulture(n2)), n2 = typeof n2 != "undefined" && typeof n2 == typeof this.globalCultureObject ? n2.name : n2, n2 && (this.globalCultureObject = ej.globalize.findCulture(n2)), ej.cultures.current = this.globalCultureObject, this.globalCultureObject;
    };
    ej.globalize.culture = function(n2) {
      ej.cultures.current = ej.globalize.findCulture(n2);
    };
    ej.globalize.findCulture = function(t2) {
      var f2, i2, e2, u2, r2, o2;
      if (t2) {
        if (n.isPlainObject(t2) && t2.numberFormat && (f2 = t2), typeof t2 == "string") {
          if (i2 = ej.cultures, i2[t2]) return i2[t2];
          if (t2.indexOf("-") > -1) {
            if (e2 = t2.split("-")[0], i2[e2]) return i2[e2];
          } else for (u2 = n.map(i2, function(n2) {
            return n2;
          }), r2 = 0; r2 < u2.length; r2++) if (o2 = u2[r2].name.split("-")[0], o2 === t2) return u2[r2];
          return ej.cultures["default"];
        }
      } else f2 = ej.cultures.current || ej.cultures["default"];
      return f2;
    };
    ej.globalize.format = function(n2, t2, i2) {
      var r2 = ej.globalize.findCulture(i2);
      return typeof n2 == "number" ? n2 = ct(n2, t2, r2) : n2 instanceof Date && (n2 = g(n2, t2, r2)), n2;
    };
    ej.globalize._round = function(n2, t2) {
      var i2 = Math.pow(10, t2);
      return Math.round(n2 * i2) / i2;
    };
    ej.globalize.parseInt = function(n2, t2, i2) {
      return t2 || (t2 = 10), Math.floor(b(n2, i2, t2));
    };
    ej.globalize.getISODate = function(n2) {
      if (n2 instanceof Date) return n2.toISOString();
    };
    ej.globalize.parseFloat = function(n2, t2, i2) {
      return typeof t2 == "string" && (i2 = t2, t2 = 10), b(n2, i2);
    };
    ej.globalize.parseDate = function(n2, t2, i2) {
      var r2, o2, f2, u2, s2, e2;
      if (i2 = ej.globalize.findCulture(i2), t2) {
        if (typeof t2 == "string" && (t2 = [t2]), t2.length) {
          for (u2 = 0, s2 = t2.length; u2 < s2; u2++) if (e2 = t2[u2], e2 && (r2 = d(n2, e2, i2), r2)) break;
        }
      } else {
        f2 = i2.calendar.patterns;
        for (o2 in f2) if (r2 = d(n2, f2[o2], i2), r2) break;
      }
      return r2 || null;
    };
    ej.globalize.getLocalizedConstants = function(t2, i2) {
      var r2, u2 = t2.replace("ej.", "").split(".");
      return r2 = nt(ej, u2), n.extend(true, {}, r2.Locale["default"], r2.Locale[i2 ? i2 : this.cultureObject.name]);
    };
    n.extend(ej, ej.globalize);
  }(jQuery);
});
/*! Bundled license information:

@boldreports/javascript-reporting-controls/Scripts/common/bold.reports.common.min.js:
  (*!
  *  filename: bold.reports.common.min.js
  *  version : 9.1.15
  *  Copyright Syncfusion Inc. 2001 - 2025. All rights reserved.
  *  Use of this code is subject to the terms of our license.
  *  A copy of the current license can be obtained at any time by e-mailing
  *  <EMAIL>. Any infringement will be prosecuted under
  *  applicable laws. 
  *)
*/
//# sourceMappingURL=@boldreports_javascript-reporting-controls_Scripts_common_bold__reports__common__min.js.map
