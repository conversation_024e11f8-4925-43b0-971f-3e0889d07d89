﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using Azure.Core;

namespace Application.Infrastructure.Models.Response
{
    public class EmployeeCResponse
    {
        public Guid? Id { get; set; }
        public string? FullName { get; set; }
        public string? ShortName { get; set; }
        public int? OrganizationUnitId { get; set; }
        public short? PositionType { get; set; }

        public static Expression<Func<EmployeeCEntity, EmployeeCResponse>> Expression
        {
            get
            {
                return entity => new EmployeeCResponse()
                {
                    Id = entity.Id,
                    FullName = entity.FullName,
                    ShortName = entity.ShortName,
                    OrganizationUnitId = entity.OrganizationUnitId,
                    PositionType = entity.PositionType
                };
            }
        }

        public static EmployeeCResponse Create(EmployeeCEntity response)
        {
            if (response == null) return null;

            return Expression.Compile().Invoke(response);
        }
    }
}
