﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.Degree;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Country
{
    public class UpdateCountryRequest : CreateCountryRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateCountryRequest, CountryEntity>> Expression
        {
            get
            {
                return entity => new CountryEntity
                {
                    Id = entity.Id,
                    CountryCode = entity.CountryCode,
                    CountryName = entity.CountryName,
                    LanguageName = entity.LanguageName,
                    VietnameseName = entity.VietnameseName,
                    Active = entity.Active,
                    Class = entity.Class,
                    Year = DateTime.Now.Year,
                };
            }
        }

        public static CountryEntity Create(UpdateCountryRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
