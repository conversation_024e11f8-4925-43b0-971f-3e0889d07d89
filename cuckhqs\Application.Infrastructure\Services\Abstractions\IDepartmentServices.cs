﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models;
using Application.Infrastructure.Models.Request;
using Application.Infrastructure.Models.Response;
using Microsoft.AspNetCore.Http;

namespace Application.Infrastructure.Services
{
    public interface IDepartmentServices
    {
        Task<BaseSearchResponse<DepartmentResponse>> SearchDepartmentAsync(SearchDepartmentRequest request);

        Task<DepartmentResponse> CreateDepartmentAsync(CreateDepartmentRequest request);

        Task<bool> UpdateDepartmentAsync(UpdateDepartmentRequest request);

        Task<bool> DeleteDepartmentAsync(DeleteDepartmentRequest request);

        Task<bool> UpdateStatusByIds(UpdateStatusByIdsIntRequest request);

    }
}