﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace Application.Infrastructure.Configurations
{
    public class OnDutyCommandConfiguration : IEntityTypeConfiguration<OnDutyCommandEntity>
    {
        public void Configure(EntityTypeBuilder<OnDutyCommandEntity> builder)
        {
            builder.ToTable("OnDutyComand");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("Id");
            builder.Property(x => x.Year).HasColumnName("Year");
            builder.Property(x => x.Week).HasColumnName("Week");
            builder.Property(x => x.Date).HasColumnName("Date");
            builder.Property(x => x.EmployeeId_H).HasColumnName("EmployeeId_H");
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");

            builder.HasOne(x => x.Employee).WithMany(x => x.OnDutyCommands).HasForeignKey(x => x.EmployeeId_H);
        }
    }
}
