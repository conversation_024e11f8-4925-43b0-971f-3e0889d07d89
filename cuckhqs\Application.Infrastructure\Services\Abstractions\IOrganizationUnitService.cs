using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.Category.OrganizationUnit;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;

namespace Application.Infrastructure.Services.Abstractions;

public interface IOrganizationUnitService
{
    Task<List<OrganizationUnitDbResponse>> GetAllOrganizationUnitAsync();
    Task<OrganizationUnitTreeResponse> GetOrganizationUnitTreeAsync();
    Task<List<Organization_Unit_Tree_Response>> OrganizationUnitCascadeEmployee();
    Task<BaseSearchResponse<OrganizationUnitResponse>> SearchOrganizationUnitAsync(SearchOrganizationUnitRequest request);
    Task<List<OrganizationUnitResponse>> GetOrganizationUnitById(int Id);
    Task<OrganizationUnitResponse> CreateOrganizationUnitAsync(CreateOrganizationUnitRequest request);
    Task<bool> UpdateOrganizationUnitAsync(UpdateOrganizationUnitRequest request);
    Task<string> DeleteOrganizationUnitAsync(DeleteOrganizationUnitRequest request);
}