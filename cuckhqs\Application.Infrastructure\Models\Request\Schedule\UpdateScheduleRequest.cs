﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;

namespace Application.Infrastructure.Models.Request.Schedule
{
    public class UpdateScheduleRequest : CreateScheduleRequest
    {
        public int Id { get; set; }

        public static Expression<Func<UpdateScheduleRequest, ScheduleEntity>> Expression
        {
            get
            {
                return entity => new ScheduleEntity
                {
                    Id = entity.Id,
                    Name = Convert.ToString(entity.Name),
                    Year = Convert.ToInt32(entity.Year),
                    Week = Convert.ToInt32(entity.Week),
                    Date = Convert.ToDateTime(entity.Date),
                    Rank = Convert.ToString(entity.Rank),
                    Active = Convert.ToBoolean(entity.Active),
                    SortOrder = Convert.ToInt32(entity.SortOrder),
                };
            }
        }

        public static ScheduleEntity Create(UpdateScheduleRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
