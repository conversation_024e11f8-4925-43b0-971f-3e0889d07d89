{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-scrollpanel.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * ScrollPanel is a cross browser, lightweight and themable alternative to native browser scrollbar.\n * @group Components\n */\nconst _c0 = [\"container\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"xBar\"];\nconst _c3 = [\"yBar\"];\nconst _c4 = [\"*\"];\nfunction ScrollPanel_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nclass ScrollPanel {\n  platformId;\n  el;\n  zone;\n  cd;\n  document;\n  renderer;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Step factor to scroll the content while pressing the arrow keys.\n   * @group Props\n   */\n  step = 5;\n  containerViewChild;\n  contentViewChild;\n  xBarViewChild;\n  yBarViewChild;\n  templates;\n  scrollYRatio;\n  scrollXRatio;\n  timeoutFrame = fn => setTimeout(fn, 0);\n  initialized = false;\n  lastPageY;\n  lastPageX;\n  isXBarClicked = false;\n  isYBarClicked = false;\n  contentTemplate;\n  lastScrollLeft = 0;\n  lastScrollTop = 0;\n  orientation = 'vertical';\n  timer;\n  contentId;\n  windowResizeListener;\n  contentScrollListener;\n  mouseEnterListener;\n  xBarMouseDownListener;\n  yBarMouseDownListener;\n  documentMouseMoveListener;\n  documentMouseUpListener;\n  constructor(platformId, el, zone, cd, document, renderer) {\n    this.platformId = platformId;\n    this.el = el;\n    this.zone = zone;\n    this.cd = cd;\n    this.document = document;\n    this.renderer = renderer;\n    this.contentId = UniqueComponentId() + '_content';\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        this.moveBar();\n        this.moveBar = this.moveBar.bind(this);\n        this.onXBarMouseDown = this.onXBarMouseDown.bind(this);\n        this.onYBarMouseDown = this.onYBarMouseDown.bind(this);\n        this.onDocumentMouseMove = this.onDocumentMouseMove.bind(this);\n        this.onDocumentMouseUp = this.onDocumentMouseUp.bind(this);\n        this.windowResizeListener = this.renderer.listen(window, 'resize', this.moveBar);\n        this.contentScrollListener = this.renderer.listen(this.contentViewChild.nativeElement, 'scroll', this.moveBar);\n        this.mouseEnterListener = this.renderer.listen(this.contentViewChild.nativeElement, 'mouseenter', this.moveBar);\n        this.xBarMouseDownListener = this.renderer.listen(this.xBarViewChild.nativeElement, 'mousedown', this.onXBarMouseDown);\n        this.yBarMouseDownListener = this.renderer.listen(this.yBarViewChild.nativeElement, 'mousedown', this.onYBarMouseDown);\n        this.calculateContainerHeight();\n        this.initialized = true;\n      });\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  calculateContainerHeight() {\n    let container = this.containerViewChild.nativeElement;\n    let content = this.contentViewChild.nativeElement;\n    let xBar = this.xBarViewChild.nativeElement;\n    const window = this.document.defaultView;\n    let containerStyles = window.getComputedStyle(container),\n      xBarStyles = window.getComputedStyle(xBar),\n      pureContainerHeight = DomHandler.getHeight(container) - parseInt(xBarStyles['height'], 10);\n    if (containerStyles['max-height'] != 'none' && pureContainerHeight == 0) {\n      if (content.offsetHeight + parseInt(xBarStyles['height'], 10) > parseInt(containerStyles['max-height'], 10)) {\n        container.style.height = containerStyles['max-height'];\n      } else {\n        container.style.height = content.offsetHeight + parseFloat(containerStyles.paddingTop) + parseFloat(containerStyles.paddingBottom) + parseFloat(containerStyles.borderTopWidth) + parseFloat(containerStyles.borderBottomWidth) + 'px';\n      }\n    }\n  }\n  moveBar() {\n    let container = this.containerViewChild.nativeElement;\n    let content = this.contentViewChild.nativeElement;\n    let xBar = this.xBarViewChild.nativeElement;\n    let yBar = this.yBarViewChild.nativeElement;\n    function computeBarPosition() {\n      /* horizontal scroll */\n      let totalWidth = content.scrollWidth;\n      let ownWidth = content.clientWidth;\n      let bottom = (container.clientHeight - xBar.clientHeight) * -1;\n      this.scrollXRatio = ownWidth / totalWidth;\n      /* vertical scroll */\n      let totalHeight = content.scrollHeight;\n      let ownHeight = content.clientHeight;\n      let right = (container.clientWidth - yBar.clientWidth) * -1;\n      this.scrollYRatio = ownHeight / totalHeight;\n      return {\n        totalWidth,\n        ownWidth,\n        bottom,\n        totalHeight,\n        ownHeight,\n        right\n      };\n    }\n    this.requestAnimationFrame(() => {\n      let {\n        totalWidth,\n        ownWidth,\n        bottom,\n        totalHeight,\n        ownHeight,\n        right\n      } = computeBarPosition.call(this);\n      if (this.scrollXRatio >= 1) {\n        xBar.setAttribute('data-p-scrollpanel-hidden', 'true');\n        DomHandler.addClass(xBar, 'p-scrollpanel-hidden');\n      } else {\n        xBar.setAttribute('data-p-scrollpanel-hidden', 'false');\n        DomHandler.removeClass(xBar, 'p-scrollpanel-hidden');\n        const xBarWidth = Math.max(this.scrollXRatio * 100, 10);\n        const xBarLeft = content.scrollLeft * (100 - xBarWidth) / (totalWidth - ownWidth);\n        xBar.style.cssText = 'width:' + xBarWidth + '%; left:' + xBarLeft + '%;bottom:' + bottom + 'px;';\n      }\n      if (this.scrollYRatio >= 1) {\n        yBar.setAttribute('data-p-scrollpanel-hidden', 'true');\n        DomHandler.addClass(yBar, 'p-scrollpanel-hidden');\n      } else {\n        yBar.setAttribute('data-p-scrollpanel-hidden', 'false');\n        DomHandler.removeClass(yBar, 'p-scrollpanel-hidden');\n        const yBarHeight = Math.max(this.scrollYRatio * 100, 10);\n        const yBarTop = content.scrollTop * (100 - yBarHeight) / (totalHeight - ownHeight);\n        yBar.style.cssText = 'height:' + yBarHeight + '%; top: calc(' + yBarTop + '% - ' + xBar.clientHeight + 'px);right:' + right + 'px;';\n      }\n    });\n    this.cd.markForCheck();\n  }\n  onScroll(event) {\n    if (this.lastScrollLeft !== event.target.scrollLeft) {\n      this.lastScrollLeft = event.target.scrollLeft;\n      this.orientation = 'horizontal';\n    } else if (this.lastScrollTop !== event.target.scrollTop) {\n      this.lastScrollTop = event.target.scrollTop;\n      this.orientation = 'vertical';\n    }\n    this.moveBar();\n  }\n  onKeyDown(event) {\n    if (this.orientation === 'vertical') {\n      switch (event.code) {\n        case 'ArrowDown':\n          {\n            this.setTimer('scrollTop', this.step);\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowUp':\n          {\n            this.setTimer('scrollTop', this.step * -1);\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          {\n            event.preventDefault();\n            break;\n          }\n        default:\n          //no op\n          break;\n      }\n    } else if (this.orientation === 'horizontal') {\n      switch (event.code) {\n        case 'ArrowRight':\n          {\n            this.setTimer('scrollLeft', this.step);\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowLeft':\n          {\n            this.setTimer('scrollLeft', this.step * -1);\n            event.preventDefault();\n            break;\n          }\n        case 'ArrowDown':\n        case 'ArrowUp':\n          {\n            event.preventDefault();\n            break;\n          }\n        default:\n          //no op\n          break;\n      }\n    }\n  }\n  onKeyUp() {\n    this.clearTimer();\n  }\n  repeat(bar, step) {\n    this.contentViewChild.nativeElement[bar] += step;\n    this.moveBar();\n  }\n  setTimer(bar, step) {\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(bar, step);\n    }, 40);\n  }\n  clearTimer() {\n    if (this.timer) {\n      clearTimeout(this.timer);\n    }\n  }\n  bindDocumentMouseListeners() {\n    if (!this.documentMouseMoveListener) {\n      this.documentMouseMoveListener = e => {\n        this.onDocumentMouseMove(e);\n      };\n      this.document.addEventListener('mousemove', this.documentMouseMoveListener);\n    }\n    if (!this.documentMouseUpListener) {\n      this.documentMouseUpListener = e => {\n        this.onDocumentMouseUp(e);\n      };\n      this.document.addEventListener('mouseup', this.documentMouseUpListener);\n    }\n  }\n  unbindDocumentMouseListeners() {\n    if (this.documentMouseMoveListener) {\n      this.document.removeEventListener('mousemove', this.documentMouseMoveListener);\n      this.documentMouseMoveListener = null;\n    }\n    if (this.documentMouseUpListener) {\n      document.removeEventListener('mouseup', this.documentMouseUpListener);\n      this.documentMouseUpListener = null;\n    }\n  }\n  onYBarMouseDown(e) {\n    this.isYBarClicked = true;\n    this.yBarViewChild.nativeElement.focus();\n    this.lastPageY = e.pageY;\n    this.yBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'true');\n    DomHandler.addClass(this.yBarViewChild.nativeElement, 'p-scrollpanel-grabbed');\n    this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'true');\n    DomHandler.addClass(this.document.body, 'p-scrollpanel-grabbed');\n    this.bindDocumentMouseListeners();\n    e.preventDefault();\n  }\n  onXBarMouseDown(e) {\n    this.isXBarClicked = true;\n    this.xBarViewChild.nativeElement.focus();\n    this.lastPageX = e.pageX;\n    this.xBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n    DomHandler.addClass(this.xBarViewChild.nativeElement, 'p-scrollpanel-grabbed');\n    this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'false');\n    DomHandler.addClass(this.document.body, 'p-scrollpanel-grabbed');\n    this.bindDocumentMouseListeners();\n    e.preventDefault();\n  }\n  onDocumentMouseMove(e) {\n    if (this.isXBarClicked) {\n      this.onMouseMoveForXBar(e);\n    } else if (this.isYBarClicked) {\n      this.onMouseMoveForYBar(e);\n    } else {\n      this.onMouseMoveForXBar(e);\n      this.onMouseMoveForYBar(e);\n    }\n  }\n  onMouseMoveForXBar(e) {\n    let deltaX = e.pageX - this.lastPageX;\n    this.lastPageX = e.pageX;\n    this.requestAnimationFrame(() => {\n      this.contentViewChild.nativeElement.scrollLeft += deltaX / this.scrollXRatio;\n    });\n  }\n  onMouseMoveForYBar(e) {\n    let deltaY = e.pageY - this.lastPageY;\n    this.lastPageY = e.pageY;\n    this.requestAnimationFrame(() => {\n      this.contentViewChild.nativeElement.scrollTop += deltaY / this.scrollYRatio;\n    });\n  }\n  /**\n   * Scrolls the top location to the given value.\n   * @param scrollTop\n   * @group Method\n   */\n  scrollTop(scrollTop) {\n    let scrollableHeight = this.contentViewChild.nativeElement.scrollHeight - this.contentViewChild.nativeElement.clientHeight;\n    scrollTop = scrollTop > scrollableHeight ? scrollableHeight : scrollTop > 0 ? scrollTop : 0;\n    this.contentViewChild.nativeElement.scrollTop = scrollTop;\n  }\n  onFocus(event) {\n    if (this.xBarViewChild.nativeElement.isSameNode(event.target)) {\n      this.orientation = 'horizontal';\n    } else if (this.yBarViewChild.nativeElement.isSameNode(event.target)) {\n      this.orientation = 'vertical';\n    }\n  }\n  onBlur() {\n    if (this.orientation === 'horizontal') {\n      this.orientation = 'vertical';\n    }\n  }\n  onDocumentMouseUp(e) {\n    this.yBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n    DomHandler.removeClass(this.yBarViewChild.nativeElement, 'p-scrollpanel-grabbed');\n    this.xBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n    DomHandler.removeClass(this.xBarViewChild.nativeElement, 'p-scrollpanel-grabbed');\n    this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'false');\n    DomHandler.removeClass(this.document.body, 'p-scrollpanel-grabbed');\n    this.unbindDocumentMouseListeners();\n    this.isXBarClicked = false;\n    this.isYBarClicked = false;\n  }\n  requestAnimationFrame(f) {\n    let frame = window.requestAnimationFrame || this.timeoutFrame;\n    frame(f);\n  }\n  unbindListeners() {\n    if (this.windowResizeListener) {\n      this.windowResizeListener();\n      this.windowResizeListener = null;\n    }\n    if (this.contentScrollListener) {\n      this.contentScrollListener();\n      this.contentScrollListener = null;\n    }\n    if (this.mouseEnterListener) {\n      this.mouseEnterListener();\n      this.mouseEnterListener = null;\n    }\n    if (this.xBarMouseDownListener) {\n      this.xBarMouseDownListener();\n      this.xBarMouseDownListener = null;\n    }\n    if (this.yBarMouseDownListener) {\n      this.yBarMouseDownListener();\n      this.yBarMouseDownListener = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.initialized) {\n      this.unbindListeners();\n    }\n  }\n  /**\n   * Refreshes the position and size of the scrollbar.\n   * @group Method\n   */\n  refresh() {\n    this.moveBar();\n  }\n  static ɵfac = function ScrollPanel_Factory(t) {\n    return new (t || ScrollPanel)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ScrollPanel,\n    selectors: [[\"p-scrollPanel\"]],\n    contentQueries: function ScrollPanel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function ScrollPanel_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.xBarViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.yBarViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      step: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"step\", \"step\", numberAttribute]\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c4,\n    decls: 11,\n    vars: 16,\n    consts: [[\"container\", \"\"], [\"content\", \"\"], [\"xBar\", \"\"], [\"yBar\", \"\"], [3, \"ngClass\", \"ngStyle\"], [1, \"p-scrollpanel-wrapper\"], [1, \"p-scrollpanel-content\", 3, \"mouseenter\", \"scroll\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"role\", \"scrollbar\", 1, \"p-scrollpanel-bar\", \"p-scrollpanel-bar-x\", 3, \"mousedown\", \"keydown\", \"keyup\", \"focus\", \"blur\"], [\"tabindex\", \"0\", \"role\", \"scrollbar\", 1, \"p-scrollpanel-bar\", \"p-scrollpanel-bar-y\", 3, \"mousedown\", \"keydown\", \"keyup\", \"focus\"]],\n    template: function ScrollPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 4, 0)(2, \"div\", 5)(3, \"div\", 6, 1);\n        i0.ɵɵlistener(\"mouseenter\", function ScrollPanel_Template_div_mouseenter_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.moveBar());\n        })(\"scroll\", function ScrollPanel_Template_div_scroll_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onScroll($event));\n        });\n        i0.ɵɵprojection(5);\n        i0.ɵɵtemplate(6, ScrollPanel_ng_container_6_Template, 1, 0, \"ng-container\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 8, 2);\n        i0.ɵɵlistener(\"mousedown\", function ScrollPanel_Template_div_mousedown_7_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onXBarMouseDown($event));\n        })(\"keydown\", function ScrollPanel_Template_div_keydown_7_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"keyup\", function ScrollPanel_Template_div_keyup_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyUp());\n        })(\"focus\", function ScrollPanel_Template_div_focus_7_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus($event));\n        })(\"blur\", function ScrollPanel_Template_div_blur_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 9, 3);\n        i0.ɵɵlistener(\"mousedown\", function ScrollPanel_Template_div_mousedown_9_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onYBarMouseDown($event));\n        })(\"keydown\", function ScrollPanel_Template_div_keydown_9_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"keyup\", function ScrollPanel_Template_div_keyup_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyUp());\n        })(\"focus\", function ScrollPanel_Template_div_focus_9_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus($event));\n        });\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-scrollpanel p-component\")(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"scrollpanel\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"data-pc-section\", \"wrapper\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"content\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"aria-orientation\", \"horizontal\")(\"aria-valuenow\", ctx.lastScrollLeft)(\"data-pc-section\", \"barx\")(\"aria-controls\", ctx.contentId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-orientation\", \"vertical\")(\"aria-valuenow\", ctx.lastScrollTop)(\"data-pc-section\", \"bary\")(\"aria-controls\", ctx.contentId);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgTemplateOutlet, i1.NgStyle],\n    styles: [\"@layer primeng{.p-scrollpanel-wrapper{overflow:hidden;width:100%;height:100%;position:relative;float:left}.p-scrollpanel-content{height:calc(100% + 18px);width:calc(100% + 18px);padding:0 18px 18px 0;position:relative;overflow:auto;box-sizing:border-box}.p-scrollpanel-bar{position:relative;background:#c1c1c1;border-radius:3px;cursor:pointer;opacity:0;transition:opacity .25s linear}.p-scrollpanel-bar-y{width:9px;top:0}.p-scrollpanel-bar-x{height:9px;bottom:0}.p-scrollpanel-hidden{visibility:hidden}.p-scrollpanel:hover .p-scrollpanel-bar,.p-scrollpanel:active .p-scrollpanel-bar{opacity:1}.p-scrollpanel-grabbed{-webkit-user-select:none;user-select:none}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollPanel, [{\n    type: Component,\n    args: [{\n      selector: 'p-scrollPanel',\n      template: `\n        <div #container [ngClass]=\"'p-scrollpanel p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'scrollpanel'\">\n            <div class=\"p-scrollpanel-wrapper\" [attr.data-pc-section]=\"'wrapper'\">\n                <div #content class=\"p-scrollpanel-content\" [attr.data-pc-section]=\"'content'\" (mouseenter)=\"moveBar()\" (scroll)=\"onScroll($event)\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n            <div\n                #xBar\n                class=\"p-scrollpanel-bar p-scrollpanel-bar-x\"\n                tabindex=\"0\"\n                role=\"scrollbar\"\n                [attr.aria-orientation]=\"'horizontal'\"\n                [attr.aria-valuenow]=\"lastScrollLeft\"\n                [attr.data-pc-section]=\"'barx'\"\n                [attr.aria-controls]=\"contentId\"\n                (mousedown)=\"onXBarMouseDown($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (keyup)=\"onKeyUp()\"\n                (focus)=\"onFocus($event)\"\n                (blur)=\"onBlur()\"\n            ></div>\n            <div\n                #yBar\n                class=\"p-scrollpanel-bar p-scrollpanel-bar-y\"\n                tabindex=\"0\"\n                role=\"scrollbar\"\n                [attr.aria-orientation]=\"'vertical'\"\n                [attr.aria-valuenow]=\"lastScrollTop\"\n                [attr.data-pc-section]=\"'bary'\"\n                [attr.aria-controls]=\"contentId\"\n                (mousedown)=\"onYBarMouseDown($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (keyup)=\"onKeyUp()\"\n                (focus)=\"onFocus($event)\"\n            ></div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-scrollpanel-wrapper{overflow:hidden;width:100%;height:100%;position:relative;float:left}.p-scrollpanel-content{height:calc(100% + 18px);width:calc(100% + 18px);padding:0 18px 18px 0;position:relative;overflow:auto;box-sizing:border-box}.p-scrollpanel-bar{position:relative;background:#c1c1c1;border-radius:3px;cursor:pointer;opacity:0;transition:opacity .25s linear}.p-scrollpanel-bar-y{width:9px;top:0}.p-scrollpanel-bar-x{height:9px;bottom:0}.p-scrollpanel-hidden{visibility:hidden}.p-scrollpanel:hover .p-scrollpanel-bar,.p-scrollpanel:active .p-scrollpanel-bar{opacity:1}.p-scrollpanel-grabbed{-webkit-user-select:none;user-select:none}}\\n\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.Renderer2\n  }], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    step: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    xBarViewChild: [{\n      type: ViewChild,\n      args: ['xBar']\n    }],\n    yBarViewChild: [{\n      type: ViewChild,\n      args: ['yBar']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ScrollPanelModule {\n  static ɵfac = function ScrollPanelModule_Factory(t) {\n    return new (t || ScrollPanelModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollPanelModule,\n    declarations: [ScrollPanel],\n    imports: [CommonModule],\n    exports: [ScrollPanel]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollPanelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ScrollPanel],\n      declarations: [ScrollPanel]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ScrollPanel, ScrollPanelModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe,QAAM,WAAW,IAAI,CAAC;AAAA,EACrC,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB;AAAA,EACA,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,YAAY,IAAI,MAAM,IAAIA,WAAU,UAAU;AACxD,SAAK,aAAa;AAClB,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,WAAWA;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY,kBAAkB,IAAI;AAAA,EACzC;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,KAAK,kBAAkB,MAAM;AAChC,aAAK,QAAQ;AACb,aAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AACrC,aAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,aAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,aAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,aAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,aAAK,uBAAuB,KAAK,SAAS,OAAO,QAAQ,UAAU,KAAK,OAAO;AAC/E,aAAK,wBAAwB,KAAK,SAAS,OAAO,KAAK,iBAAiB,eAAe,UAAU,KAAK,OAAO;AAC7G,aAAK,qBAAqB,KAAK,SAAS,OAAO,KAAK,iBAAiB,eAAe,cAAc,KAAK,OAAO;AAC9G,aAAK,wBAAwB,KAAK,SAAS,OAAO,KAAK,cAAc,eAAe,aAAa,KAAK,eAAe;AACrH,aAAK,wBAAwB,KAAK,SAAS,OAAO,KAAK,cAAc,eAAe,aAAa,KAAK,eAAe;AACrH,aAAK,yBAAyB;AAC9B,aAAK,cAAc;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AACE,eAAK,kBAAkB,KAAK;AAC5B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,2BAA2B;AACzB,QAAI,YAAY,KAAK,mBAAmB;AACxC,QAAI,UAAU,KAAK,iBAAiB;AACpC,QAAI,OAAO,KAAK,cAAc;AAC9B,UAAMC,UAAS,KAAK,SAAS;AAC7B,QAAI,kBAAkBA,QAAO,iBAAiB,SAAS,GACrD,aAAaA,QAAO,iBAAiB,IAAI,GACzC,sBAAsB,WAAW,UAAU,SAAS,IAAI,SAAS,WAAW,QAAQ,GAAG,EAAE;AAC3F,QAAI,gBAAgB,YAAY,KAAK,UAAU,uBAAuB,GAAG;AACvE,UAAI,QAAQ,eAAe,SAAS,WAAW,QAAQ,GAAG,EAAE,IAAI,SAAS,gBAAgB,YAAY,GAAG,EAAE,GAAG;AAC3G,kBAAU,MAAM,SAAS,gBAAgB,YAAY;AAAA,MACvD,OAAO;AACL,kBAAU,MAAM,SAAS,QAAQ,eAAe,WAAW,gBAAgB,UAAU,IAAI,WAAW,gBAAgB,aAAa,IAAI,WAAW,gBAAgB,cAAc,IAAI,WAAW,gBAAgB,iBAAiB,IAAI;AAAA,MACpO;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,YAAY,KAAK,mBAAmB;AACxC,QAAI,UAAU,KAAK,iBAAiB;AACpC,QAAI,OAAO,KAAK,cAAc;AAC9B,QAAI,OAAO,KAAK,cAAc;AAC9B,aAAS,qBAAqB;AAE5B,UAAI,aAAa,QAAQ;AACzB,UAAI,WAAW,QAAQ;AACvB,UAAI,UAAU,UAAU,eAAe,KAAK,gBAAgB;AAC5D,WAAK,eAAe,WAAW;AAE/B,UAAI,cAAc,QAAQ;AAC1B,UAAI,YAAY,QAAQ;AACxB,UAAI,SAAS,UAAU,cAAc,KAAK,eAAe;AACzD,WAAK,eAAe,YAAY;AAChC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,sBAAsB,MAAM;AAC/B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,mBAAmB,KAAK,IAAI;AAChC,UAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAK,aAAa,6BAA6B,MAAM;AACrD,mBAAW,SAAS,MAAM,sBAAsB;AAAA,MAClD,OAAO;AACL,aAAK,aAAa,6BAA6B,OAAO;AACtD,mBAAW,YAAY,MAAM,sBAAsB;AACnD,cAAM,YAAY,KAAK,IAAI,KAAK,eAAe,KAAK,EAAE;AACtD,cAAM,WAAW,QAAQ,cAAc,MAAM,cAAc,aAAa;AACxE,aAAK,MAAM,UAAU,WAAW,YAAY,aAAa,WAAW,cAAc,SAAS;AAAA,MAC7F;AACA,UAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAK,aAAa,6BAA6B,MAAM;AACrD,mBAAW,SAAS,MAAM,sBAAsB;AAAA,MAClD,OAAO;AACL,aAAK,aAAa,6BAA6B,OAAO;AACtD,mBAAW,YAAY,MAAM,sBAAsB;AACnD,cAAM,aAAa,KAAK,IAAI,KAAK,eAAe,KAAK,EAAE;AACvD,cAAM,UAAU,QAAQ,aAAa,MAAM,eAAe,cAAc;AACxE,aAAK,MAAM,UAAU,YAAY,aAAa,kBAAkB,UAAU,SAAS,KAAK,eAAe,eAAe,QAAQ;AAAA,MAChI;AAAA,IACF,CAAC;AACD,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,mBAAmB,MAAM,OAAO,YAAY;AACnD,WAAK,iBAAiB,MAAM,OAAO;AACnC,WAAK,cAAc;AAAA,IACrB,WAAW,KAAK,kBAAkB,MAAM,OAAO,WAAW;AACxD,WAAK,gBAAgB,MAAM,OAAO;AAClC,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU,OAAO;AACf,QAAI,KAAK,gBAAgB,YAAY;AACnC,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,aACH;AACE,eAAK,SAAS,aAAa,KAAK,IAAI;AACpC,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,eAAK,SAAS,aAAa,KAAK,OAAO,EAAE;AACzC,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF,KAAK;AAAA,QACL,KAAK,cACH;AACE,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF;AAEE;AAAA,MACJ;AAAA,IACF,WAAW,KAAK,gBAAgB,cAAc;AAC5C,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,cACH;AACE,eAAK,SAAS,cAAc,KAAK,IAAI;AACrC,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF,KAAK,aACH;AACE,eAAK,SAAS,cAAc,KAAK,OAAO,EAAE;AAC1C,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF,KAAK;AAAA,QACL,KAAK,WACH;AACE,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,QACF;AAEE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO,KAAK,MAAM;AAChB,SAAK,iBAAiB,cAAc,GAAG,KAAK;AAC5C,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,KAAK,MAAM;AAClB,SAAK,WAAW;AAChB,SAAK,QAAQ,WAAW,MAAM;AAC5B,WAAK,OAAO,KAAK,IAAI;AAAA,IACvB,GAAG,EAAE;AAAA,EACP;AAAA,EACA,aAAa;AACX,QAAI,KAAK,OAAO;AACd,mBAAa,KAAK,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,CAAC,KAAK,2BAA2B;AACnC,WAAK,4BAA4B,OAAK;AACpC,aAAK,oBAAoB,CAAC;AAAA,MAC5B;AACA,WAAK,SAAS,iBAAiB,aAAa,KAAK,yBAAyB;AAAA,IAC5E;AACA,QAAI,CAAC,KAAK,yBAAyB;AACjC,WAAK,0BAA0B,OAAK;AAClC,aAAK,kBAAkB,CAAC;AAAA,MAC1B;AACA,WAAK,SAAS,iBAAiB,WAAW,KAAK,uBAAuB;AAAA,IACxE;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,2BAA2B;AAClC,WAAK,SAAS,oBAAoB,aAAa,KAAK,yBAAyB;AAC7E,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,KAAK,yBAAyB;AAChC,eAAS,oBAAoB,WAAW,KAAK,uBAAuB;AACpE,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,gBAAgB,GAAG;AACjB,SAAK,gBAAgB;AACrB,SAAK,cAAc,cAAc,MAAM;AACvC,SAAK,YAAY,EAAE;AACnB,SAAK,cAAc,cAAc,aAAa,8BAA8B,MAAM;AAClF,eAAW,SAAS,KAAK,cAAc,eAAe,uBAAuB;AAC7E,SAAK,SAAS,KAAK,aAAa,8BAA8B,MAAM;AACpE,eAAW,SAAS,KAAK,SAAS,MAAM,uBAAuB;AAC/D,SAAK,2BAA2B;AAChC,MAAE,eAAe;AAAA,EACnB;AAAA,EACA,gBAAgB,GAAG;AACjB,SAAK,gBAAgB;AACrB,SAAK,cAAc,cAAc,MAAM;AACvC,SAAK,YAAY,EAAE;AACnB,SAAK,cAAc,cAAc,aAAa,8BAA8B,OAAO;AACnF,eAAW,SAAS,KAAK,cAAc,eAAe,uBAAuB;AAC7E,SAAK,SAAS,KAAK,aAAa,8BAA8B,OAAO;AACrE,eAAW,SAAS,KAAK,SAAS,MAAM,uBAAuB;AAC/D,SAAK,2BAA2B;AAChC,MAAE,eAAe;AAAA,EACnB;AAAA,EACA,oBAAoB,GAAG;AACrB,QAAI,KAAK,eAAe;AACtB,WAAK,mBAAmB,CAAC;AAAA,IAC3B,WAAW,KAAK,eAAe;AAC7B,WAAK,mBAAmB,CAAC;AAAA,IAC3B,OAAO;AACL,WAAK,mBAAmB,CAAC;AACzB,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,mBAAmB,GAAG;AACpB,QAAI,SAAS,EAAE,QAAQ,KAAK;AAC5B,SAAK,YAAY,EAAE;AACnB,SAAK,sBAAsB,MAAM;AAC/B,WAAK,iBAAiB,cAAc,cAAc,SAAS,KAAK;AAAA,IAClE,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,GAAG;AACpB,QAAI,SAAS,EAAE,QAAQ,KAAK;AAC5B,SAAK,YAAY,EAAE;AACnB,SAAK,sBAAsB,MAAM;AAC/B,WAAK,iBAAiB,cAAc,aAAa,SAAS,KAAK;AAAA,IACjE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,WAAW;AACnB,QAAI,mBAAmB,KAAK,iBAAiB,cAAc,eAAe,KAAK,iBAAiB,cAAc;AAC9G,gBAAY,YAAY,mBAAmB,mBAAmB,YAAY,IAAI,YAAY;AAC1F,SAAK,iBAAiB,cAAc,YAAY;AAAA,EAClD;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,KAAK,cAAc,cAAc,WAAW,MAAM,MAAM,GAAG;AAC7D,WAAK,cAAc;AAAA,IACrB,WAAW,KAAK,cAAc,cAAc,WAAW,MAAM,MAAM,GAAG;AACpE,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,KAAK,gBAAgB,cAAc;AACrC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,kBAAkB,GAAG;AACnB,SAAK,cAAc,cAAc,aAAa,8BAA8B,OAAO;AACnF,eAAW,YAAY,KAAK,cAAc,eAAe,uBAAuB;AAChF,SAAK,cAAc,cAAc,aAAa,8BAA8B,OAAO;AACnF,eAAW,YAAY,KAAK,cAAc,eAAe,uBAAuB;AAChF,SAAK,SAAS,KAAK,aAAa,8BAA8B,OAAO;AACrE,eAAW,YAAY,KAAK,SAAS,MAAM,uBAAuB;AAClE,SAAK,6BAA6B;AAClC,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,sBAAsB,GAAG;AACvB,QAAI,QAAQ,OAAO,yBAAyB,KAAK;AACjD,UAAM,CAAC;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,uBAAuB;AAAA,IAC9B;AACA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB;AACxB,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,aAAa;AACpB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,GAAG;AAC5C,WAAO,KAAK,KAAK,cAAgB,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,SAAS,CAAC;AAAA,EACvP;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM,CAAI,WAAa,4BAA4B,QAAQ,QAAQ,eAAe;AAAA,IACpF;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,cAAc,QAAQ,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,YAAY,KAAK,QAAQ,aAAa,GAAG,qBAAqB,uBAAuB,GAAG,aAAa,WAAW,SAAS,SAAS,MAAM,GAAG,CAAC,YAAY,KAAK,QAAQ,aAAa,GAAG,qBAAqB,uBAAuB,GAAG,aAAa,WAAW,SAAS,OAAO,CAAC;AAAA,IACle,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC;AAC7D,QAAG,WAAW,cAAc,SAAS,iDAAiD;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,UAAU,SAAS,2CAA2C,QAAQ;AACvE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,SAAS,MAAM,CAAC;AAAA,QAC5C,CAAC;AACD,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,gBAAgB,CAAC;AAC7E,QAAG,aAAa,EAAE;AAClB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,aAAa,SAAS,8CAA8C,QAAQ;AACxF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,gBAAgB,MAAM,CAAC;AAAA,QACnD,CAAC,EAAE,WAAW,SAAS,4CAA4C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,SAAS,SAAS,4CAA4C;AAC/D,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,QAC3C,CAAC,EAAE,QAAQ,SAAS,2CAA2C;AAC7D,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,QACpC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,aAAa,SAAS,8CAA8C,QAAQ;AACxF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,gBAAgB,MAAM,CAAC;AAAA,QACnD,CAAC,EAAE,WAAW,SAAS,4CAA4C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,QAC7C,CAAC,EAAE,SAAS,SAAS,4CAA4C;AAC/D,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,2BAA2B,EAAE,WAAW,IAAI,KAAK;AAC1E,QAAG,YAAY,gBAAgB,aAAa;AAC5C,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,mBAAmB,SAAS;AAC3C,QAAG,UAAU;AACb,QAAG,YAAY,mBAAmB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,QAAG,UAAU;AACb,QAAG,YAAY,oBAAoB,YAAY,EAAE,iBAAiB,IAAI,cAAc,EAAE,mBAAmB,MAAM,EAAE,iBAAiB,IAAI,SAAS;AAC/I,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,oBAAoB,UAAU,EAAE,iBAAiB,IAAI,aAAa,EAAE,mBAAmB,MAAM,EAAE,iBAAiB,IAAI,SAAS;AAAA,MAC9I;AAAA,IACF;AAAA,IACA,cAAc,CAAI,SAAY,kBAAqB,OAAO;AAAA,IAC1D,QAAQ,CAAC,upBAAupB;AAAA,IAChqB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuCV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,upBAAupB;AAAA,IAClqB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,GAAG;AAClD,WAAO,KAAK,KAAK,oBAAmB;AAAA,EACtC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,WAAW;AAAA,IAC1B,SAAS,CAAC,YAAY;AAAA,IACtB,SAAS,CAAC,WAAW;AAAA,EACvB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,WAAW;AAAA,MACrB,cAAc,CAAC,WAAW;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["document", "window"]}