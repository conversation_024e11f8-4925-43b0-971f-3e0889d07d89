﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.WorkingResultSynthetic;
using Application.Infrastructure.Models.Response;

namespace Application.Infrastructure.Services.Abstractions;

public interface IWorkingResultSyntheticService
{
    Task<BaseSearchResponse<WorkingResultSyntheticResponse>> SearchWorkingResultSyntheticAsync(SearchWorkingResultSyntheticRequest request);
    Task<WorkingResultSyntheticResponse> CreateWorkingResultSyntheticAsync(CreateWorkingResultSyntheticRequest request);
    Task<bool> UpdateWorkingResultSyntheticAsync(UpdateWorkingResultSyntheticRequest request);
    Task<bool> DeleteWorkingResultSyntheticAsync(DeleteWorkingResultSyntheticRequest request);
}