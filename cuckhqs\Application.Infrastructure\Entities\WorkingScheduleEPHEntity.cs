﻿using Application.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleEPHEntity : BaseEntity<int>
    {
        public Guid EmployeeId { get; set; }
        public int WorkingScheduleId { get; set; }
        [NotMapped]
        public string WorkingScheduleEPHName { get; set; }

        public WorkingScheduleEntity WorkingSchedule { get; set; }
    }
}
