﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Entities;
using Azure.Core;

namespace Application.Infrastructure.Models.Response
{
    public class AdvertisementResponse
    {
        public int Id { get; set; }
        public string? AdvertisementCode { set; get; }
        public string? AdvertisementName { set; get; }
        public DateTime? Start { set; get; }
        public int? StartHour { set; get; }
        public int? StartMinute { set; get; }
        public DateTime? Ends { set; get; }
        public int? EndsHour { set; get; }
        public int? EndsMinute { set; get; }
        public int? Year { set; get; }
        public bool? Active { set; get; }
        public int? SortOrder { set; get; }
        public DateTime? CreatedDate { set; get; }
        public DateTime? ModifiedDate { set; get; }
        public string? IPAddress { set; get; }
        public string? ModifiedBy { set; get; }
        public string? CreatedBy { set; get; }

        public static Expression<Func<AdvertisementEntity, AdvertisementResponse>> Expression
        {
            get
            {
                return entity => new AdvertisementResponse()
                {
                    Id = entity.Id,
                    AdvertisementCode = entity.AdvertisementCode,
                    AdvertisementName = entity.AdvertisementName,
                    Start = entity.Start,
                    StartHour = entity.StartHour,
                    StartMinute = entity.StartMinute,
                    Ends = entity.Ends,
                    EndsHour = entity.EndsHour,
                    EndsMinute = entity.EndsMinute,
                    Year = entity.Year,
                    Active = entity.Active,
                    SortOrder = entity.SortOrder,
                    CreatedDate = entity.CreatedDate,
                    ModifiedDate = entity.ModifiedDate,
                    IPAddress = entity.IPAddress,
                    ModifiedBy = entity.ModifiedBy,
                    CreatedBy = entity.CreatedBy
                };
            }
        }

        public static AdvertisementResponse Create(AdvertisementEntity response)
        {
            if (response == null) return null;

            return Expression.Compile().Invoke(response);
        }
    }
}
