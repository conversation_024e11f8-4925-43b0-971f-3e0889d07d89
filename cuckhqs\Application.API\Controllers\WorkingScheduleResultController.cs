﻿using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Request.WorkingSchedule;
using Application.Infrastructure.Models.Request.WorkingScheduleResult;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class WorkingScheduleResultController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IWorkingScheduleResultService _workingScheduleResultService;
        private readonly string _filePath;


        public WorkingScheduleResultController(IWorkingScheduleResultService WorkingScheduleResult, IWebHostEnvironment webHostEnvironment)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            _workingScheduleResultService = WorkingScheduleResult;
            _webHostEnvironment = webHostEnvironment;
        }


        [HttpPost("Create")]
        public async Task<IActionResult> CreateWorkingScheduleResult([FromBody] CreateWorkingScheduleResultRequest request)
        {
            try
            {
                var response = await _workingScheduleResultService.CreateWorkingScheduleResultAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpGet("GetWorkingScheduleResultByWorkingScheduleId")]
        public async Task<IActionResult> GetWorkingScheduleResultByWorkingScheduleId([FromQuery] int WorkingScheduleId)
        {
            var response = await _workingScheduleResultService.GetWorkingScheduleResultByWorkingScheduleId(WorkingScheduleId);
            return Ok(response);
        }
    }
}
