﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Response;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IAdvertisementService
    {
        Task<BaseSearchResponse<AdvertisementResponse>> SearchAdvertisementAsync(SearchAdvertisementRequest request);
        Task<AdvertisementResponse> CreateAdvertisementAsync(CreateAdvertisementRequest request);
        Task<AdvertisementResponse> UpdateAdvertisementAsync(UpdateAdvertisementRequest request);
        Task<bool> DeleteAdvertisementAsync(DeleteAdvertisementRequest request);

    }
}
