﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class CountryRepository : GenericRepository<CountryEntity, int>, ICountryRepository
    {
        public AppDbContext Context { get; set; }

        public CountryRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(CountryEntity requestObject, CountryEntity targetObject)
        {
            targetObject.CountryCode = requestObject.CountryCode;
            targetObject.CountryName = requestObject.CountryName;
            targetObject.LanguageName = requestObject.LanguageName;
            targetObject.Class = requestObject.Class;
            targetObject.Active = requestObject.Active;
            targetObject.VietnameseName = requestObject.VietnameseName;
        }
    }
}
