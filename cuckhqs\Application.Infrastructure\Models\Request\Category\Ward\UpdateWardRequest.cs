﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.District;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Ward
{
    public class UpdateWardRequest : CreateWardRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateWardRequest, WardEntity>> Expression
        {
            get
            {
                return entity => new WardEntity
                {
                    Id = entity.Id,
                    ProvinceId = entity.ProvinceId,
                    DistrictId = entity.DistrictId,
                    WardCode = entity.WardCode,
                    WardName = entity.WardName,
                    Description = entity.Description,
                    Active = entity.Active
                };
            }
        }

        public static WardEntity Create(UpdateWardRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
