﻿using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Azure.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class AdvertisementController : ControllerBase
    {
        private readonly IAdvertisementService _advertisementService;

        public AdvertisementController(IAdvertisementService advertisementService)
        {
            _advertisementService = advertisementService;
        }
        //[AllowAnonymous]
        [HttpPost("Search")]
        public async Task<IActionResult> SearchAdvertisementAsync([FromBody] SearchAdvertisementRequest request)
        {
            try
            {
                var response = await _advertisementService.SearchAdvertisementAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateAdvertisementAsync([FromBody] CreateAdvertisementRequest request)
        {
            try
            {
                var response = await _advertisementService.CreateAdvertisementAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateAdvertisementAsync([FromBody] UpdateAdvertisementRequest request)
        {
            try
            {
                var response = await _advertisementService.UpdateAdvertisementAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeleteAdvertisementAsync([FromBody] DeleteAdvertisementRequest request)
        {
            try
            {
                var response = await _advertisementService.DeleteAdvertisementAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
