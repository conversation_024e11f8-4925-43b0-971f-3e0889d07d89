﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class EmployeeResponse
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public int? OrganizationUnitId { get; set; }
        public int? Classify { get; set; }
        public string? OrganizationUnitName { get; set; }
        public string? EmployeeCode { get; set; }
        public string? Fullname { get; set; }
        public int? RankId { get; set; }
        public int? Gender { get; set; }
        public string? Gender_Name { get; set; }
        public int? AcademicRankId { get; set; }
        public int? YearOfAcademicRank { get; set; }
        public int? DegreeId { get; set; }
        public string? Degree_Name { get; set; }
        public int? YearOfDegree { get; set; }
        public int? PositionId { get; set; }
        public string? PositionName { get; set; }
        public short? PositionType { get; set; }
        public int? PartyPositionId { get; set; }
        public DateTime? BirthDay { get; set; }
        public bool? Owned { get; set; }
        public bool? Active { get; set; }
        public bool? ActiveAccount { get; set; }
        public bool? IsAdministrator { get; set; }
        public string? RankName { get; set; }

        #region thêm sau cho dễ nhớ
        public string? LongFullName { get; set; }
        public string? BirthPlace { get; set; }
        public string? HomeLand { get; set; }
        public string? NativeAddress { get; set; }
        public string? Tel { get; set; }
        public string? HomeTel { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? OfficeAddress { get; set; }
        public string? HomeAddress { get; set; }
        public string? Website { get; set; }
        public string? Description { get; set; }
        public string? IDNumber { get; set; }
        public string? IssuedBy { get; set; }
        public DateTime? DateBy { get; set; }
        public string? AccountNumber { get; set; }
        public string? Bank { get; set; }
        public string? Avatar { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        #endregion

        public static Expression<Func<EmployeeCEntity, EmployeeResponse>> Expression
        {
            get
            {
                return s => new EmployeeResponse()
                {
                    Id = s.Id,
                    FirstName = s.FirstName,
                    LastName = s.LastName,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Classify = s.Classify,
                    //   OrganizationUnitName = "",
                    EmployeeCode = s.EmployeeCode,
                    Fullname = s.FullName,
                    RankId = s.RankId,
                    // RankName = s.RankName,
                    Gender = s.Gender,
                    //  Gender_Name = s.Gender == 1 ? "Nam" : "Nữ",
                    AcademicRankId = s.AcademicRankId,
                    YearOfAcademicRank = s.YearOfAcademicRank,
                    DegreeId = s.DegreeId,
                    //  Degree_Name = "",
                    YearOfDegree = s.YearOfDegree,
                    PositionId = s.PositionId,
                    // PositionName = "",
                    PositionType = s.PositionType,
                    PartyPositionId = s.PartyPositionId,
                    BirthDay = s.BirthDay,
                    Owned = s.Owned,
                    Active = s.Active,
                    ActiveAccount = s.ActiveAccount,
                    IsAdministrator = s.IsAdministrator,
                    //  RankName = s.RankName
                    LongFullName = s.LongFullName,
                    BirthPlace = s.BirthPlace,
                    HomeLand = s.HomeLand,
                    NativeAddress = s.NativeAddress,
                    Tel = s.Tel,
                    HomeTel = s.HomeTel,
                    Mobile = s.Mobile,
                    Fax = s.Fax,
                    Email = s.Email,
                    OfficeAddress = s.OfficeAddress,
                    HomeAddress = s.HomeAddress,
                    Website = s.Website,
                    Description = s.Description,
                    IDNumber = s.IDNumber,
                    IssuedBy = s.IssuedBy,
                    DateBy = s.DateBy,
                    AccountNumber = s.AccountNumber,
                    Bank = s.Bank,
                    Avatar = s.Avatar,
                    SortOrder = s.SortOrder,
                    //CreatedDate = s.CreatedDate,
                    //ModifiedDate = s.ModifiedDate,
                    //IPAddress = s.IPAddress,
                    //ModifiedBy = s.ModifiedBy,
                    //CreatedBy = s.CreatedBy
                };
            }
        }

        public static EmployeeResponse Create(EmployeeCEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
