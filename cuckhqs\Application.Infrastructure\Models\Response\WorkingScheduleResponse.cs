﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response
{
    public class WorkingScheduleResponse
    {
        public bool Success { get; set; }
        public int ErrorType { get; set; }
        public List<ScheduleData> Data { get; set; }
        public int Id { get; set; }
        public int Total { get; set; }
        public int Code { get; set; }
    }

    public class ScheduleData
    {
        public int Id { get; set; }
        public int? WorkingScheduleResultId { get; set; }
        public short? Classify { get; set; }
        public int? OrganizationUnitId { get; set; }
        public bool? Register { get; set; }
        public int? Year { get; set; }
        public int? Week { get; set; }
        public DateTime? Date { get; set; }
        public string? CoChair { get; set; }
        public short? Time { get; set; }
        public DateTime? TimeFrom { get; set; }
        public DateTime? TimeTo { get; set; }
        public string? Place { get; set; }
        public string? Contents { get; set; }
        public string? Member { get; set; }
        public string? Note { get; set; }
        public bool? Message { get; set; }
        public bool? Active { get; set; }
        public int? OrganizationUnitId_Chair { get; set; }
        public string? OrganizationUnitIdName { get; set; }
        public List<string>? WorkingScheduleC { get; set; }
        public string? WorkingScheduleCName { get; set; }
        public string? WorkingScheduleCName_Short { get; set; }
        public List<string>? WorkingScheduleEP { get; set; }
        public string? WorkingScheduleEPName { get; set; }
        public string? WorkingScheduleEPName_Short { get; set; }
        public List<string>? WorkingScheduleEPH { get; set; }
        public string? WorkingScheduleEPHName { get; set; }
        public string? WorkingScheduleEPHName_Short { get; set; }
        public List<string>? WorkingScheduleOU { get; set; }
        public string? WorkingScheduleOUName { get; set; }
        public string? WorkingScheduleOUName_Short { get; set; }
        public string? ShortOrganizationUnitName { get; set; }
        public int? Announced { get; set; }
        public string? OrganizationUnitId_ChairName { get; set; }
        public int? MTEntityState { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }
        public short? SortOrder { get; set; }
        public List<FileData> File { get; set; }
    }
}
