﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Request.WorkingResult;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Services.Abstractions
{
    public interface IEmployeeCService
    {
        Task<List<EmployeeCResponse>> GetAlDepartmentHeadAsync();
        Task<List<EmployeeCResponse>> GetAllEmployeesAsync();
        Task<BaseSearchResponse<EmployeeResponse>> SearchEmployeeAsync(SearchEmployeeRequest request);
        Task<List<EmployeeResponse>> GetEmployeeById(Guid Id);

        Task<EmployeeResponse> CreateEmployeeAsync(CreateEmployeeRequest request);

        Task<bool> UpdateEmployeeAsync(UpdateEmployeeRequest request);

        Task<bool> DeleteEmployeeAsync(DeleteEmployeeRequest request);
    }
}
