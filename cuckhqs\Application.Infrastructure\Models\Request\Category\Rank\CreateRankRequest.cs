﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Advertisement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Rank
{
    public class CreateRankRequest
    {
        public string? RankCode { get; set; }
        public string? RankName { get; set; }
        public string? Description { get; set; }
        public static Expression<Func<CreateRankRequest, RankEntity>> Expression
        {
            get
            {
                return entity => new RankEntity
                {
                    RankCode = entity.RankCode,
                    RankName = entity.RankName,
                    Description = entity.Description,
                    Active = true,
                };
            }
        }

        public static RankEntity Create(CreateRankRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
