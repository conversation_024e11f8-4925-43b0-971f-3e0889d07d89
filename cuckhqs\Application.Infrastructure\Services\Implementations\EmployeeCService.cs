﻿using Application.Infrastructure.Commons;
using Application.Infrastructure.Exceptions;
using Application.Infrastructure.Models.Request.Category.Employee;
using Application.Infrastructure.Models.Response;
using Application.Infrastructure.Models.Response.Category;
using Application.Infrastructure.Repositories.Abstractions;
using Application.Infrastructure.Repositories.Abstractions.IEmployee;
using Application.Infrastructure.Repositories.Abstractions.IOrganizationUnit;
using Application.Infrastructure.Services.Abstractions;
//using ExcelDataReader.Log;
using Microsoft.Data.SqlClient;
using ql_tb_vk_vt.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Microsoft.EntityFrameworkCore;
using Application.Infrastructure.Models.Request.Advertisement;
using Application.Infrastructure.Models.Request.WorkingResult;
using Microsoft.Extensions.Configuration;

namespace Application.Infrastructure.Services.Implementations
{
    public class EmployeeCService : IEmployeeCService
    {
        private readonly IOrganizationUnitRepository _orgUnitRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeRepository _employeeRepository;
        private static readonly ILog log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType);
        private readonly IConfiguration _configuration;

        public EmployeeCService(
            IOrganizationUnitRepository orgUnitRepository,
            IEmployeeRepository employeeRepository,
            IUnitOfWork unitOfWork,
            IConfiguration configuration)
        {
            _orgUnitRepository = orgUnitRepository;
            _employeeRepository = employeeRepository;
            _unitOfWork = unitOfWork;
            _configuration = configuration;
        }

        public async Task<List<EmployeeCResponse>> GetAlDepartmentHeadAsync()
        {
            try
            {
                var result = await DatabaseSql.ExecuteProcToList<EmployeeCResponse>("sp_Get_DepartmentHead", new List<SqlParameter>(), _configuration);
                return result.ToList();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw new Exception(e.Message);
            }
        }
        public async Task<List<EmployeeCResponse>> GetAllEmployeesAsync()
        {
            var employees = await _employeeRepository.GetAllEmployeesAsync();
            var employeeResponses = employees.Select(e => new EmployeeCResponse
            {
                Id = e.Id,
                FullName = e.FullName,
                ShortName = e.ShortName,
                OrganizationUnitId = e.OrganizationUnitId,
                PositionType = e.PositionType
            }).Where(x => x.PositionType != 1).ToList();

            return employeeResponses;
        }


        public async Task<BaseSearchResponse<EmployeeResponse>> SearchEmployeeAsync(SearchEmployeeRequest request)
        {
            try
            {
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();

                IQueryable<EmployeeResponse> query = _unitOfWork.Employees
                                                               .AsQueryable()
                                                               .AsNoTracking()
                                                               .Where(x => (string.IsNullOrEmpty(request.keyword) ||
                                                                   //  x.RankName.Contains(request.keyword) ||
                                                                     x.FullName.Contains(request.keyword)) &&
                                                                    x.PositionType != null &&
                                                                    (x.PositionType == 1 || x.PositionType == 2))
                                                               .Select(s => new EmployeeResponse()
                                                               {
                                                                   Id = s.Id,
                                                                   FirstName = s.FirstName,
                                                                   LastName = s.LastName,
                                                                   Classify = s.Classify,
                                                                   OrganizationUnitId = s.OrganizationUnitId,
                                                                   OrganizationUnitName = null,
                                                                   EmployeeCode = s.EmployeeCode,
                                                                   Fullname = s.FullName,
                                                                   RankId = s.RankId,
                                                                   Gender = s.Gender,
                                                                   Gender_Name = s.Gender == 1 ? "Nam" : "Nữ",
                                                                   AcademicRankId = s.AcademicRankId,
                                                                   YearOfAcademicRank = s.YearOfAcademicRank,
                                                                   DegreeId = s.DegreeId,
                                                                   YearOfDegree = s.YearOfDegree,
                                                                   PositionId = s.PositionId,
                                                                   //    PositionName = s.PositionName,
                                                                   PositionType = s.PositionType,
                                                                   PartyPositionId = s.PartyPositionId,
                                                                   BirthDay = s.BirthDay,
                                                                   Owned = s.Owned,
                                                                   Active = s.Active,
                                                                   ActiveAccount = s.ActiveAccount,
                                                                   IsAdministrator = s.IsAdministrator,
                                                                   //   RankName = s.RankName,
                                                               });


                var resultQuery = query.AsEnumerable() // Chuyển sang IEnumerable để xử lý trong bộ nhớ
                    .Select(emp =>
                    {
                        emp.OrganizationUnitName = orgDict.TryGetValue(emp.OrganizationUnitId?.ToString().Trim() ?? "", out var name) ? name : "";
                        return emp;
                    })
                    .AsQueryable();

                return await BaseSearchResponse<EmployeeResponse>.GetResponse(resultQuery, request);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }

        public async Task<List<EmployeeResponse>> GetEmployeeById(Guid Id)
        {
            try
            {
                var org = await _unitOfWork.Organizations.GetAllOrganiztionUnitAsync();
                var orgDict = org?.ToDictionary(e => e.Id.ToString().Trim(), e => e.Name) ?? new Dictionary<string, string>();

                var Emm = await _unitOfWork.Employees
               .AsQueryable()
               .AsNoTracking()
               .Where(s => s.Id == Id)
               .ToListAsync();

                var Employee = Emm.Select(s => new EmployeeResponse
                {
                    Id = s.Id,
                    OrganizationUnitId = s.OrganizationUnitId,
                    Classify = s.Classify,
                    OrganizationUnitName = "",
                    EmployeeCode = s.EmployeeCode,
                    FirstName = s.FirstName,
                    LastName = s.LastName,
                    Fullname = s.FullName,
                    RankId = s.RankId,
                   // RankName = s.RankName,
                    Gender = s.Gender,
                    Gender_Name = s.Gender == 1 ? "Nam" : "Nữ",
                    AcademicRankId = s.AcademicRankId,
                    YearOfAcademicRank = s.YearOfAcademicRank,
                    DegreeId = s.DegreeId,
                    //  Degree_Name = "",
                    YearOfDegree = s.YearOfDegree,
                    PositionId = s.PositionId,
                    // PositionName = "",
                    PositionType = s.PositionType,
                    PartyPositionId = s.PartyPositionId,
                    BirthDay = s.BirthDay,
                    Owned = s.Owned,
                    Active = s.Active,
                    ActiveAccount = s.ActiveAccount,
                    IsAdministrator = s.IsAdministrator,
                    //  RankName = s.RankName
                    LongFullName = s.LongFullName,
                    BirthPlace = s.BirthPlace,
                    HomeLand = s.HomeLand,
                    NativeAddress = s.NativeAddress,
                    Tel = s.Tel,
                    HomeTel = s.HomeTel,
                    Mobile = s.Mobile,
                    Fax = s.Fax,
                    Email = s.Email,
                    OfficeAddress = s.OfficeAddress,
                    HomeAddress = s.HomeAddress,
                    Website = s.Website,
                    Description = s.Description,
                    IDNumber = s.IDNumber,
                    IssuedBy = s.IssuedBy,
                    DateBy = s.DateBy,
                    AccountNumber = s.AccountNumber,
                    Bank = s.Bank,
                    Avatar = s.Avatar,
                    SortOrder = s.SortOrder,
                    CreatedDate = s.CreatedDate,
                    ModifiedDate = s.ModifiedDate,
                    IPAddress = s.IPAddress,
                    ModifiedBy = s.ModifiedBy,
                    CreatedBy = s.CreatedBy
                }).ToList();


                var res = Employee.AsEnumerable() // Chuyển sang IEnumerable để xử lý trong bộ nhớ
                    .Select(emp =>
                    {
                        emp.OrganizationUnitName = orgDict.TryGetValue(emp.OrganizationUnitId?.ToString().Trim() ?? "", out var name) ? name : "";
                        return emp;
                    })
                    .AsQueryable().ToList();
                return res;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateSoftwaresAsync: " + ex.ToString());
                throw new NotFoundException(MessageErrorConstant.UNKNOWN_ERROR + ex.ToString());
            }
        }


        public async Task<EmployeeResponse> CreateEmployeeAsync(CreateEmployeeRequest request)
        {
            try
            {
                var entity = CreateEmployeeRequest.Create(request);

                await _unitOfWork.Employees.AddAsync(entity);
                await _unitOfWork.CommitChangesAsync();
                return EmployeeResponse.Create(entity);
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại CreateDepartmentAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> UpdateEmployeeAsync(UpdateEmployeeRequest request)
        {
            try
            {
                var findRecord = await _unitOfWork.Employees.AsQueryable()
                                                        .AsNoTracking()
                                                        .FirstOrDefaultAsync(x => x.Id == request.Id);

                if (findRecord == null) throw new NotFoundException(MessageErrorConstant.NOT_FOUND);

                var updateRecord = UpdateEmployeeRequest.Create(request);

                await _unitOfWork.Employees.UpdateAsync(request.Id, updateRecord);
                await _unitOfWork.CommitChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại UpdateAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }

        public async Task<bool> DeleteEmployeeAsync(DeleteEmployeeRequest request)
        {
            try
            {
                var record = await _unitOfWork.Employees.AsQueryable().Where(records => request.Ids.Any(id => id == records.Id)).ToListAsync();

                _unitOfWork.Employees.RemoveRange(record);
                await _unitOfWork.CommitChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                log.Error("Lỗi tại DeleteAdvertisementAsync: " + ex.ToString());
                throw new NotFoundException("UNKNOWN_ERROR" + ex.ToString());
            }
        }
    }
}
