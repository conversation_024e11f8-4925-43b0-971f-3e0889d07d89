namespace Application.Infrastructure.Models.Response;

public class OrganizationUnitTreeResponse
{
    public int Id { get; set; }
    public int? ParentId { get; set; }
    public bool IsRoot { get; set; }
    public string? OrganizationUnitCode { get; set; }
    public string? OrganizationUnitName { get; set; }
    public string? ShortOrganizationUnitName { get; set; }
    public string? FullOrganizationUnitName { get; set; }
    public string? ParentCode { get; set; }
    public int? Classify { get; set; }
    public int? ClassifyGroup { get; set; }
    public List<OrganizationUnitTreeResponse> Children { get; set; } = new List<OrganizationUnitTreeResponse>();
}