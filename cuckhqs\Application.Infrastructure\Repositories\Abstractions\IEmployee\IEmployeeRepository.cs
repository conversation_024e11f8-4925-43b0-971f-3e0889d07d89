﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Abstractions.IEmployee
{
    public interface IEmployeeRepository : IRepository<EmployeeCEntity, Guid>
    {
        Task<List<EmployeeCEntity>> GetEmployeesByOrgUnitIdAsync(int orgUnitId);
        Task<List<EmployeeCEntity>> GetAllEmployeesAsync();
    }
}
