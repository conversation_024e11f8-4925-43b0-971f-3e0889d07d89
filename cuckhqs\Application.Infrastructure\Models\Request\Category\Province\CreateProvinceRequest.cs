﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.Province
{
    public class CreateProvinceRequest
    {
        public string? ProvinceCode { get; set; }
        public string? ProvinceName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public static Expression<Func<CreateProvinceRequest, ProvinceEntity>> Expression
        {
            get
            {
                return entity => new ProvinceEntity
                {
                    ProvinceCode = entity.ProvinceCode,
                    ProvinceName = entity.ProvinceName,
                    Description = entity.Description,
                    Active = entity.Active,
                };
            }
        }

        public static ProvinceEntity Create(CreateProvinceRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
