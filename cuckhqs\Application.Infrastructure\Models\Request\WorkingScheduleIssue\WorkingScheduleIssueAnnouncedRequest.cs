﻿using Application.Infrastructure.Commons;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.WorkingSchedule
{
    public class WorkingScheduleIssueAnnouncedRequest
    {
        public int? OrganizationUnitId { get; set; }
        public int? Year { get; set; }
        public int Week { get; set; }
        public int? Number { get; set; }
        public string? Sign { get; set; }
        public string? Date { get; set; }
        public int? Announced { get; set; }
        public string? Place { get; set; }
        public string? Command { get; set; }
        public string? PersonSigningOther { get; set; }
        public Guid? PersonSigning { get; set; }
        public string? UnitPositionSigning { get; set; }
        public string? ReceiverIDs { get; set; }
    }
}
