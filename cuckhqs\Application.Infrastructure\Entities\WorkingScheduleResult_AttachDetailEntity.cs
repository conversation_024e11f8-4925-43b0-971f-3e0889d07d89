﻿using Application.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Entities
{
    public class WorkingScheduleResult_AttachDetailEntity : BaseEntity<int>
    {
        public int? WorkingScheduleResultId {  get; set; }
        public string? Contents {  get; set; }
        public string? FileName {  get; set; }
        public string? FileType {  get; set; }
        public double? FileSize {  get; set; }
        public bool? Inactive {  get; set; }
        public int? SortOrder {  get; set; }
        public DateTime? CreatedDate {  get; set; }
        public DateTime? ModifiedDate {  get; set; }
        public string? IPAddress {  get; set; }
        public string? ModifiedBy {  get; set; }
        public string? CreatedBy {  get; set; }
        [ForeignKey("WorkingScheduleResultId")]
        public virtual WorkingScheduleResultEntity WorkingScheduleResult { get; set; }
    }
}
