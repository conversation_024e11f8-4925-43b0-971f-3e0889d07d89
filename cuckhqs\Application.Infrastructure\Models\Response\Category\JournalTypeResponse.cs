﻿using Application.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Response.Category
{
    public class JournalTypeResponse
    {
        public int? Id { get; set; }
        public string? JournalTypeCode { get; set; }
        public string? JournalTypeName { get; set; }
        public string? Description { get; set; }
        public bool? Active { get; set; }
        public int? SortOrder { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? IPAddress { get; set; }
        public string? ModifiedBy { get; set; }
        public string? CreatedBy { get; set; }

        public static Expression<Func<JournalTypeEntity, JournalTypeResponse>> Expression
        {
            get
            {
                return entity => new JournalTypeResponse()
                {
                    Id = entity.Id,
                    JournalTypeCode = entity.JournalTypeCode,
                    JournalTypeName = entity.JournalTypeName,
                    Description = entity.Description,
                    Active = true,
                };
            }
        }

        public static JournalTypeResponse Create(JournalTypeEntity response)
        {
            return Expression.Compile().Invoke(response);
        }
    }
}
