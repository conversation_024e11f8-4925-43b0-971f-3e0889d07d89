{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-panel.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * Panel is a container with the optional content toggle feature.\n * @group Components\n */\nconst _c0 = [\"*\", [[\"p-header\"]], [[\"p-footer\"]]];\nconst _c1 = [\"*\", \"p-header\", \"p-footer\"];\nconst _c2 = (a0, a1) => ({\n  \"p-panel p-component\": true,\n  \"p-panel-toggleable\": a0,\n  \"p-panel-expanded\": a1\n});\nconst _c3 = a0 => ({\n  transitionParams: a0,\n  height: \"0\",\n  opacity: \"0\"\n});\nconst _c4 = a0 => ({\n  value: \"hidden\",\n  params: a0\n});\nconst _c5 = a0 => ({\n  transitionParams: a0,\n  height: \"*\",\n  opacity: \"1\"\n});\nconst _c6 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c7 = (a0, a1, a2) => ({\n  \"p-panel-icons-start\": a0,\n  \"p-panel-icons-end\": a1,\n  \"p-panel-icons-center\": a2\n});\nconst _c8 = a0 => ({\n  $implicit: a0\n});\nfunction Panel_div_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_header\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.header);\n  }\n}\nfunction Panel_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_1_5_ng_template_0_Template(rf, ctx) {}\nfunction Panel_div_1_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Panel_div_1_5_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r1.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_MinusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"MinusIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_1_span_1_Template, 1, 3, \"span\", 14)(2, Panel_div_1_button_6_ng_container_1_ng_container_1_MinusIcon_2_Template, 1, 1, \"MinusIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.expandIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.expandIcon);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r1.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_PlusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"PlusIcon\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.iconClass);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_2_span_1_Template, 1, 3, \"span\", 14)(2, Panel_div_1_button_6_ng_container_1_ng_container_2_PlusIcon_2_Template, 1, 1, \"PlusIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.collapseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.collapseIcon);\n  }\n}\nfunction Panel_div_1_button_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 12)(2, Panel_div_1_button_6_ng_container_1_ng_container_2_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.collapsed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.collapsed);\n  }\n}\nfunction Panel_div_1_button_6_2_ng_template_0_Template(rf, ctx) {}\nfunction Panel_div_1_button_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Panel_div_1_button_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Panel_div_1_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function Panel_div_1_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIconClick($event));\n    })(\"keydown\", function Panel_div_1_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onKeyDown($event));\n    });\n    i0.ɵɵtemplate(1, Panel_div_1_button_6_ng_container_1_Template, 3, 2, \"ng-container\", 12)(2, Panel_div_1_button_6_2_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_header\")(\"aria-label\", ctx_r1.buttonAriaLabel)(\"aria-controls\", ctx_r1.id + \"_content\")(\"aria-expanded\", !ctx_r1.collapsed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.headerIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c8, ctx_r1.collapsed));\n  }\n}\nfunction Panel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function Panel_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onHeaderClick($event));\n    });\n    i0.ɵɵtemplate(1, Panel_div_1_span_1_Template, 2, 2, \"span\", 7);\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵtemplate(3, Panel_div_1_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementStart(4, \"div\", 8);\n    i0.ɵɵtemplate(5, Panel_div_1_5_Template, 1, 0, null, 4)(6, Panel_div_1_button_6_Template, 3, 9, \"button\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-titlebar\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.header);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c7, ctx_r1.iconPos === \"start\", ctx_r1.iconPos === \"end\", ctx_r1.iconPos === \"center\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.iconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.toggleable);\n  }\n}\nfunction Panel_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Panel_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵtemplate(2, Panel_div_6_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nclass Panel {\n  el;\n  cd;\n  /**\n   * Defines if content of panel can be expanded and collapsed.\n   * @group Props\n   */\n  toggleable;\n  /**\n   * Header text of the panel.\n   * @group Props\n   */\n  header;\n  /**\n   * Defines the initial state of panel content, supports one or two-way binding as well.\n   * @group Props\n   */\n  collapsed;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Position of the icons.\n   * @group Props\n   */\n  iconPos = 'end';\n  /**\n   * Expand icon of the toggle button.\n   * @group Props\n   * @deprecated since v15.4.2, use `headericons` template instead.\n   */\n  expandIcon;\n  /**\n   * Collapse icon of the toggle button.\n   * @group Props\n   * @deprecated since v15.4.2, use `headericons` template instead.\n   */\n  collapseIcon;\n  /**\n   * Specifies if header of panel cannot be displayed.\n   * @group Props\n   * @deprecated since v15.4.2, use `headericons` template instead.\n   */\n  showHeader = true;\n  /**\n   * Specifies the toggler element to toggle the panel content.\n   * @group Props\n   */\n  toggler = 'icon';\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Emitted when the collapsed changes.\n   * @param {boolean} value - New Value.\n   * @group Emits\n   */\n  collapsedChange = new EventEmitter();\n  /**\n   * Callback to invoke before panel toggle.\n   * @param {PanelBeforeToggleEvent} event - Custom panel toggle event\n   * @group Emits\n   */\n  onBeforeToggle = new EventEmitter();\n  /**\n   * Callback to invoke after panel toggle.\n   * @param {PanelAfterToggleEvent} event - Custom panel toggle event\n   * @group Emits\n   */\n  onAfterToggle = new EventEmitter();\n  footerFacet;\n  templates;\n  iconTemplate;\n  animating;\n  headerTemplate;\n  contentTemplate;\n  footerTemplate;\n  headerIconTemplate;\n  id = UniqueComponentId();\n  get buttonAriaLabel() {\n    return this.header;\n  }\n  constructor(el, cd) {\n    this.el = el;\n    this.cd = cd;\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n        case 'icons':\n          this.iconTemplate = item.template;\n          break;\n        case 'headericons':\n          this.headerIconTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  onHeaderClick(event) {\n    if (this.toggler === 'header') {\n      this.toggle(event);\n    }\n  }\n  onIconClick(event) {\n    if (this.toggler === 'icon') {\n      this.toggle(event);\n    }\n  }\n  toggle(event) {\n    if (this.animating) {\n      return false;\n    }\n    this.animating = true;\n    this.onBeforeToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n    if (this.toggleable) {\n      if (this.collapsed) this.expand();else this.collapse();\n    }\n    this.cd.markForCheck();\n    event.preventDefault();\n  }\n  expand() {\n    this.collapsed = false;\n    this.collapsedChange.emit(this.collapsed);\n  }\n  collapse() {\n    this.collapsed = true;\n    this.collapsedChange.emit(this.collapsed);\n  }\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n  onKeyDown(event) {\n    if (event.code === 'Enter' || event.code === 'Space') {\n      this.toggle(event);\n      event.preventDefault();\n    }\n  }\n  onToggleDone(event) {\n    this.animating = false;\n    this.onAfterToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n  }\n  static ɵfac = function Panel_Factory(t) {\n    return new (t || Panel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Panel,\n    selectors: [[\"p-panel\"]],\n    contentQueries: function Panel_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      toggleable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"toggleable\", \"toggleable\", booleanAttribute],\n      header: \"header\",\n      collapsed: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"collapsed\", \"collapsed\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      iconPos: \"iconPos\",\n      expandIcon: \"expandIcon\",\n      collapseIcon: \"collapseIcon\",\n      showHeader: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showHeader\", \"showHeader\", booleanAttribute],\n      toggler: \"toggler\",\n      transitionOptions: \"transitionOptions\"\n    },\n    outputs: {\n      collapsedChange: \"collapsedChange\",\n      onBeforeToggle: \"onBeforeToggle\",\n      onAfterToggle: \"onAfterToggle\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c1,\n    decls: 7,\n    vars: 25,\n    consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-panel-header\", 3, \"click\", 4, \"ngIf\"], [\"role\", \"region\", 1, \"p-toggleable-content\", 3, \"id\"], [1, \"p-panel-content\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-panel-footer\", 4, \"ngIf\"], [1, \"p-panel-header\", 3, \"click\"], [\"class\", \"p-panel-title\", 4, \"ngIf\"], [1, \"p-panel-icons\", 3, \"ngClass\"], [\"pRipple\", \"\", \"type\", \"button\", \"role\", \"button\", \"class\", \"p-panel-header-icon p-panel-toggler p-link\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [1, \"p-panel-title\"], [\"pRipple\", \"\", \"type\", \"button\", \"role\", \"button\", 1, \"p-panel-header-icon\", \"p-panel-toggler\", \"p-link\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\"], [1, \"p-panel-footer\"]],\n    template: function Panel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, Panel_div_1_Template, 7, 10, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵlistener(\"@panelContent.done\", function Panel_Template_div_animation_panelContent_done_2_listener($event) {\n          return ctx.onToggleDone($event);\n        });\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵprojection(4);\n        i0.ɵɵtemplate(5, Panel_ng_container_5_Template, 1, 0, \"ng-container\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, Panel_div_6_Template, 3, 1, \"div\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c2, ctx.toggleable, !ctx.collapsed && ctx.toggleable))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"id\", ctx.id)(\"data-pc-name\", \"panel\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx.id + \"_content\")(\"@panelContent\", ctx.collapsed ? i0.ɵɵpureFunction1(19, _c4, i0.ɵɵpureFunction1(17, _c3, ctx.animating ? ctx.transitionOptions : \"0ms\")) : i0.ɵɵpureFunction1(23, _c6, i0.ɵɵpureFunction1(21, _c5, ctx.animating ? ctx.transitionOptions : \"0ms\")));\n        i0.ɵɵattribute(\"aria-labelledby\", ctx.id + \"_header\")(\"aria-hidden\", ctx.collapsed)(\"tabindex\", ctx.collapsed ? \"-1\" : undefined);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, PlusIcon, MinusIcon],\n    styles: [\"@layer primeng{.p-panel-header{display:flex;align-items:center}.p-panel-title{line-height:1;order:1}.p-panel-header-icon{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;text-decoration:none;overflow:hidden;position:relative}.p-panel-toggleable.p-panel-expanded>.p-toggleable-content:not(.ng-animating){overflow:visible}.p-panel-toggleable .p-toggleable-content{overflow:hidden}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('panelContent', [state('hidden', style({\n        height: '0'\n      })), state('void', style({\n        height: '{{height}}'\n      }), {\n        params: {\n          height: '0'\n        }\n      }), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => hidden', animate('{{transitionParams}}')), transition('void => visible', animate('{{transitionParams}}'))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Panel, [{\n    type: Component,\n    args: [{\n      selector: 'p-panel',\n      template: `\n        <div [attr.id]=\"id\" [attr.data-pc-name]=\"'panel'\" [ngClass]=\"{ 'p-panel p-component': true, 'p-panel-toggleable': toggleable, 'p-panel-expanded': !collapsed && toggleable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-panel-header\" *ngIf=\"showHeader\" (click)=\"onHeaderClick($event)\" [attr.id]=\"id + '-titlebar'\">\n                <span class=\"p-panel-title\" *ngIf=\"header\" [attr.id]=\"id + '_header'\">{{ header }}</span>\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <div class=\"p-panel-icons\" [ngClass]=\"{ 'p-panel-icons-start': iconPos === 'start', 'p-panel-icons-end': iconPos === 'end', 'p-panel-icons-center': iconPos === 'center' }\">\n                    <ng-template *ngTemplateOutlet=\"iconTemplate\"></ng-template>\n                    <button\n                        *ngIf=\"toggleable\"\n                        [attr.id]=\"id + '_header'\"\n                        pRipple\n                        type=\"button\"\n                        role=\"button\"\n                        class=\"p-panel-header-icon p-panel-toggler p-link\"\n                        [attr.aria-label]=\"buttonAriaLabel\"\n                        [attr.aria-controls]=\"id + '_content'\"\n                        [attr.aria-expanded]=\"!collapsed\"\n                        (click)=\"onIconClick($event)\"\n                        (keydown)=\"onKeyDown($event)\"\n                    >\n                        <ng-container *ngIf=\"!headerIconTemplate\">\n                            <ng-container *ngIf=\"!collapsed\">\n                                <span *ngIf=\"expandIcon\" [class]=\"expandIcon\" [ngClass]=\"iconClass\"></span>\n                                <MinusIcon *ngIf=\"!expandIcon\" [styleClass]=\"iconClass\" />\n                            </ng-container>\n\n                            <ng-container *ngIf=\"collapsed\">\n                                <span *ngIf=\"collapseIcon\" [class]=\"collapseIcon\" [ngClass]=\"iconClass\"></span>\n                                <PlusIcon *ngIf=\"!collapseIcon\" [styleClass]=\"iconClass\" />\n                            </ng-container>\n                        </ng-container>\n\n                        <ng-template *ngTemplateOutlet=\"headerIconTemplate; context: { $implicit: collapsed }\"></ng-template>\n                    </button>\n                </div>\n            </div>\n            <div\n                class=\"p-toggleable-content\"\n                [id]=\"id + '_content'\"\n                role=\"region\"\n                [attr.aria-labelledby]=\"id + '_header'\"\n                [attr.aria-hidden]=\"collapsed\"\n                [attr.tabindex]=\"collapsed ? '-1' : undefined\"\n                [@panelContent]=\"\n                    collapsed\n                        ? { value: 'hidden', params: { transitionParams: animating ? transitionOptions : '0ms', height: '0', opacity: '0' } }\n                        : { value: 'visible', params: { transitionParams: animating ? transitionOptions : '0ms', height: '*', opacity: '1' } }\n                \"\n                (@panelContent.done)=\"onToggleDone($event)\"\n            >\n                <div class=\"p-panel-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n\n                <div class=\"p-panel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                    <ng-content select=\"p-footer\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('panelContent', [state('hidden', style({\n        height: '0'\n      })), state('void', style({\n        height: '{{height}}'\n      }), {\n        params: {\n          height: '0'\n        }\n      }), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => hidden', animate('{{transitionParams}}')), transition('void => visible', animate('{{transitionParams}}'))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-panel-header{display:flex;align-items:center}.p-panel-title{line-height:1;order:1}.p-panel-header-icon{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;text-decoration:none;overflow:hidden;position:relative}.p-panel-toggleable.p-panel-expanded>.p-toggleable-content:not(.ng-animating){overflow:visible}.p-panel-toggleable .p-toggleable-content{overflow:hidden}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    toggleable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    header: [{\n      type: Input\n    }],\n    collapsed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    iconPos: [{\n      type: Input\n    }],\n    expandIcon: [{\n      type: Input\n    }],\n    collapseIcon: [{\n      type: Input\n    }],\n    showHeader: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    toggler: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    collapsedChange: [{\n      type: Output\n    }],\n    onBeforeToggle: [{\n      type: Output\n    }],\n    onAfterToggle: [{\n      type: Output\n    }],\n    footerFacet: [{\n      type: ContentChild,\n      args: [Footer]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass PanelModule {\n  static ɵfac = function PanelModule_Factory(t) {\n    return new (t || PanelModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PanelModule,\n    declarations: [Panel],\n    imports: [CommonModule, SharedModule, RippleModule, PlusIcon, MinusIcon],\n    exports: [Panel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, RippleModule, PlusIcon, MinusIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule, PlusIcon, MinusIcon],\n      exports: [Panel, SharedModule],\n      declarations: [Panel]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Panel, PanelModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;AAChD,IAAM,MAAM,CAAC,KAAK,YAAY,UAAU;AACxC,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AACtB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,SAAS;AACX;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,SAAS;AACX;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,wBAAwB;AAC1B;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,MAAM,OAAO,KAAK,SAAS;AAC1C,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AAAC;AACxD,SAAS,uBAAuB,IAAI,KAAK;AACvC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,aAAa;AAAA,EAC5E;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,WAAW,OAAO,SAAS;AAAA,EAC3C;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,EAAE;AAAA,EACjC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,aAAa,EAAE;AACxM,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,UAAU;AAAA,EAC1C;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,YAAY;AACjC,IAAG,WAAW,WAAW,OAAO,SAAS;AAAA,EAC3C;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,wEAAwE,GAAG,GAAG,YAAY,EAAE;AACtM,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,YAAY;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,YAAY;AAAA,EAC5C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,gBAAgB,EAAE;AAChM,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS;AAAA,EACxC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAAC;AACjE,SAAS,gCAAgC,IAAI,KAAK;AAChD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,aAAa;AAAA,EACrF;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,sDAAsD,QAAQ;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,WAAW,SAAS,wDAAwD,QAAQ;AACrF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,iCAAiC,GAAG,GAAG,MAAM,EAAE;AAC3I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,MAAM,OAAO,KAAK,SAAS,EAAE,cAAc,OAAO,eAAe,EAAE,iBAAiB,OAAO,KAAK,UAAU,EAAE,iBAAiB,CAAC,OAAO,SAAS;AAC7J,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,kBAAkB;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,SAAS,CAAC;AAAA,EACtI;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,0CAA0C,QAAQ;AAChF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,6BAA6B,GAAG,GAAG,QAAQ,CAAC;AAC7D,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,gBAAgB,CAAC;AAC7E,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wBAAwB,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,+BAA+B,GAAG,GAAG,UAAU,CAAC;AAC3G,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,MAAM,OAAO,KAAK,WAAW;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,cAAc;AACvD,IAAG,UAAU;AACb,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,OAAO,YAAY,SAAS,OAAO,YAAY,OAAO,OAAO,YAAY,QAAQ,CAAC;AACtI,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AAAA,EACzC;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,qBAAqB,IAAI,KAAK;AACrC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,gBAAgB,CAAC;AAC7E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,cAAc;AAAA,EACzD;AACF;AACA,IAAM,QAAN,MAAM,OAAM;AAAA,EACV;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlC,gBAAgB,IAAI,aAAa;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK,kBAAkB;AAAA,EACvB,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,IAAI,IAAI;AAClB,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,qBAAqB,KAAK;AAC/B;AAAA,QACF;AACE,eAAK,kBAAkB,KAAK;AAC5B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,KAAK,YAAY,UAAU;AAC7B,WAAK,OAAO,KAAK;AAAA,IACnB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,YAAY,QAAQ;AAC3B,WAAK,OAAO,KAAK;AAAA,IACnB;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,SAAK,YAAY;AACjB,SAAK,eAAe,KAAK;AAAA,MACvB,eAAe;AAAA,MACf,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,UAAW,MAAK,OAAO;AAAA,UAAO,MAAK,SAAS;AAAA,IACvD;AACA,SAAK,GAAG,aAAa;AACrB,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,WAAW;AACT,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,GAAG,cAAc,SAAS,CAAC;AAAA,EACzC;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,SAAS,WAAW,MAAM,SAAS,SAAS;AACpD,WAAK,OAAO,KAAK;AACjB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK;AAAA,MACtB,eAAe;AAAA,MACf,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,cAAc,GAAG;AACtC,WAAO,KAAK,KAAK,QAAU,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EACzG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,CAAC;AAAA,IACvB,gBAAgB,SAAS,qBAAqB,IAAI,KAAK,UAAU;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,QAAQ,CAAC;AACrC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,WAAW;AAAA,IAC1B,QAAQ;AAAA,MACN,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,QAAQ;AAAA,MACR,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,gBAAgB;AAAA,MAClG,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY,CAAI,WAAa,4BAA4B,cAAc,cAAc,gBAAgB;AAAA,MACrG,SAAS;AAAA,MACT,mBAAmB;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB;AAAA,IACA,UAAU,CAAI,wBAAwB;AAAA,IACtC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,SAAS,kBAAkB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,GAAG,wBAAwB,GAAG,IAAI,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,OAAO,GAAG,CAAC,SAAS,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,iBAAiB,GAAG,SAAS,GAAG,CAAC,WAAW,IAAI,QAAQ,UAAU,QAAQ,UAAU,SAAS,8CAA8C,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,WAAW,IAAI,QAAQ,UAAU,QAAQ,UAAU,GAAG,uBAAuB,mBAAmB,UAAU,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,gBAAgB,CAAC;AAAA,IACzzB,UAAU,SAAS,eAAe,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,sBAAsB,GAAG,IAAI,OAAO,CAAC;AACtD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,sBAAsB,SAAS,0DAA0D,QAAQ;AAC7G,iBAAO,IAAI,aAAa,MAAM;AAAA,QAChC,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,WAAW,GAAG,+BAA+B,GAAG,GAAG,gBAAgB,CAAC;AACvE,QAAG,aAAa;AAChB,QAAG,WAAW,GAAG,sBAAsB,GAAG,GAAG,OAAO,CAAC;AACrD,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,YAAY,CAAC,IAAI,aAAa,IAAI,UAAU,CAAC,EAAE,WAAW,IAAI,KAAK;AAC5H,QAAG,YAAY,MAAM,IAAI,EAAE,EAAE,gBAAgB,OAAO;AACpD,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,UAAU;AACpC,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,KAAK,UAAU,EAAE,iBAAiB,IAAI,YAAe,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,IAAI,YAAY,IAAI,oBAAoB,KAAK,CAAC,IAAO,gBAAgB,IAAI,KAAQ,gBAAgB,IAAI,KAAK,IAAI,YAAY,IAAI,oBAAoB,KAAK,CAAC,CAAC;AAC3R,QAAG,YAAY,mBAAmB,IAAI,KAAK,SAAS,EAAE,eAAe,IAAI,SAAS,EAAE,YAAY,IAAI,YAAY,OAAO,MAAS;AAChI,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,eAAe,IAAI,cAAc;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,cAAc,MAAM,CAAI,SAAY,MAAS,kBAAqB,SAAY,QAAQ,UAAU,SAAS;AAAA,IACzG,QAAQ,CAAC,2ZAA2Z;AAAA,IACpa,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,gBAAgB,CAAC,MAAM,UAAU,MAAM;AAAA,QACzD,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,QAAQ,MAAM;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,kBAAkB,QAAQ,sBAAsB,CAAC,GAAG,WAAW,mBAAmB,QAAQ,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAAA,IAC3M;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,OAAO,CAAC;AAAA,IAC9E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+DV,YAAY,CAAC,QAAQ,gBAAgB,CAAC,MAAM,UAAU,MAAM;AAAA,QAC1D,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,MAAM,QAAQ,MAAM;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC,GAAG;AAAA,QACF,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,QACzB,QAAQ;AAAA,MACV,CAAC,CAAC,GAAG,WAAW,sBAAsB,CAAC,QAAQ,sBAAsB,CAAC,CAAC,GAAG,WAAW,kBAAkB,QAAQ,sBAAsB,CAAC,GAAG,WAAW,mBAAmB,QAAQ,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAAA,MACzM,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,2ZAA2Z;AAAA,IACta,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,OAAO,SAAS,oBAAoB,GAAG;AAC5C,WAAO,KAAK,KAAK,cAAa;AAAA,EAChC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,KAAK;AAAA,IACpB,SAAS,CAAC,cAAc,cAAc,cAAc,UAAU,SAAS;AAAA,IACvE,SAAS,CAAC,OAAO,YAAY;AAAA,EAC/B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,cAAc,UAAU,WAAW,YAAY;AAAA,EACvF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,cAAc,cAAc,UAAU,SAAS;AAAA,MACvE,SAAS,CAAC,OAAO,YAAY;AAAA,MAC7B,cAAc,CAAC,KAAK;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}