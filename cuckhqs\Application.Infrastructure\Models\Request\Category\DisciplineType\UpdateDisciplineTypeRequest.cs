﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Models.Request.Category.DecisionLevel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Models.Request.Category.DisciplineType
{
    public class UpdateDisciplineTypeRequest : CreateDisciplineTypeRequest
    {
        public int Id { get; set; }
        public static Expression<Func<UpdateDisciplineTypeRequest, DisciplineTypeEntity>> Expression
        {
            get
            {
                return entity => new DisciplineTypeEntity
                {
                    Id = entity.Id,
                    DisciplineTypeCode = entity.DisciplineTypeCode,
                    DisciplineTypeName = entity.DisciplineTypeName,
                    Class = entity.Class,
                    Active = entity.Active
                };
            }
        }

        public static DisciplineTypeEntity Create(UpdateDisciplineTypeRequest request)
        {
            return Expression.Compile().Invoke(request);
        }
    }
}
