﻿using Application.Infrastructure.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Helpers
{
    public abstract class GenerateCodeTrigger
    {
        public static string GenrateCode(string entityName, dynamic entityData, string departmentCode)
        {
            return string.Empty;//$"{entityName.GetCodeOfEntity()}{DateTimeConstant.GetCurrentYear}{}";
        }
    }
}
