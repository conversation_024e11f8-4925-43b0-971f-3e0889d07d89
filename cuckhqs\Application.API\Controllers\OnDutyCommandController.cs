﻿using Application.Infrastructure.Models.Request.OnDutyCommand;
using Application.Infrastructure.Services.Abstractions;
using Application.Infrastructure.Services.Implementations;
using Azure.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Validation.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Application.API.Controllers
{
    [ApiController]
    [Authorize(AuthenticationSchemes = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    public class OnDutyCommandController : ControllerBase
    {
        private readonly IOnDutyCommandService _OnDutyCommandService;

        public OnDutyCommandController(IOnDutyCommandService OnDutyCommandService)
        {
            _OnDutyCommandService = OnDutyCommandService;
        }

        [HttpPost("Search")]
        public async Task<IActionResult> SearchOnDutyCommandAsync([FromBody] SearchOnDutyCommandRequest request)
        {
            try
            {
                var response = await _OnDutyCommandService.SearchOnDutyCommandAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateOnDutyCommandAsync([FromBody] CreateOnDutyCommandRequest request)
        {
            try
            {
                var response = await _OnDutyCommandService.CreateOnDutyCommandAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateOnDutyCommandAsync([FromBody] UpdateOnDutyCommandRequest request)
        {
            try
            {
                var response = await _OnDutyCommandService.UpdateOnDutyCommandAsync(request);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
