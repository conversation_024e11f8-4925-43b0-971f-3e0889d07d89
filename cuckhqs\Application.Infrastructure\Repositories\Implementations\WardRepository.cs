﻿using Application.Infrastructure.Configurations;
using Application.Infrastructure.Entities;
using Application.Infrastructure.Repositories.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Repositories.Implementations
{
    public class WardRepository : GenericRepository<WardEntity, int>, IWardRepository
    {
        public AppDbContext Context { get; set; }

        public WardRepository(AppDbContext context) : base(context)
        {
        }

        protected override void Update(WardEntity requestObject, WardEntity targetObject)
        {
            targetObject.ProvinceId = requestObject.ProvinceId;
            targetObject.DistrictId = requestObject.DistrictId;
            targetObject.WardCode = requestObject.WardCode;
            targetObject.WardName = requestObject.WardName;
            targetObject.Active = requestObject.Active;
            targetObject.Description = requestObject.Description;
        }
    }
}
